========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:24:08.992Z
File: app_main_2025-08-31_20-24-08_001.log
========================================

[2025-09-01T01:24:09.290Z] [INFO] AIStudio application started successfully
[2025-09-01T01:24:09.290Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:24:09.327Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:24:10.370Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:24:28.971Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-24-28_001.log
[2025-09-01T01:24:28.971Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:24:57.384Z] [ERROR] Framepack video generation failed: Error: spawn C:\WINDOWS\system32\cmd.exe ENOENT
