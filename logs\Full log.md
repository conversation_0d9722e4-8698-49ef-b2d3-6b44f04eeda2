========================================
         AIStudio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\AIStudio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

✓ 2129 modules transformed.
../../dist/renderer/index.html                     0.46 kB │ gzip:   0.30 kB
../../dist/renderer/assets/index-DsKk4x6U.css     50.29 kB │ gzip:   8.07 kB
../../dist/renderer/assets/index-E2ArCN4p.js      11.52 kB │ gzip:   4.64 kB
../../dist/renderer/assets/index-BQVfvDey.js   1,556.51 kB │ gzip: 417.26 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 13.16s
Γ£ô Frontend build complete

========================================
    Starting AIStudio (Production)...
========================================


> ai-studio@1.0.0 start
> electron .


[RealtimeLogger] Started logging main to: app_main_2025-09-03_14-34-12_001.log
info: Logger initialized. {"service":"user-service","timestamp":"2025-09-03 14:34:12"}
info: App is ready. {"service":"user-service","timestamp":"2025-09-03 14:34:12"}
[main] AIStudio application started successfully
info: AISTUDIO_ROOT set to: N:\AIStudio {"service":"user-service","timestamp":"2025-09-03 14:34:12"}
info: Console window hidden at startup {"service":"user-service","timestamp":"2025-09-03 14:34:12"}
[Trellis Server] Checking for lingering Python processes...
[Trellis Server] No Python processes found
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Production mode - Loading file: N:\AIStudio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: ImageUpscaling {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: ImageEdit {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: Video Generation {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: HunyuanVideo {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Loaded pipeline: v13_hunyuan2-stableprojectorz {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Registering IPC handlers... {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
[main] ERROR: Unhandled Rejection at: [object Promise], reason: Error: Attempted to register a second handler for 'resolve-video-path'
Unhandled Rejection: Error: Attempted to register a second handler for 'resolve-video-path'
    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:101132)
    at registerIPCHandlers (N:\AIStudio\src\main\index.js:1711:9)
    at N:\AIStudio\src\main\index.js:175:3
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: ImageUpscaling {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: ImageEdit {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Skipping directory creation for system package pipeline: trellis-stable-projectorz-101 {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: Video Generation {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: HunyuanVideo {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Ensured pipeline directory exists: v13_hunyuan2-stableprojectorz {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Registering pipelines... {"service":"user-service","timestamp":"2025-09-03 14:34:13"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: Retrieved from store: huggingface-token = [37 chars] {"service":"user-service","timestamp":"2025-09-03 14:34:14"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"ImageGeneration","2":"ImageUpscaling","3":"ImageEdit","4":"trellis-stable-projectorz-101","5":"Video Generation","6":"HunyuanVideo","7":"v13_hunyuan2-stableprojectorz","service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loading 34 sample images {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 1/34: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 2/34: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 3/34: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 4/34: Green Flower Pot {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 5/34: Blue Couch {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 6/34: Little Girl {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 7/34: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 8/34: Lantern {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 9/34: Wooden Barrel {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 10/34: Cut Log Piece {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 11/34: White Chair {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 12/34: Greek Statue {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 13/34: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 14/34: Stylized Tree {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 15/34: Chinese Stone Lion {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 16/34: White Backpack {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 17/34: Potted Plant {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 18/34: Red Myan Pot {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 19/34: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 20/34: Golden Greek Pillar {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 21/34: Brown Pot {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 22/34: Greek Head {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 23/34: Christmas Light {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 24/34: Septor {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 25/34: Treasure {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 26/34: Light Pole {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 27/34: MJ {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 28/34: Japanese Shrine {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 29/34: Spaceship {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 30/34: Stone Ark {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 31/34: Dragon Baby {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 32/34: Red Bonzai {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 33/34: Stitch {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: Loaded sample 34/34: African Three Legged Black Pot {"service":"user-service","timestamp":"2025-09-03 14:34:25"}
info: [DEBUG] Found pipeline config.json files: {"0":"N:\\AIStudio\\pipelines\\3DPipelines\\gen3d\\trellis-stable-projectorz-101\\config.json","1":"N:\\AIStudio\\pipelines\\3DPipelines\\gen3d\\v13_hunyuan2-stableprojectorz\\config.json","10":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack\\config.json","11":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\config.json","12":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\hf_download\\hub\\models--hunyuanvideo-community--HunyuanVideo\\snapshots\\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\\text_encoder\\config.json","13":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\hf_download\\hub\\models--hunyuanvideo-community--HunyuanVideo\\snapshots\\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\\text_encoder_2\\config.json","14":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\hf_download\\hub\\models--hunyuanvideo-community--HunyuanVideo\\snapshots\\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\\vae\\config.json","15":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\hf_download\\hub\\models--lllyasviel--flux_redux_bfl\\snapshots\\45b801affc54ff2af4e5daf1b282e0921901db87\\image_encoder\\config.json","16":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\hf_download\\hub\\models--lllyasviel--FramePackI2V_HY\\snapshots\\86cef4396041b6002c957852daac4c91aaa47c79\\config.json","17":"N:\\AIStudio\\pipelines\\VideoPipelines\\framepack_cu126_torch26\\system\\python\\Lib\\site-packages\\mesh_graphormer\\modeling\\bert\\bert-base-uncased\\config.json","2":"N:\\AIStudio\\pipelines\\Core\\config.json","3":"N:\\AIStudio\\pipelines\\ImageEdit\\config.json","4":"N:\\AIStudio\\pipelines\\ImageEdit\\models\\models--Qwen--Qwen-Image-Edit\\snapshots\\0b71959872ea3bf4d106c578b7c480ebb133dba7\\text_encoder\\config.json","5":"N:\\AIStudio\\pipelines\\ImageEdit\\models\\models--Qwen--Qwen-Image-Edit\\snapshots\\0b71959872ea3bf4d106c578b7c480ebb133dba7\\transformer\\config.json","6":"N:\\AIStudio\\pipelines\\ImageEdit\\models\\models--Qwen--Qwen-Image-Edit\\snapshots\\0b71959872ea3bf4d106c578b7c480ebb133dba7\\vae\\config.json","7":"N:\\AIStudio\\pipelines\\ImageGeneration\\config.json","8":"N:\\AIStudio\\pipelines\\ImageUpscaling\\config.json","9":"N:\\AIStudio\\pipelines\\VideoPipelines\\config.json","service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: trellis-stable-projectorz-101 (from N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: v13_hunyuan2-stableprojectorz (from N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: Core (from N:\AIStudio\pipelines\Core\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: ImageEdit (from N:\AIStudio\pipelines\ImageEdit\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: text_encoder (from N:\AIStudio\pipelines\ImageEdit\models\models--Qwen--Qwen-Image-Edit\snapshots\0b71959872ea3bf4d106c578b7c480ebb133dba7\text_encoder\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: transformer (from N:\AIStudio\pipelines\ImageEdit\models\models--Qwen--Qwen-Image-Edit\snapshots\0b71959872ea3bf4d106c578b7c480ebb133dba7\transformer\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: vae (from N:\AIStudio\pipelines\ImageEdit\models\models--Qwen--Qwen-Image-Edit\snapshots\0b71959872ea3bf4d106c578b7c480ebb133dba7\vae\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: ImageGeneration (from N:\AIStudio\pipelines\ImageGeneration\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: ImageUpscaling (from N:\AIStudio\pipelines\ImageUpscaling\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: VideoPipelines (from N:\AIStudio\pipelines\VideoPipelines\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: framepack (from N:\AIStudio\pipelines\VideoPipelines\framepack\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: framepack_cu126_torch26 (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: text_encoder (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo\snapshots\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\text_encoder\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: text_encoder_2 (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo\snapshots\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\text_encoder_2\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: vae (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo\snapshots\e8c2aaa66fe3742a32c11a6766aecbf07c56e773\vae\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: image_encoder (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\hf_download\hub\models--lllyasviel--flux_redux_bfl\snapshots\45b801affc54ff2af4e5daf1b282e0921901db87\image_encoder\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: 86cef4396041b6002c957852daac4c91aaa47c79 (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\hf_download\hub\models--lllyasviel--FramePackI2V_HY\snapshots\86cef4396041b6002c957852daac4c91aaa47c79\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Registered pipeline: bert-base-uncased (from N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\Lib\site-packages\mesh_graphormer\modeling\bert\bert-base-uncased\config.json) {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: [DEBUG] Final registered pipelines: {"0":"trellis-stable-projectorz-101","1":"v13_hunyuan2-stableprojectorz","10":"framepack","11":"framepack_cu126_torch26","12":"text_encoder_2","13":"image_encoder","14":"86cef4396041b6002c957852daac4c91aaa47c79","15":"bert-base-uncased","2":"Core","3":"ImageEdit","4":"text_encoder","5":"transformer","6":"vae","7":"ImageGeneration","8":"ImageUpscaling","9":"VideoPipelines","service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline trellis-stable-projectorz-101 already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline v13_hunyuan2-stableprojectorz already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline Core already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline ImageEdit already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline ImageGeneration already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Pipeline ImageUpscaling already exists in embedded configs, keeping embedded version {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: PipelineLoader: Final pipelines after merge: {"0":"Core","1":"ImageGeneration","10":"vae","11":"VideoPipelines","12":"framepack","13":"framepack_cu126_torch26","14":"text_encoder_2","15":"image_encoder","16":"86cef4396041b6002c957852daac4c91aaa47c79","17":"bert-base-uncased","2":"ImageUpscaling","3":"ImageEdit","4":"trellis-stable-projectorz-101","5":"Video Generation","6":"HunyuanVideo","7":"v13_hunyuan2-stableprojectorz","8":"text_encoder","9":"transformer","service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
[RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-03_14-34-31_001.log
[dependency_core] DependencyManager: Starting dependency check for Core
info: DependencyManager: Starting dependency check for Core {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
warn: Core dependency check failed or timed out: {"0":"s","1":"p","10":"k","11":"a","12":"g","13":"e","14":"s","15":" ","16":"i","17":"s","18":" ","19":"n","2":"e","20":"o","21":"t","22":" ","23":"d","24":"e","25":"f","26":"i","27":"n","28":"e","29":"d","3":"c","4":"i","5":"a","6":"l","7":"P","8":"a","9":"c","service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Splash Status: Core dependencies check skipped - continuing startup {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-09-03 14:34:31"}
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: Initialization complete signal sent to renderer {"service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"ImageGeneration","10":"vae","11":"VideoPipelines","12":"framepack","13":"framepack_cu126_torch26","14":"text_encoder_2","15":"image_encoder","16":"86cef4396041b6002c957852daac4c91aaa47c79","17":"bert-base-uncased","2":"ImageUpscaling","3":"ImageEdit","4":"trellis-stable-projectorz-101","5":"Video Generation","6":"HunyuanVideo","7":"v13_hunyuan2-stableprojectorz","8":"text_encoder","9":"transformer","service":"user-service","timestamp":"2025-09-03 14:34:34"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model sdxl-turbo at path: N:\AIStudio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Stable Diffusion model check - model_index.json found for sdxl-turbo {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model flux-dev at path: N:\AIStudio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: FLUX model check - Required files: vae/config.json, text_encoder_2, transformer {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: FLUX model check - Files found: vae/config.json: true, text_encoder_2: true, transformer: true {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model stable-diffusion-v1-5 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Model dependency check for ImageGeneration: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model swinir-real-sr-x4 at path: N:\AIStudio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model realesrgan-x4plus at path: N:\AIStudio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model realesrgan-x4plus-anime at path: N:\AIStudio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model swinir-m-x4 at path: N:\AIStudio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model 4xlsdir at path: N:\AIStudio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
warn: Model 4xlsdir directory exists but is empty: N:\AIStudio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Model dependency check for ImageUpscaling: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:45"}
info: Checking model qwen-image-edit at path: N:\AIStudio\models\ImageEdit\qwen-image-edit {"service":"user-service","timestamp":"2025-09-03 14:34:48"}
info: Model qwen-image-edit directory does not exist: N:\AIStudio\models\ImageEdit\qwen-image-edit {"service":"user-service","timestamp":"2025-09-03 14:34:48"}
warn: Required model qwen-image-edit is not properly installed {"service":"user-service","timestamp":"2025-09-03 14:34:48"}
info: Model dependency check for ImageEdit: not satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:48"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
warn: Failed to check dependencies for Video Generation: requiredModels is not iterable {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: Model dependency check for HunyuanVideo: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 validation paths: {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Server path: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Venv path: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Python exe: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Official repo: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Official API server: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\api_spz\main_api.py {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info:   Start server bat: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\run.bat {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: Checking if Python executable exists: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 Python executable found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 official repository found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 official API server found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 installation marker found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 start server batch file found {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: hunyuan3d-2.1-spz-101 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-09-03 14:34:51"}
info: IPC: get-dependency-status called {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Getting detailed dependency status for all pipelines... {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Model dependency check for Core: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Using cached dependency status for ImageGeneration {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model sdxl-turbo at path: N:\AIStudio\models\ImageGeneration\sdxl-turbo {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Stable Diffusion model check - model_index.json found for sdxl-turbo {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model stable-diffusion-xl-base-1.0 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-xl-base-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model stable-diffusion-xl-refiner-1.0 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-xl-refiner-1.0 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model flux-dev at path: N:\AIStudio\models\ImageGeneration\fluxDev {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: FLUX model check - Required files: vae/config.json, text_encoder_2, transformer {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: FLUX model check - Files found: vae/config.json: true, text_encoder_2: true, transformer: true {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model stable-diffusion-v1-5 at path: N:\AIStudio\models\ImageGeneration\stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Stable Diffusion model check - model_index.json found for stable-diffusion-v1-5 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Model dependency check for ImageGeneration: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model swinir-real-sr-x4 at path: N:\AIStudio\models\upscaling\swinir-real-sr-x4 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model realesrgan-x4plus at path: N:\AIStudio\models\upscaling\realesrgan-x4plus {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model realesrgan-x4plus-anime at path: N:\AIStudio\models\upscaling\realesrgan-x4plus-anime {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model swinir-m-x4 at path: N:\AIStudio\models\upscaling\swinir-m-x4 {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model 4xlsdir at path: N:\AIStudio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
warn: Model 4xlsdir directory exists but is empty: N:\AIStudio\models\upscaling\4xlsdir {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Model dependency check for ImageUpscaling: satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:55"}
info: Checking model qwen-image-edit at path: N:\AIStudio\models\ImageEdit\qwen-image-edit {"service":"user-service","timestamp":"2025-09-03 14:34:58"}
info: Model qwen-image-edit directory does not exist: N:\AIStudio\models\ImageEdit\qwen-image-edit {"service":"user-service","timestamp":"2025-09-03 14:34:58"}
warn: Required model qwen-image-edit is not properly installed {"service":"user-service","timestamp":"2025-09-03 14:34:58"}
info: Model dependency check for ImageEdit: not satisfied {"service":"user-service","timestamp":"2025-09-03 14:34:58"}
info: Microsoft_TRELLIS Python executable found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: Microsoft_TRELLIS ENV folder not found, but venv exists {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: Microsoft_TRELLIS System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
warn: Failed to check dependencies for Video Generation: requiredModels is not iterable {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: Model dependency check for HunyuanVideo: satisfied {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 validation paths: {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Server path: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Venv path: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Python exe: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Official repo: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Official API server: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\api_spz\main_api.py {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info:   Start server bat: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\run.bat {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: Checking if Python executable exists: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Scripts\python.exe {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 Python executable found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 official repository found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 official API server found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 installation marker found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 start server batch file found {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
info: hunyuan3d-2.1-spz-101 System Package validation successful (3/3 key packages found) {"service":"user-service","timestamp":"2025-09-03 14:35:00"}
Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:83837)
    at WebContents.emit (node:events:519:28)