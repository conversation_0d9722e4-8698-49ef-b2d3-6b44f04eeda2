========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T17:08:10.596Z
File: dependency_python installation_2025-08-31_12-08-10_001.log
========================================

[2025-08-31T17:08:10.597Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:08:10.597Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:08:10.598Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:08:10.598Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:08:10.599Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
