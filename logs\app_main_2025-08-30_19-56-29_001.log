========================================
AIStudio Real-time Log: main
Started: 2025-08-31T00:56:29.723Z
File: app_main_2025-08-30_19-56-29_001.log
========================================

[2025-08-31T00:56:29.955Z] [INFO] AIStudio application started successfully
[2025-08-31T00:56:29.955Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T00:56:29.990Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T00:56:30.952Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T00:56:51.630Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_19-56-51_001.log
[2025-08-31T00:56:51.631Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T00:58:50.592Z] [ERROR] Error occurred in handler for 'clean-video-generation-install-with-progress': TypeError: dependencyManager._installVideoGenerationModule is not a function
