========================================
AIStudio Real-time Log: main
Started: 2025-08-30T22:14:39.842Z
File: app_main_2025-08-30_17-14-39_001.log
========================================

[2025-08-30T22:14:40.077Z] [INFO] AIStudio application started successfully
[2025-08-30T22:14:40.077Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T22:14:40.110Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T22:14:41.030Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T22:14:57.172Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_17-14-57_001.log
[2025-08-30T22:14:57.173Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
