========================================
AIStudio Real-time Log: main
Started: 2025-09-01T00:34:00.533Z
File: app_main_2025-08-31_19-34-00_001.log
========================================

[2025-09-01T00:34:00.855Z] [INFO] AIStudio application started successfully
[2025-09-01T00:34:00.855Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T00:34:00.891Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T00:34:01.843Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T00:34:19.767Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_19-34-19_001.log
[2025-09-01T00:34:19.767Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
