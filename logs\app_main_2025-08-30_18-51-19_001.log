========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:51:19.281Z
File: app_main_2025-08-30_18-51-19_001.log
========================================

[2025-08-30T23:51:19.334Z] [ERROR] App threw an error during load
[2025-08-30T23:51:19.334Z] [ERROR] N:\AIStudio\src\main\dependencyManager.js:6567
                  \"& {Remove-Item -LiteralPath '${framepackDir.replace(/\/g, '\\')}' -Recurse -Force}\" -Wait
                                                                        ^

SyntaxError: Invalid regular expression: missing /
    at wrapSafe (node:internal/modules/cjs/loader:1385:20)
    at Module._compile (node:internal/modules/cjs/loader:1435:41)
    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
    at Module.load (node:internal/modules/cjs/loader:1295:32)
    at Module._load (node:internal/modules/cjs/loader:1111:12)
    at c._load (node:electron/js2c/node_init:2:16955)
    at Module.require (node:internal/modules/cjs/loader:1318:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (N:\AIStudio\src\main\index.js:13:27)
    at Module._compile (node:internal/modules/cjs/loader:1484:14)
[2025-08-30T23:51:19.335Z] [ERROR] Uncaught Exception: SyntaxError: Invalid regular expression: missing /
[2025-08-30T23:51:19.335Z] [ERROR] [main] ERROR: Uncaught Exception: SyntaxError: Invalid regular expression: missing /
[2025-08-30T23:51:19.335Z] [ERROR] Uncaught Exception: SyntaxError: Invalid regular expression: missing /
