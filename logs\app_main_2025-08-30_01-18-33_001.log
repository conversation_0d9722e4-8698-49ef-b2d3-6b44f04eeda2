========================================
AIStudio Real-time Log: main
Started: 2025-08-30T06:18:33.417Z
File: app_main_2025-08-30_01-18-33_001.log
========================================

[2025-08-30T06:18:33.666Z] [INFO] AIStudio application started successfully
[2025-08-30T06:18:33.666Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T06:18:33.701Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T06:18:34.556Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T06:18:51.963Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_01-18-51_001.log
[2025-08-30T06:18:51.963Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T06:18:54.234Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T06:18:54.235Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T06:18:55.242Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T06:19:35.315Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_dependency check_2025-08-30_01-19-35_001.log
[2025-08-30T06:19:35.315Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting dependency check for ImageGeneration
