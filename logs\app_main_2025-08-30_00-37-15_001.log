========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:37:15.525Z
File: app_main_2025-08-30_00-37-15_001.log
========================================

[2025-08-30T05:37:15.744Z] [INFO] AIStudio application started successfully
[2025-08-30T05:37:15.744Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:37:15.778Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:37:16.766Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:37:39.771Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-37-39_001.log
[2025-08-30T05:37:39.771Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:37:45.309Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:37:45.310Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:37:46.313Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
