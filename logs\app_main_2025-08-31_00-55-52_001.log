========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:55:52.584Z
File: app_main_2025-08-31_00-55-52_001.log
========================================

[2025-08-31T05:55:52.799Z] [INFO] AIStudio application started successfully
[2025-08-31T05:55:52.799Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:55:52.832Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:55:53.819Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:56:11.487Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-56-11_001.log
[2025-08-31T05:56:11.488Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:56:42.183Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-56-42_001.log
[2025-08-31T05:56:42.183Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:56:42.184Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:56:42.185Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:56:42.185Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:56:42.186Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:56:42.187Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-56-42_001.log
[2025-08-31T05:56:42.187Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T05:57:42.775Z] [INFO] [RealtimeLogger] Started logging dependency_imageedit to: dependency_models installation_2025-08-31_00-57-42_001.log
[2025-08-31T05:57:42.776Z] [INFO] [dependency_imageedit] DependencyManager: Starting models installation for ImageEdit
[2025-08-31T05:57:42.776Z] [INFO] [dependency_imageedit] DependencyManager: Installing dependencies for ImageEdit (models:all)
[2025-08-31T05:57:42.777Z] [INFO] [dependency_imageedit] DependencyManager: Component type: string, Component value: 'models'
[2025-08-31T05:57:42.777Z] [INFO] [dependency_imageedit] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:57:42.777Z] [INFO] [dependency_imageedit] DependencyManager: About to check routing for ImageEdit with component models
