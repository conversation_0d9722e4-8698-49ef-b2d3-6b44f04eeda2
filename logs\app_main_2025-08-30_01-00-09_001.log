========================================
AIStudio Real-time Log: main
Started: 2025-08-30T06:00:09.719Z
File: app_main_2025-08-30_01-00-09_001.log
========================================

[2025-08-30T06:00:09.955Z] [INFO] AIStudio application started successfully
[2025-08-30T06:00:09.955Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T06:00:09.989Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T06:00:10.877Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T06:00:27.438Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_01-00-27_001.log
[2025-08-30T06:00:27.438Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T06:00:29.838Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T06:00:29.839Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T06:00:30.844Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
