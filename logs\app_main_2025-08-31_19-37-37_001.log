========================================
AIStudio Real-time Log: main
Started: 2025-09-01T00:37:37.156Z
File: app_main_2025-08-31_19-37-37_001.log
========================================

[2025-09-01T00:37:37.395Z] [INFO] AIStudio application started successfully
[2025-09-01T00:37:37.395Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T00:37:37.428Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T00:37:38.494Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T00:37:55.545Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_19-37-55_001.log
[2025-09-01T00:37:55.545Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T00:39:21.790Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_19-39-21_001.log
[2025-09-01T00:39:21.791Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-09-01T00:39:21.792Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-09-01T00:39:21.792Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-09-01T00:39:21.793Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-09-01T00:39:21.793Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-09-01T00:39:21.795Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_19-39-21_001.log
[2025-09-01T00:39:21.795Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
