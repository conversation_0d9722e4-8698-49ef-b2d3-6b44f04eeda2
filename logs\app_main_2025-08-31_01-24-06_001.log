========================================
AIStudio Real-time Log: main
Started: 2025-08-31T06:24:06.499Z
File: app_main_2025-08-31_01-24-06_001.log
========================================

[2025-08-31T06:24:06.732Z] [INFO] AIStudio application started successfully
[2025-08-31T06:24:06.732Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T06:24:06.761Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T06:24:07.726Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T06:24:24.548Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_01-24-24_001.log
[2025-08-31T06:24:24.548Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T06:24:51.058Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-24-51_001.log
[2025-08-31T06:24:51.058Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:24:51.059Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:24:51.059Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:24:51.060Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:24:51.060Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:24:51.061Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-24-51_001.log
[2025-08-31T06:24:51.061Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
