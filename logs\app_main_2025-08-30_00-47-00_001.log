========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:47:00.363Z
File: app_main_2025-08-30_00-47-00_001.log
========================================

[2025-08-30T05:47:00.582Z] [INFO] AIStudio application started successfully
[2025-08-30T05:47:00.582Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:47:00.617Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:47:01.473Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:47:18.142Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-47-18_001.log
[2025-08-30T05:47:18.143Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:47:20.667Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:47:20.668Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:47:21.675Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
