========================================
AIStudio Real-time Log: main
Started: 2025-08-31T17:56:46.867Z
File: app_main_2025-08-31_12-56-46_001.log
========================================

[2025-08-31T17:56:47.113Z] [INFO] AIStudio application started successfully
[2025-08-31T17:56:47.113Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T17:56:47.149Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T17:56:48.191Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T17:57:02.266Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_12-57-02_001.log
[2025-08-31T17:57:02.266Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T17:57:43.030Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_12-57-43_001.log
[2025-08-31T17:57:43.030Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:57:43.031Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:57:43.031Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:57:43.032Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:57:43.032Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T17:57:43.033Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_12-57-43_001.log
[2025-08-31T17:57:43.033Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
