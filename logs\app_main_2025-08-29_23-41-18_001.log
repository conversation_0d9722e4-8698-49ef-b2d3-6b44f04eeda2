========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:41:18.202Z
File: app_main_2025-08-29_23-41-18_001.log
========================================

[2025-08-30T04:41:18.427Z] [INFO] AIStudio application started successfully
[2025-08-30T04:41:18.427Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:41:18.462Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:41:19.326Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T04:41:37.188Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-41-37_001.log
[2025-08-30T04:41:37.189Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:41:39.692Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:41:39.693Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:41:40.704Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
