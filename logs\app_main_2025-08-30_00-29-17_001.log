========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:29:17.294Z
File: app_main_2025-08-30_00-29-17_001.log
========================================

[2025-08-30T05:29:17.513Z] [INFO] AIStudio application started successfully
[2025-08-30T05:29:17.513Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:29:17.544Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:29:18.434Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:29:35.213Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-29-35_001.log
[2025-08-30T05:29:35.213Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:29:41.985Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:29:41.986Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:29:43.000Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
