========================================
AIStudio Real-time Log: main
Started: 2025-08-31T17:46:51.731Z
File: app_main_2025-08-31_12-46-51_001.log
========================================

[2025-08-31T17:46:51.963Z] [INFO] AIStudio application started successfully
[2025-08-31T17:46:51.963Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T17:46:51.999Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T17:46:53.051Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T17:47:07.852Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_12-47-07_001.log
[2025-08-31T17:47:07.852Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T17:49:48.097Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_12-49-48_001.log
[2025-08-31T17:49:48.098Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:49:48.098Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:49:48.099Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:49:48.099Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:49:48.100Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T17:49:48.101Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_12-49-48_001.log
[2025-08-31T17:49:48.101Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
