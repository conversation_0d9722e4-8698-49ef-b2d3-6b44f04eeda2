========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-08-30T00:07:27.097Z
File: dependency_bundled installation_2025-08-29_19-07-27_001.log
========================================

[2025-08-30T00:07:27.097Z] [INFO] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:07:27.097Z] [INFO] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:07:27.098Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:07:27.098Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:07:27.099Z] [INFO] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:07:27.099Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:07:27.116Z] [INFO] DependencyManager: Existing directory removed successfully
[2025-08-30T00:07:27.118Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:07:27.119Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
