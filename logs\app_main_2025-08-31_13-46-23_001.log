========================================
AIStudio Real-time Log: main
Started: 2025-08-31T18:46:23.967Z
File: app_main_2025-08-31_13-46-23_001.log
========================================

[2025-08-31T18:46:24.232Z] [INFO] AIStudio application started successfully
[2025-08-31T18:46:24.232Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T18:46:24.269Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T18:46:25.252Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T18:46:39.745Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_13-46-39_001.log
[2025-08-31T18:46:39.746Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T18:47:32.707Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_13-47-32_001.log
[2025-08-31T18:47:32.707Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T18:47:32.708Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T18:47:32.709Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T18:47:32.709Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T18:47:32.710Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T18:47:32.711Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_13-47-32_001.log
[2025-08-31T18:47:32.711Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
