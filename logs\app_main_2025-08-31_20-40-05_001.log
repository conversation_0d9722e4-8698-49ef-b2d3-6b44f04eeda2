========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:40:05.964Z
File: app_main_2025-08-31_20-40-05_001.log
========================================

[2025-09-01T01:40:06.247Z] [INFO] AIStudio application started successfully
[2025-09-01T01:40:06.247Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:40:06.283Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:40:07.287Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:40:26.342Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-40-26_001.log
[2025-09-01T01:40:26.342Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:40:48.431Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T01:40:48.433Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T01:40:48.434Z] [INFO] [34m[Video Server][39m Batch file exists: true
[2025-09-01T01:40:48.436Z] [INFO] [34m[Video Server][39m Working directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-01T01:40:48.437Z] [INFO] [34m[Video Server][39m Command: cmd.exe /c run.bat
[2025-09-01T01:40:53.476Z] [INFO] [34m[Video Server][39m Status check error: this.isServerRunning is not a function debug
[2025-09-01T01:41:02.562Z] [INFO] [34m[Video Server][39m [STDOUT] Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']
[2025-09-01T01:41:02.564Z] [INFO] [34m[Video Server][39m [STDOUT] Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
[2025-09-01T01:41:03.154Z] [INFO] [34m[Video Server][39m [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)
[2025-09-01T01:41:04.153Z] [INFO] [34m[Video Server][39m [STDOUT] Free VRAM 10.9814453125 GB
High-VRAM Mode: False
[2025-09-01T01:41:05.070Z] [INFO] [34m[Video Server][39m [STDERR] Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]
[2025-09-01T01:41:05.170Z] [INFO] [34m[Video Server][39m [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
[2025-09-01T01:41:18.477Z] [ERROR] Framepack video generation failed: Error: Server startup timed out after 30 seconds
