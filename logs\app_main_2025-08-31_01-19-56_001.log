========================================
AIStudio Real-time Log: main
Started: 2025-08-31T06:19:56.060Z
File: app_main_2025-08-31_01-19-56_001.log
========================================

[2025-08-31T06:19:56.327Z] [INFO] AIStudio application started successfully
[2025-08-31T06:19:56.327Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T06:19:56.364Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T06:19:57.343Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T06:20:24.891Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_01-20-24_001.log
[2025-08-31T06:20:24.891Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T06:20:59.091Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-20-59_001.log
[2025-08-31T06:20:59.091Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:20:59.092Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:20:59.093Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:20:59.094Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:20:59.095Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:20:59.099Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-20-59_001.log
[2025-08-31T06:20:59.099Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
