========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:53:16.560Z
File: app_main_2025-08-30_00-53-16_001.log
========================================

[2025-08-30T05:53:16.786Z] [INFO] AIStudio application started successfully
[2025-08-30T05:53:16.786Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:53:16.817Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:53:17.716Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:53:34.592Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-53-34_001.log
[2025-08-30T05:53:34.592Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:53:36.847Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:53:36.848Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:53:37.857Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
