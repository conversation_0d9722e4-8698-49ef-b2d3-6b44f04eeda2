========================================
AIStudio Real-time Log: main
Started: 2025-08-30T06:10:33.435Z
File: app_main_2025-08-30_01-10-33_001.log
========================================

[2025-08-30T06:10:33.642Z] [INFO] AIStudio application started successfully
[2025-08-30T06:10:33.642Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T06:10:33.671Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T06:10:34.638Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T06:10:50.865Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_01-10-50_001.log
[2025-08-30T06:10:50.865Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T06:10:53.488Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T06:10:53.489Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T06:10:54.493Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T06:11:18.481Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_dependency check_2025-08-30_01-11-18_001.log
[2025-08-30T06:11:18.482Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting dependency check for ImageGeneration
