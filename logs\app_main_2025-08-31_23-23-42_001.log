========================================
AIStudio Real-time Log: main
Started: 2025-09-01T04:23:42.417Z
File: app_main_2025-08-31_23-23-42_001.log
========================================

[2025-09-01T04:23:42.655Z] [INFO] AIStudio application started successfully
[2025-09-01T04:23:42.655Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T04:23:42.687Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T04:23:43.701Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-01T04:23:43.702Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                   41712 Console                    1  2,202,308 K
[2025-09-01T04:23:44.095Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-01T04:24:05.601Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_23-24-05_001.log
[2025-09-01T04:24:05.602Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
