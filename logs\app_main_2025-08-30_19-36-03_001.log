========================================
AIStudio Real-time Log: main
Started: 2025-08-31T00:36:03.044Z
File: app_main_2025-08-30_19-36-03_001.log
========================================

[2025-08-31T00:36:03.281Z] [INFO] AIStudio application started successfully
[2025-08-31T00:36:03.282Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T00:36:03.315Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T00:36:04.355Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T00:36:20.947Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_19-36-20_001.log
[2025-08-31T00:36:20.948Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
