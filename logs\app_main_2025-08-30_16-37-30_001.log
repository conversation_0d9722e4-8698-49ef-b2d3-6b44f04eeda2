========================================
AIStudio Real-time Log: main
Started: 2025-08-30T21:37:30.396Z
File: app_main_2025-08-30_16-37-30_001.log
========================================

[2025-08-30T21:37:30.638Z] [INFO] AIStudio application started successfully
[2025-08-30T21:37:30.638Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T21:37:30.674Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T21:37:31.662Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T21:37:44.713Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_16-37-44_001.log
[2025-08-30T21:37:44.713Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T21:37:47.245Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T21:37:47.246Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T21:37:48.253Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
