const Stream = require('stream');

module.exports = function(entry) {
  return new Promise(function(resolve, reject) {
    const chunks = [];
    const bufferStream = Stream.Transform()
      .on('finish', function() {
        resolve(Buffer.concat(chunks));
      })
      .on('error', reject);

    bufferStream._transform = function(d, e, cb) {
      chunks.push(d);
      cb();
    };
    entry.on('error', reject)
      .pipe(bufferStream);
  });
};
