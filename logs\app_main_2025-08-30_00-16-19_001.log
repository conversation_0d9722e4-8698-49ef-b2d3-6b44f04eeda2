========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:16:19.861Z
File: app_main_2025-08-30_00-16-19_001.log
========================================

[2025-08-30T05:16:20.090Z] [INFO] AIStudio application started successfully
[2025-08-30T05:16:20.090Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:16:20.128Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:16:21.048Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:16:42.668Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-16-42_001.log
[2025-08-30T05:16:42.668Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:16:45.110Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:16:45.111Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:16:46.120Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T05:17:21.974Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_dependency check_2025-08-30_00-17-21_001.log
[2025-08-30T05:17:21.974Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting dependency check for ImageGeneration
