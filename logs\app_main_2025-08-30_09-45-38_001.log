========================================
AIStudio Real-time Log: main
Started: 2025-08-30T14:45:38.943Z
File: app_main_2025-08-30_09-45-38_001.log
========================================

[2025-08-30T14:45:39.185Z] [INFO] AIStudio application started successfully
[2025-08-30T14:45:39.186Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T14:45:39.217Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T14:45:40.007Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-08-30T14:45:40.007Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                    4604 Console                    1 12,686,124 K
[2025-08-30T14:45:40.397Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-08-30T14:47:03.438Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_09-47-03_001.log
[2025-08-30T14:47:03.438Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T14:47:07.044Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T14:47:07.045Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T14:47:08.054Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
