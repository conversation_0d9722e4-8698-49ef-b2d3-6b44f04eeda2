========================================
AIStudio Real-time Log: main
Started: 2025-09-01T00:02:48.210Z
File: app_main_2025-08-31_19-02-48_001.log
========================================

[2025-09-01T00:02:48.447Z] [INFO] AIStudio application started successfully
[2025-09-01T00:02:48.447Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T00:02:48.484Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T00:02:49.541Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T00:03:07.152Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_19-03-07_001.log
[2025-09-01T00:03:07.152Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T00:03:52.613Z] [INFO] [RealtimeLogger] Started logging videogeneration to: process_videogeneration_2025-08-31_19-03-52_001.log
[2025-09-01T00:03:52.622Z] [INFO] [RealtimeLogger] Closed log stream: videogeneration
[2025-09-01T00:03:52.624Z] [ERROR] Framepack video generation failed: Error: spawn N:\AIStudio\pipelines\VideoPipelines\framepack\venv\Scripts\python.exe ENOENT
