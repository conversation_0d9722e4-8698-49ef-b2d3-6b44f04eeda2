========================================
AIStudio Real-time Log: main
Started: 2025-08-30T06:06:00.900Z
File: app_main_2025-08-30_01-06-00_001.log
========================================

[2025-08-30T06:06:01.117Z] [INFO] AIStudio application started successfully
[2025-08-30T06:06:01.117Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T06:06:01.148Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T06:06:02.060Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T06:06:18.513Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_01-06-18_001.log
[2025-08-30T06:06:18.513Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T06:06:22.582Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T06:06:22.583Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T06:06:23.587Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T06:07:28.485Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_dependency check_2025-08-30_01-07-28_001.log
[2025-08-30T06:07:28.485Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting dependency check for ImageGeneration
