========================================
AIStudio Real-time Log: main
Started: 2025-08-31T04:09:12.167Z
File: app_main_2025-08-30_23-09-12_001.log
========================================

[2025-08-31T04:09:12.413Z] [INFO] AIStudio application started successfully
[2025-08-31T04:09:12.413Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T04:09:12.457Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T04:09:13.451Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T04:09:34.909Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_23-09-34_001.log
[2025-08-31T04:09:34.909Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T04:31:40.958Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_23-31-40_001.log
[2025-08-31T04:31:40.958Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T04:31:40.959Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T04:31:40.960Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T04:31:40.961Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T04:31:40.962Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T04:31:40.963Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_23-31-40_001.log
[2025-08-31T04:31:40.964Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
