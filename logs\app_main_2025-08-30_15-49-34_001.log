========================================
AIStudio Real-time Log: main
Started: 2025-08-30T20:49:34.384Z
File: app_main_2025-08-30_15-49-34_001.log
========================================

[2025-08-30T20:49:34.632Z] [INFO] AIStudio application started successfully
[2025-08-30T20:49:34.632Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T20:49:34.671Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T20:49:35.738Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T20:49:51.609Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_15-49-51_001.log
[2025-08-30T20:49:51.610Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T20:49:55.364Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T20:49:55.365Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T20:49:56.380Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
