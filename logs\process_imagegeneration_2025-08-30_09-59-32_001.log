========================================
AIStudio Real-time Log: imagegeneration
Started: 2025-08-30T14:59:32.532Z
File: process_imagegeneration_2025-08-30_09-59-32_001.log
========================================

[2025-08-30T15:09:49.854Z] [STDERR] A matching Triton is not available, some optimizations will not be enabled
[2025-08-30T15:09:49.854Z] [STDERR] Traceback (most recent call last):
[2025-08-30T15:09:49.855Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\xformers\__init__.py", line 57, in _is_triton_available
[2025-08-30T15:09:49.855Z] [STDERR] import triton  # noqa
[2025-08-30T15:09:49.855Z] [STDERR] ^^^^^^^^^^^^^
[2025-08-30T15:09:49.856Z] [STDERR] ModuleNotFoundError: No module named 'triton'
[2025-08-30T15:09:53.122Z] [STDOUT] Unified systems loaded successfully
[2025-08-30T15:09:53.123Z] [STDOUT] PROGRESS:{"stage": "initializing", "step": 1, "total": 2, "stage_progress": 50.0, "overall_progress": 2.5, "message": "Initializing image generator...", "timestamp": "2025-08-30T10:09:53.120523", "elapsed_time": 0.0}
[2025-08-30T15:09:54.891Z] [STDOUT] TAESD decoder loaded for fast previews
[2025-08-30T15:09:54.892Z] [STDOUT] Unified systems initialized
[2025-08-30T15:09:54.892Z] [STDOUT] ImageGenerator initialized:
[2025-08-30T15:09:54.893Z] [STDOUT] Device: cuda
[2025-08-30T15:09:54.893Z] [STDOUT] Torch dtype: torch.float16
[2025-08-30T15:09:54.894Z] [STDOUT] App root: N:\AIStudio
[2025-08-30T15:09:54.894Z] [STDOUT] Models path: N:\AIStudio\models\ImageGeneration
[2025-08-30T15:09:54.894Z] [STDOUT] Preview quality: high
[2025-08-30T15:09:54.895Z] [STDOUT] PROGRESS:{"stage": "initializing", "step": 2, "total": 2, "stage_progress": 100.0, "overall_progress": 5.0, "message": "Generator initialized", "timestamp": "2025-08-30T10:09:54.889021", "elapsed_time": 1.77}
[2025-08-30T15:09:54.895Z] [STDOUT] Initializing sdxl-turbo model...
[2025-08-30T15:09:54.896Z] [STDOUT] Loading model: sdxl-turbo
[2025-08-30T15:09:54.896Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 0, "total": 7, "stage_progress": 0.0, "overall_progress": 5.0, "message": "Starting to load sdxl-turbo", "timestamp": "2025-08-30T10:09:54.889021", "elapsed_time": 1.77}
[2025-08-30T15:09:54.897Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 1, "total": 7, "stage_progress": 14.3, "overall_progress": 10.7, "message": "Cleared previous model", "timestamp": "2025-08-30T10:09:54.889021", "elapsed_time": 1.77}
[2025-08-30T15:09:54.899Z] [STDOUT] Using unified model loader for sdxl-turbo
[2025-08-30T15:09:54.899Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 1, "total": 5, "stage_progress": 20.0, "overall_progress": 13.0, "message": "Loading sdxl-turbo...", "timestamp": "2025-08-30T10:09:54.893252", "elapsed_time": 1.77}
[2025-08-30T15:09:54.900Z] [STDOUT] [Model Loader] Using fp16 variant for sdxl-turbo
[2025-08-30T15:09:54.900Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 2, "total": 5, "stage_progress": 40.0, "overall_progress": 21.0, "message": "Initializing sdxl-turbo pipeline...", "timestamp": "2025-08-30T10:09:54.893252", "elapsed_time": 1.77}
[2025-08-30T15:09:54.927Z] [STDERR] Loading pipeline components...:   0%|          | 0/7 [00:00<?, ?it/s]
[2025-08-30T15:09:55.058Z] [STDERR] Loading pipeline components...:  14%|#4        | 1/7 [00:00<00:00,  7.78it/s]
[2025-08-30T15:09:55.838Z] [STDERR] Loading pipeline components...:  29%|##8       | 2/7 [00:00<00:02,  1.95it/s]
[2025-08-30T15:09:55.947Z] [STDERR] Loading pipeline components...:  43%|####2     | 3/7 [00:01<00:01,  3.05it/s]
[2025-08-30T15:09:56.330Z] [STDERR] Loading pipeline components...:  57%|#####7    | 4/7 [00:01<00:01,  2.86it/s]
[2025-08-30T15:09:58.630Z] [STDERR] Loading pipeline components...:  71%|#######1  | 5/7 [00:03<00:02,  1.05s/it]
[2025-08-30T15:10:06.815Z] [STDERR] Loading pipeline components...:  86%|########5 | 6/7 [00:11<00:03,  3.48s/it]
[2025-08-30T15:10:07.257Z] [STDERR] Loading pipeline components...: 100%|##########| 7/7 [00:12<00:00,  2.49s/it]
Loading pipeline components...: 100%|##########| 7/7 [00:12<00:00,  1.76s/it]
[2025-08-30T15:10:07.259Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 4, "total": 5, "stage_progress": 80.0, "overall_progress": 37.0, "message": "Moving sdxl-turbo to device...", "timestamp": "2025-08-30T10:10:07.256781", "elapsed_time": 14.14}
[2025-08-30T15:11:34.321Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 5, "total": 5, "stage_progress": 100.0, "overall_progress": 45.0, "message": "sdxl-turbo loaded successfully", "timestamp": "2025-08-30T10:11:34.320073", "elapsed_time": 101.2}
[2025-08-30T15:11:34.321Z] [STDOUT] Successfully loaded sdxl-turbo
[2025-08-30T15:11:34.322Z] [STDOUT] PROGRESS:{"stage": "loading_model", "step": 7, "total": 7, "stage_progress": 100.0, "overall_progress": 45.0, "message": "Model sdxl-turbo loaded successfully", "timestamp": "2025-08-30T10:11:34.320073", "elapsed_time": 101.2}
[2025-08-30T15:11:34.519Z] [STDOUT] Starting image generation...
[2025-08-30T15:11:34.519Z] [STDOUT] Generating image with prompt: 'A red sports car, isolated object on white background, centered, full view, clean white studio light...'
[2025-08-30T15:11:34.520Z] [STDOUT] Parameters: 1024x1024, steps=20, guidance=7.5, seed=None
[2025-08-30T15:11:34.520Z] [STDOUT] PROGRESS:{"stage": "generating", "step": 0, "total": 20, "stage_progress": 0.0, "overall_progress": 45.0, "message": "Starting generation with 20 steps", "timestamp": "2025-08-30T10:11:34.520499", "elapsed_time": 101.4}
[2025-08-30T15:11:34.568Z] [STDERR] N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1028: FutureWarning: `callback` is deprecated and will be removed in version 1.0.0. Passing `callback` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
[2025-08-30T15:11:34.568Z] [STDERR] deprecate(
[2025-08-30T15:11:34.569Z] [STDERR] N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py:1034: FutureWarning: `callback_steps` is deprecated and will be removed in version 1.0.0. Passing `callback_steps` as an input argument to `__call__` is deprecated, consider use `callback_on_step_end`
[2025-08-30T15:11:34.569Z] [STDERR] deprecate(
[2025-08-30T15:11:41.188Z] [STDERR] 0%|          | 0/4 [00:00<?, ?it/s]
[2025-08-30T15:11:51.668Z] [STDERR] 25%|##5       | 1/4 [00:10<00:31, 10.48s/it]
[2025-08-30T15:11:55.108Z] [STDERR] Traceback (most recent call last):
[2025-08-30T15:11:55.109Z] [STDERR] File "N:\AIStudio\utils\helpers\unified_preview_system.py", line 102, in decode_latents_with_vae
[2025-08-30T15:11:55.109Z] [STDERR] decoded = vae.decode(scaled_latents).sample[0].cpu()
[2025-08-30T15:11:55.109Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.110Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\utils\accelerate_utils.py", line 46, in wrapper
[2025-08-30T15:11:55.110Z] [STDERR] return method(self, *args, **kwargs)
[2025-08-30T15:11:55.110Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.111Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 323, in decode
[2025-08-30T15:11:55.111Z] [STDERR] decoded = self._decode(z).sample
[2025-08-30T15:11:55.111Z] [STDERR] ^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.112Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\diffusers\models\autoencoders\autoencoder_kl.py", line 292, in _decode
[2025-08-30T15:11:55.112Z] [STDERR] z = self.post_quant_conv(z)
[2025-08-30T15:11:55.112Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.113Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
[2025-08-30T15:11:55.113Z] [STDERR] return self._call_impl(*args, **kwargs)
[2025-08-30T15:11:55.113Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.114Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
[2025-08-30T15:11:55.114Z] [STDERR] return forward_call(*args, **kwargs)
[2025-08-30T15:11:55.114Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.114Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 554, in forward
[2025-08-30T15:11:55.115Z] [STDERR] return self._conv_forward(input, self.weight, self.bias)
[2025-08-30T15:11:55.115Z] [STDERR] ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-30T15:11:55.115Z] [STDERR] File "N:\AIStudio\pipelines\ImageGeneration\env\Lib\site-packages\torch\nn\modules\conv.py", line 549, in _conv_forward
[2025-08-30T15:11:55.116Z] [STDERR] return F.conv2d(
[2025-08-30T15:11:55.116Z] [STDERR] ^^^^^^^^^
[2025-08-30T15:11:55.116Z] [STDERR] RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward)
[2025-08-30T15:11:55.922Z] [STDOUT] [Preview] Generating preview for sdxl-turbo, quality: high
[2025-08-30T15:11:55.923Z] [STDOUT] [Preview VAE] Starting decode with latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:11:55.923Z] [STDOUT] [Preview VAE] Using scaling factor: 0.13025
[2025-08-30T15:11:55.923Z] [STDOUT] [Preview VAE] VAE device: cpu, dtype: torch.float16
[2025-08-30T15:11:55.924Z] [STDOUT] [Preview VAE] Scaled latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:11:55.924Z] [STDOUT] [Preview VAE] Failed to decode with VAE: Expected all tensors to be on the same device, but found at least two devices, cpu and cuda:0! (when checking argument for argument weight in method wrapper_CUDA___slow_conv2d_forward)
[2025-08-30T15:11:55.925Z] [STDOUT] [Preview TAESD] Using TAESD decoder
[2025-08-30T15:11:55.925Z] [STDOUT] [Preview TAESD] Failed to decode with TAESD: Given groups=1, weight of size [64, 3, 3, 3], expected input[1, 4, 128, 128] to have 3 channels, but got 4 channels instead
[2025-08-30T15:11:55.926Z] [STDOUT] [Preview Latent] Creating visualization for sdxl-turbo
[2025-08-30T15:11:55.926Z] [STDOUT] [Preview Latent] Input latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:11:55.926Z] [STDOUT] [Preview Latent] Processing latents shape: torch.Size([4, 128, 128])
[2025-08-30T15:11:55.927Z] [STDOUT] [Preview Latent] Combined latent image shape: torch.Size([128, 128])
[2025-08-30T15:11:55.928Z] [STDOUT] [Preview Latent] Created PIL image size: (128, 128)
[2025-08-30T15:11:55.928Z] [STDOUT] [Preview Encode] Encoded to base64, length: 19016
[2025-08-30T15:11:55.928Z] [STDOUT] [Preview] Latent visualization successful
[2025-08-30T15:11:55.929Z] [STDOUT] [Progress] Generated preview image (base64 length: 19016)
[2025-08-30T15:11:55.931Z] [STDOUT] PROGRESS:{"stage": "generating", "step": 1, "total": 20, "stage_progress": 5.0, "overall_progress": 47.5, "message": "Denoising step 1/20", "timestamp": "2025-08-30T10:11:55.922231", "elapsed_time": 122.8, "preview_image": "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"}
[2025-08-30T15:12:00.150Z] [STDERR] 50%|#####     | 2/4 [00:18<00:18,  9.30s/it]
[2025-08-30T15:12:04.983Z] [STDERR] N:\AIStudio\utils\helpers\unified_preview_system.py:109: RuntimeWarning: invalid value encountered in cast
[2025-08-30T15:12:04.983Z] [STDERR] np_img = (decoded.permute(1, 2, 0).numpy() * 255).astype('uint8')
[2025-08-30T15:12:05.081Z] [STDOUT] [Preview] Generating preview for sdxl-turbo, quality: high
[2025-08-30T15:12:05.081Z] [STDOUT] [Preview VAE] Starting decode with latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:05.082Z] [STDOUT] [Preview VAE] Using scaling factor: 0.13025
[2025-08-30T15:12:05.082Z] [STDOUT] [Preview VAE] VAE device: cuda:0, dtype: torch.float16
[2025-08-30T15:12:05.083Z] [STDOUT] [Preview VAE] Scaled latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:05.083Z] [STDOUT] [Preview VAE] Decoded shape: torch.Size([3, 1024, 1024])
[2025-08-30T15:12:05.083Z] [STDOUT] [Preview VAE] PIL image size: (1024, 1024)
[2025-08-30T15:12:05.084Z] [STDOUT] [Preview Encode] Resized to: (512, 512)
[2025-08-30T15:12:05.084Z] [STDOUT] [Preview Encode] Encoded to base64, length: 6300
[2025-08-30T15:12:05.085Z] [STDOUT] [Preview] High-quality VAE decode successful
[2025-08-30T15:12:05.085Z] [STDOUT] [Progress] Generated preview image (base64 length: 6300)
[2025-08-30T15:12:05.086Z] [STDOUT] PROGRESS:{"stage": "generating", "step": 2, "total": 20, "stage_progress": 10.0, "overall_progress": 50.0, "message": "Denoising step 2/20", "timestamp": "2025-08-30T10:12:05.082022", "elapsed_time": 131.96, "preview_image": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAIAAgADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD8qqKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/2Q=="}
[2025-08-30T15:12:09.027Z] [STDERR] 75%|#######5  | 3/4 [00:27<00:09,  9.11s/it]
[2025-08-30T15:12:13.019Z] [STDOUT] [Preview] Generating preview for sdxl-turbo, quality: high
[2025-08-30T15:12:13.020Z] [STDOUT] [Preview VAE] Starting decode with latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:13.020Z] [STDOUT] [Preview VAE] Using scaling factor: 0.13025
[2025-08-30T15:12:13.020Z] [STDOUT] [Preview VAE] VAE device: cuda:0, dtype: torch.float16
[2025-08-30T15:12:13.020Z] [STDOUT] [Preview VAE] Scaled latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:13.021Z] [STDOUT] [Preview VAE] Decoded shape: torch.Size([3, 1024, 1024])
[2025-08-30T15:12:13.021Z] [STDOUT] [Preview VAE] PIL image size: (1024, 1024)
[2025-08-30T15:12:13.021Z] [STDOUT] [Preview Encode] Resized to: (512, 512)
[2025-08-30T15:12:13.021Z] [STDOUT] [Preview Encode] Encoded to base64, length: 122644
[2025-08-30T15:12:13.022Z] [STDOUT] [Preview] High-quality VAE decode successful
[2025-08-30T15:12:13.022Z] [STDOUT] [Progress] Generated preview image (base64 length: 122644)
[2025-08-30T15:12:13.026Z] [STDOUT] PROGRESS:{"stage": "generating", "step": 3, "total": 20, "stage_progress": 15.0, "overall_progress": 52.5, "message": "Denoising step 3/20", "timestamp": "2025-08-30T10:12:13.018078", "elapsed_time": 139.9, "preview_image": "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"}
[2025-08-30T15:12:17.053Z] [STDERR] 100%|##########| 4/4 [00:35<00:00,  8.68s/it]
[2025-08-30T15:12:21.136Z] [STDOUT] [Preview] Generating preview for sdxl-turbo, quality: high
[2025-08-30T15:12:21.137Z] [STDOUT] [Preview VAE] Starting decode with latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:21.137Z] [STDOUT] [Preview VAE] Using scaling factor: 0.13025
[2025-08-30T15:12:21.137Z] [STDOUT] [Preview VAE] VAE device: cuda:0, dtype: torch.float16
[2025-08-30T15:12:21.138Z] [STDOUT] [Preview VAE] Scaled latents shape: torch.Size([1, 4, 128, 128])
[2025-08-30T15:12:21.138Z] [STDOUT] [Preview VAE] Decoded shape: torch.Size([3, 1024, 1024])
[2025-08-30T15:12:21.138Z] [STDOUT] [Preview VAE] PIL image size: (1024, 1024)
[2025-08-30T15:12:21.138Z] [STDOUT] [Preview Encode] Resized to: (512, 512)
[2025-08-30T15:12:21.139Z] [STDOUT] [Preview Encode] Encoded to base64, length: 6300
[2025-08-30T15:12:21.139Z] [STDOUT] [Preview] High-quality VAE decode successful
[2025-08-30T15:12:21.139Z] [STDOUT] [Progress] Generated preview image (base64 length: 6300)
[2025-08-30T15:12:21.140Z] [STDOUT] PROGRESS:{"stage": "generating", "step": 4, "total": 20, "stage_progress": 20.0, "overall_progress": 55.0, "message": "Denoising step 4/20", "timestamp": "2025-08-30T10:12:21.137738", "elapsed_time": 148.02, "preview_image": "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"}
[2025-08-30T15:12:21.142Z] [STDERR] 100%|##########| 4/4 [00:39<00:00,  9.99s/it]
[2025-08-30T15:12:23.461Z] [STDOUT] PROGRESS:{"stage": "saving", "step": 1, "total": 1, "stage_progress": 100.0, "overall_progress": 100.0, "message": "Saving image to N:\\AIStudio\\output\\text_generated_1756565972473.png", "timestamp": "2025-08-30T10:12:23.461581", "elapsed_time": 150.34}
[2025-08-30T15:12:23.729Z] [STDOUT] DEBUG: Also saved as JPEG: N:\AIStudio\output\text_generated_1756565972473.jpg
[2025-08-30T15:12:23.729Z] [STDOUT] PROGRESS:{"stage": "saving", "step": 1, "total": 1, "stage_progress": 100.0, "overall_progress": 100.0, "message": "Image saved successfully! Total time: 150.6s", "timestamp": "2025-08-30T10:12:23.730438", "elapsed_time": 150.61}
[2025-08-30T15:12:23.797Z] [STDOUT] Successfully generated image: N:\AIStudio\output\text_generated_1756565972473.png
[2025-08-30T15:12:23.797Z] [STDOUT] {"success": true, "output_path": "N:\\AIStudio\\output\\text_generated_1756565972473.png", "model": "sdxl-turbo", "prompt": "A red sports car, isolated object on white background, centered, full view, clean white studio lighting, professional product photography, all parts visible, complete object, no cropping, 3D model reference", "width": 1024, "height": 1024, "steps": 20, "guidance_scale": 7.5, "seed": null, "execution_time": 150.60991525650024}
[2025-08-30T15:12:30.613Z] [STDOUT] Process exited with code: 0
[2025-08-30T15:12:30.614Z] [INFO] Log stream closed

