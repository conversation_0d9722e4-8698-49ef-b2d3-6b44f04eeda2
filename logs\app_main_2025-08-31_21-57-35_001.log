========================================
AIStudio Real-time Log: main
Started: 2025-09-01T02:57:35.366Z
File: app_main_2025-08-31_21-57-35_001.log
========================================

[2025-09-01T02:57:35.625Z] [INFO] AIStudio application started successfully
[2025-09-01T02:57:35.625Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T02:57:35.661Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T02:57:36.684Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-01T02:57:36.685Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                   38408 Console                    1  2,209,780 K
[2025-09-01T02:57:37.108Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-01T02:57:56.831Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_21-57-56_001.log
[2025-09-01T02:57:56.831Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T02:58:20.123Z] [INFO] [RealtimeLogger] Started logging Starting server... to: Starting server..._2025-08-31_21-58-20_001.log
[2025-09-01T02:58:20.123Z] [INFO] [Starting server...] video-server
[2025-09-01T02:58:20.124Z] [INFO] [RealtimeLogger] Started logging RUN_BAT: to: RUN_BAT:_2025-08-31_21-58-20_001.log
[2025-09-01T02:58:20.124Z] [INFO] [RUN_BAT:] video-server
[2025-09-01T02:58:20.125Z] [INFO] [RealtimeLogger] Started logging Batch file exists: to: Batch file exists:_2025-08-31_21-58-20_001.log
[2025-09-01T02:58:20.125Z] [INFO] [Batch file exists:] video-server
[2025-09-01T02:58:20.126Z] [INFO] [RealtimeLogger] Started logging Working directory: to: Working directory:_2025-08-31_21-58-20_001.log
[2025-09-01T02:58:20.127Z] [INFO] [Working directory:] video-server
[2025-09-01T02:58:20.127Z] [INFO] [RealtimeLogger] Started logging Command: cmd.exe /c to: Command: cmd.exe /c_2025-08-31_21-58-20_001.log
[2025-09-01T02:58:20.127Z] [INFO] [Command: cmd.exe /c] video-server
[2025-09-01T02:58:20.162Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-58-20_001.log'
[2025-09-01T02:58:20.162Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-58-20_001.log'
[2025-09-01T02:58:20.163Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-58-20_001.log'
[2025-09-01T02:58:25.170Z] [INFO] [RealtimeLogger] Started logging Status check error: this.isServerRunning is not a function to: Status check error: this.isServerRunning is not a function_2025-08-31_21-58-25_001.log
[2025-09-01T02:58:25.170Z] [INFO] [Status check error: this.isServerRunning is not a function] video-server
[2025-09-01T02:58:34.717Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed! to: [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!_2025-08-31_21-58-34_001.log
[2025-09-01T02:58:34.717Z] [INFO] [[STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!] video-server
[2025-09-01T02:58:34.774Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True) to: [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)_2025-08-31_21-58-34_001.log
[2025-09-01T02:58:34.774Z] [INFO] [[STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)] video-server
[2025-09-01T02:58:35.041Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False to: [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False_2025-08-31_21-58-35_001.log
[2025-09-01T02:58:35.041Z] [INFO] [[STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False] video-server
[2025-09-01T02:58:35.752Z] [INFO] [RealtimeLogger] Started logging [STDERR] Downloading shards: 0%| | 0/4 [00:00<?, ?it/s] to: [STDERR] Downloading shards: 0%| | 0/4 [00:00<?, ?it/s]_2025-08-31_21-58-35_001.log
[2025-09-01T02:58:35.753Z] [INFO] [[STDERR] Downloading shards: 0%| | 0/4 [00:00<?, ?it/s]] video-server
[2025-09-01T02:58:35.754Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Downloading shards: 0%| | 0\4 [00:00<?, ?it\s]_2025-08-31_21-58-35_001.log'
[2025-09-01T02:58:35.754Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Downloading shards: 0%| | 0\4 [00:00<?, ?it\s]_2025-08-31_21-58-35_001.log'
[2025-09-01T02:58:35.754Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Downloading shards: 0%| | 0\4 [00:00<?, ?it\s]_2025-08-31_21-58-35_001.log'
[2025-09-01T02:58:36.809Z] [INFO] [RealtimeLogger] Started logging [STDERR] Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.39it/s]Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.16it/s] to: [STDERR] Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.39it/s]Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.16it/s]_2025-08-31_21-58-36_001.log
[2025-09-01T02:58:36.809Z] [INFO] [[STDERR] Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.39it/s]Loading checkpoint shards: 100%|##########| 4/4 [00:00<00:00, 5.16it/s]] video-server
[2025-09-01T02:58:36.810Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.39it\s]Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.16it\s]_2025-08-31_21-58-36_001.log'
[2025-09-01T02:58:36.810Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.39it\s]Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.16it\s]_2025-08-31_21-58-36_001.log'
[2025-09-01T02:58:36.810Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.39it\s]Loading checkpoint shards: 100%|##########| 4\4 [00:00<00:00, 5.16it\s]_2025-08-31_21-58-36_001.log'
[2025-09-01T02:58:41.942Z] [INFO] [RealtimeLogger] Started logging [STDERR] Fetching 3 files: 0%| | 0/3 [00:00<?, ?it/s] to: [STDERR] Fetching 3 files: 0%| | 0/3 [00:00<?, ?it/s]_2025-08-31_21-58-41_001.log
[2025-09-01T02:58:41.942Z] [INFO] [[STDERR] Fetching 3 files: 0%| | 0/3 [00:00<?, ?it/s]] video-server
[2025-09-01T02:58:41.943Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Fetching 3 files: 0%| | 0\3 [00:00<?, ?it\s]_2025-08-31_21-58-41_001.log'
[2025-09-01T02:58:41.943Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Fetching 3 files: 0%| | 0\3 [00:00<?, ?it\s]_2025-08-31_21-58-41_001.log'
[2025-09-01T02:58:41.944Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] Fetching 3 files: 0%| | 0\3 [00:00<?, ?it\s]_2025-08-31_21-58-41_001.log'
[2025-09-01T02:58:42.173Z] [INFO] [RealtimeLogger] Started logging [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet` to: [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`_2025-08-31_21-58-42_001.log
[2025-09-01T02:58:42.173Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:58:42.192Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:58:42.199Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
