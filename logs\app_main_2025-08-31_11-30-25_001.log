========================================
AIStudio Real-time Log: main
Started: 2025-08-31T16:30:25.109Z
File: app_main_2025-08-31_11-30-25_001.log
========================================

[2025-08-31T16:30:25.329Z] [INFO] AIStudio application started successfully
[2025-08-31T16:30:25.329Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T16:30:25.359Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T16:30:26.307Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T16:30:40.676Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_11-30-40_001.log
[2025-08-31T16:30:40.676Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T16:37:19.506Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_11-37-19_001.log
[2025-08-31T16:37:19.507Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T16:37:19.508Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T16:37:19.508Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T16:37:19.508Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T16:37:19.509Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T16:37:19.510Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_11-37-19_001.log
[2025-08-31T16:37:19.510Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
