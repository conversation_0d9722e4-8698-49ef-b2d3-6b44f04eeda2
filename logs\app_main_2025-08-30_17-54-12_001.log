========================================
AIStudio Real-time Log: main
Started: 2025-08-30T22:54:12.559Z
File: app_main_2025-08-30_17-54-12_001.log
========================================

[2025-08-30T22:54:12.819Z] [INFO] AIStudio application started successfully
[2025-08-30T22:54:12.820Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T22:54:12.858Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T22:54:13.786Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T22:54:38.315Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_17-54-38_001.log
[2025-08-30T22:54:38.315Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
