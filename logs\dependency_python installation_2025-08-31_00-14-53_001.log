========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T05:14:53.252Z
File: dependency_python installation_2025-08-31_00-14-53_001.log
========================================

[2025-08-31T05:14:53.253Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:14:53.254Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:14:53.255Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:14:53.256Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:14:53.256Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
