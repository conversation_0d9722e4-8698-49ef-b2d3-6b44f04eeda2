========================================
AIStudio Real-time Log: main
Started: 2025-08-31T02:05:01.281Z
File: app_main_2025-08-30_21-05-01_001.log
========================================

[2025-08-31T02:05:01.538Z] [INFO] AIStudio application started successfully
[2025-08-31T02:05:01.538Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T02:05:01.569Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T02:05:02.487Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T02:05:15.740Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_21-05-15_001.log
[2025-08-31T02:05:15.741Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T02:05:47.346Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_21-05-47_001.log
[2025-08-31T02:05:47.346Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T02:05:47.347Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T02:05:47.347Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T02:05:47.348Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T02:05:47.348Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T02:05:47.349Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_21-05-47_001.log
[2025-08-31T02:05:47.350Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
