========================================
AIStudio Real-time Log: main
Started: 2025-08-31T06:41:33.976Z
File: app_main_2025-08-31_01-41-33_001.log
========================================

[2025-08-31T06:41:33.988Z] [ERROR] App threw an error during load
[2025-08-31T06:41:33.988Z] [ERROR] N:\AIStudio\src\main\dependencyManager.js:6340
  async _installFramePackModule(pipelineName, component, name) {
        ^^^^^^^^^^^^^^^^^^^^^^^

SyntaxError: Unexpected identifier '_installFramePackModule'
    at wrapSafe (node:internal/modules/cjs/loader:1385:20)
    at Module._compile (node:internal/modules/cjs/loader:1435:41)
    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
    at Module.load (node:internal/modules/cjs/loader:1295:32)
    at Module._load (node:internal/modules/cjs/loader:1111:12)
    at c._load (node:electron/js2c/node_init:2:16955)
    at Module.require (node:internal/modules/cjs/loader:1318:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (N:\AIStudio\src\main\index.js:13:27)
    at Module._compile (node:internal/modules/cjs/loader:1484:14)
[2025-08-31T06:41:33.989Z] [ERROR] Uncaught Exception: SyntaxError: Unexpected identifier '_installFramePackModule'
[2025-08-31T06:41:33.989Z] [ERROR] [main] ERROR: Uncaught Exception: SyntaxError: Unexpected identifier '_installFramePackModule'
[2025-08-31T06:41:33.989Z] [ERROR] Uncaught Exception: SyntaxError: Unexpected identifier '_installFramePackModule'
