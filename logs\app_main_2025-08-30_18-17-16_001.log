========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:17:16.324Z
File: app_main_2025-08-30_18-17-16_001.log
========================================

[2025-08-30T23:17:16.566Z] [INFO] AIStudio application started successfully
[2025-08-30T23:17:16.566Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T23:17:16.601Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T23:17:17.388Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T23:17:34.307Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_18-17-34_001.log
[2025-08-30T23:17:34.307Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
