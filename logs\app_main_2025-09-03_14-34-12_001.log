========================================
AIStudio Real-time Log: main
Started: 2025-09-03T19:34:12.386Z
File: app_main_2025-09-03_14-34-12_001.log
========================================

[2025-09-03T19:34:12.631Z] [INFO] AIStudio application started successfully
[2025-09-03T19:34:12.632Z] [INFO] [main] AIStudio application started successfully
[2025-09-03T19:34:12.661Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-03T19:34:13.693Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-03T19:34:13.837Z] [ERROR] Unhandled Rejection at: [object Promise], reason: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:34:13.837Z] [ERROR] [main] ERROR: Unhandled Rejection at: [object Promise], reason: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:34:13.838Z] [ERROR] Unhandled Rejection: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:34:31.870Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-03_14-34-31_001.log
[2025-09-03T19:34:31.870Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-03T19:35:00.894Z] [ERROR] Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
