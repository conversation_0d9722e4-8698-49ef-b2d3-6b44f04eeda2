========================================
AIStudio Real-time Log: main
Started: 2025-08-31T00:13:28.396Z
File: app_main_2025-08-30_19-13-28_001.log
========================================

[2025-08-31T00:13:28.629Z] [INFO] AIStudio application started successfully
[2025-08-31T00:13:28.630Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T00:13:28.668Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T00:13:29.646Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T00:13:45.975Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_19-13-45_001.log
[2025-08-31T00:13:45.975Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
