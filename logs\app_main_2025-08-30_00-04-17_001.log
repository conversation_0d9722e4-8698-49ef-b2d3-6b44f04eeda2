========================================
AIStudio Real-time Log: main
Started: 2025-08-30T05:04:17.793Z
File: app_main_2025-08-30_00-04-17_001.log
========================================

[2025-08-30T05:04:18.000Z] [INFO] AIStudio application started successfully
[2025-08-30T05:04:18.000Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T05:04:18.067Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T05:04:19.156Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T05:04:36.050Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_00-04-36_001.log
[2025-08-30T05:04:36.050Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T05:04:38.524Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T05:04:38.525Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T05:04:39.539Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
