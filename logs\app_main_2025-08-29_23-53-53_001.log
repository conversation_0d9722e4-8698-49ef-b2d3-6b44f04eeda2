========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:53:53.835Z
File: app_main_2025-08-29_23-53-53_001.log
========================================

[2025-08-30T04:53:54.039Z] [INFO] AIStudio application started successfully
[2025-08-30T04:53:54.039Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:53:54.069Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:53:54.920Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T04:54:11.706Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-54-11_001.log
[2025-08-30T04:54:11.707Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:54:14.007Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:54:14.008Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:54:15.020Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
