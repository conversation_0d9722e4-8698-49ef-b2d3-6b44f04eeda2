========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:31:54.871Z
File: app_main_2025-08-31_20-31-54_001.log
========================================

[2025-09-01T01:31:55.094Z] [INFO] AIStudio application started successfully
[2025-09-01T01:31:55.094Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:31:55.124Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:31:56.135Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:32:15.703Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-32-15_001.log
[2025-09-01T01:32:15.704Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:32:56.436Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T01:32:56.437Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\src\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T01:32:56.440Z] [ERROR] Framepack video generation failed: ReferenceError: fs is not defined
