========================================
AIStudio Real-time Log: main
Started: 2025-09-01T02:38:45.449Z
File: app_main_2025-08-31_21-38-45_001.log
========================================

[2025-09-01T02:38:45.774Z] [INFO] AIStudio application started successfully
[2025-09-01T02:38:45.774Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T02:38:45.824Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T02:38:46.862Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T02:40:18.156Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_21-40-18_001.log
[2025-09-01T02:40:18.156Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T02:40:59.000Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T02:40:59.001Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T02:40:59.003Z] [INFO] [34m[Video Server][39m Batch file exists: true
[2025-09-01T02:40:59.005Z] [INFO] [34m[Video Server][39m Working directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-01T02:40:59.006Z] [INFO] [34m[Video Server][39m Command: cmd.exe /c run.bat
[2025-09-01T02:41:04.058Z] [INFO] [34m[Video Server][39m Status check error: this.isServerRunning is not a function debug
[2025-09-01T02:42:08.998Z] [INFO] [34m[Video Server][39m [STDOUT] Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']
[2025-09-01T02:42:09.000Z] [INFO] [34m[Video Server][39m [STDOUT] Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
[2025-09-01T02:42:09.410Z] [INFO] [34m[Video Server][39m [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)
[2025-09-01T02:42:10.367Z] [INFO] [34m[Video Server][39m [STDOUT] Free VRAM 10.9814453125 GB
High-VRAM Mode: False
[2025-09-01T02:42:11.260Z] [INFO] [34m[Video Server][39m [STDERR] Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]
[2025-09-01T02:42:11.384Z] [INFO] [34m[Video Server][39m [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
[2025-09-01T02:42:59.065Z] [INFO] [34m[Video Server][39m Server startup timed out after 120 seconds. Process output:
[2025-09-01T02:42:08.997Z] [STDOUT] Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']
[2025-09-01T02:42:08.999Z] [STDOUT] Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
[2025-09-01T02:42:09.409Z] [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)
[2025-09-01T02:42:10.366Z] [STDOUT] Free VRAM 10.9814453125 GB
High-VRAM Mode: False
[2025-09-01T02:42:11.259Z] [STDERR] Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]
[2025-09-01T02:42:11.382Z] [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
 error
[2025-09-01T02:42:59.080Z] [ERROR] Framepack video generation failed: Error: Server startup timed out after 120 seconds
[2025-09-01T02:46:05.315Z] [INFO] [34m[Video Server][39m [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.
To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development
  warnings.warn(message)
[2025-09-01T02:46:05.317Z] [INFO] [34m[Video Server][39m [STDERR] Downloading shards:  50%|#####     | 2/4 [03:54<03:54, 117.03s/it]
[2025-09-01T02:46:05.425Z] [INFO] [34m[Video Server][39m [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
