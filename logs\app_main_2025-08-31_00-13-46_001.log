========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:13:46.470Z
File: app_main_2025-08-31_00-13-46_001.log
========================================

[2025-08-31T05:13:46.722Z] [INFO] AIStudio application started successfully
[2025-08-31T05:13:46.722Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:13:46.759Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:13:47.843Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:14:10.832Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-14-10_001.log
[2025-08-31T05:14:10.833Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:14:53.252Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-14-53_001.log
[2025-08-31T05:14:53.253Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:14:53.254Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:14:53.255Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:14:53.256Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:14:53.256Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:14:53.258Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-14-53_001.log
[2025-08-31T05:14:53.259Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
