========================================
AIStudio Real-time Log: main
Started: 2025-08-31T06:06:37.321Z
File: app_main_2025-08-31_01-06-37_001.log
========================================

[2025-08-31T06:06:37.569Z] [INFO] AIStudio application started successfully
[2025-08-31T06:06:37.569Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T06:06:37.600Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T06:06:38.613Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T06:06:55.306Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_01-06-55_001.log
[2025-08-31T06:06:55.307Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T06:07:25.366Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-07-25_001.log
[2025-08-31T06:07:25.366Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:07:25.367Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:07:25.367Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:07:25.368Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:07:25.368Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:07:25.369Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-07-25_001.log
[2025-08-31T06:07:25.370Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T06:08:02.096Z] [INFO] [RealtimeLogger] Started logging dependency_imageedit to: dependency_models installation_2025-08-31_01-08-02_001.log
[2025-08-31T06:08:02.096Z] [INFO] [dependency_imageedit] DependencyManager: Starting models installation for ImageEdit
[2025-08-31T06:08:02.097Z] [INFO] [dependency_imageedit] DependencyManager: Installing dependencies for ImageEdit (models:all)
[2025-08-31T06:08:02.098Z] [INFO] [dependency_imageedit] DependencyManager: Component type: string, Component value: 'models'
[2025-08-31T06:08:02.098Z] [INFO] [dependency_imageedit] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:08:02.099Z] [INFO] [dependency_imageedit] DependencyManager: About to check routing for ImageEdit with component models
