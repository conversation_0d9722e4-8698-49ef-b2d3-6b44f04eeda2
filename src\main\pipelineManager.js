const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const fsPromises = require('fs').promises;
const logger = require('./logger');

class PipelineManager {
    constructor(dependencyManager) {
        this.dependencyManager = dependencyManager;
        this.pipelines = {};
        this.activeProcesses = new Map(); // Track active processes for cancellation
        this.cancelledProcesses = new Set(); // Track processes that were cancelled
    }

    // Utility function to properly escape strings for Python script generation
    escapeForPython(str) {
        if (typeof str !== 'string') return str;
        return str
            .replace(/\\/g, '\\\\')  // Escape backslashes first
            .replace(/"/g, '\\"')    // Escape double quotes
            .replace(/'/g, "\\'")    // Escape single quotes
            .replace(/\n/g, '\\n')   // Escape newlines
            .replace(/\r/g, '\\r')   // Escape carriage returns
            .replace(/\t/g, '\\t');  // Escape tabs
    }

    // Track a process for potential cancellation
    trackProcess(processId, childProcess, type = 'unknown') {
        this.activeProcesses.set(processId, {
            process: childProcess,
            type: type,
            startTime: Date.now()
        });
        logger.info(`Tracking process ${processId} (${type})`);
    }

    // Remove a process from tracking
    untrackProcess(processId) {
        if (this.activeProcesses.has(processId)) {
            logger.info(`Untracking process ${processId}`);
            this.activeProcesses.delete(processId);
        }
        // Also remove from cancelled processes set
        this.cancelledProcesses.delete(processId);
    }

    // Check if a process was cancelled
    wasProcessCancelled(processId) {
        return this.cancelledProcesses.has(processId);
    }

    // Cancel a specific process
    cancelProcess(processId) {
        const processInfo = this.activeProcesses.get(processId);
        if (processInfo) {
            logger.info(`Cancelling process ${processId} (${processInfo.type})`);
            try {
                // Mark this process as cancelled before killing it
                this.cancelledProcesses.add(processId);

                processInfo.process.kill('SIGTERM');
                // If SIGTERM doesn't work, try SIGKILL after a short delay
                setTimeout(() => {
                    if (!processInfo.process.killed) {
                        logger.warn(`Process ${processId} didn't respond to SIGTERM, using SIGKILL`);
                        processInfo.process.kill('SIGKILL');
                    }
                }, 2000);
                this.untrackProcess(processId);
                return true;
            } catch (error) {
                logger.error(`Failed to cancel process ${processId}:`, error);
                return false;
            }
        }
        return false;
    }

    // Cancel all active processes
    async cancelAllProcesses() {
        logger.info(`Cancelling ${this.activeProcesses.size} active processes`);
        const processIds = Array.from(this.activeProcesses.keys());
        let cancelledCount = 0;

        // Cancel tracked processes
        for (const processId of processIds) {
            if (this.cancelProcess(processId)) {
                cancelledCount++;
            }
        }

        // Also cancel Hunyuan server processes
        try {
            const hunyaunServer = require('./hunyaunServer');
            const hunyuanCancelled = await hunyaunServer.cancelHunyuanGeneration();
            if (hunyuanCancelled) {
                cancelledCount++;
                logger.info('Hunyuan server processes cancelled');
            }
        } catch (error) {
            logger.warn('Failed to cancel Hunyuan processes:', error.message);
        }

        return cancelledCount;
    }

    // Cancel image generation processes specifically
    cancelImageGeneration() {
        logger.info('Cancelling image generation processes...');
        const processIds = Array.from(this.activeProcesses.keys());
        let cancelledCount = 0;

        for (const processId of processIds) {
            const processInfo = this.activeProcesses.get(processId);
            if (processInfo && processInfo.type === 'image-generation') {
                if (this.cancelProcess(processId)) {
                    cancelledCount++;
                }
            }
        }

        logger.info(`Cancelled ${cancelledCount} image generation processes`);
        return cancelledCount;
    }

    async initializePipeline(pipelineName) {
        // For pipelines that don't need complex initialization, just mark as initialized
        if (pipelineName === 'ImageGeneration') {
            this.pipelines[pipelineName] = true;
            return true;
        }

        // Get the Python executable from the pipeline's virtual environment
        const pythonExe = this.dependencyManager._getPythonExe(pipelineName);
        const pipelineDir = path.join('pipelines', pipelineName);

        // Import and initialize the pipeline
        const script = `
import sys
import json
from pathlib import Path

try:
    sys.path.append(str(Path("${pipelineDir}").absolute()))
    from trellis_pipeline import TrellisPipeline
    
    # Test pipeline initialization
    pipeline = TrellisPipeline()
    if pipeline.is_available():
        print(json.dumps({"success": True}))
    else:
        print(json.dumps({"success": False, "error": "Pipeline not available"}))
except Exception as e:
    print(json.dumps({"success": False, "error": str(e)}))
`;

        const tempScript = path.join(pipelineDir, 'temp_init.py');
        await fsPromises.writeFile(tempScript, script);

        try {
            const result = await new Promise((resolve, reject) => {
                const process = spawn(pythonExe, [tempScript], {
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    }
                });

                let stdout = '';
                let stderr = '';

                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                process.on('close', (code) => {
                    if (code === 0) {
                        try {
                            const result = JSON.parse(stdout);
                            resolve(result);
                        } catch {
                            reject(new Error(`Failed to parse pipeline output: ${stdout}`));
                        }
                    } else {
                        reject(new Error(`Pipeline initialization failed: ${stderr}`));
                    }
                });
            });

            if (!result.success) {
                throw new Error(result.error || 'Pipeline initialization failed');
            }

            this.pipelines[pipelineName] = true;
            return true;

        } finally {
            // Clean up temp script
            try {
                await fsPromises.unlink(tempScript);
            } catch {}
        }
    }

    async generateTrellis3D(imagePath, outputPath, settings = {}) {
        const PIPELINE_NAME = 'TrellisSource';

        // Ensure pipeline is initialized
        if (!this.pipelines[PIPELINE_NAME]) {
            await this.initializePipeline(PIPELINE_NAME);
        }

        const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
        const pipelineDir = path.join('pipelines', PIPELINE_NAME);

        const script = `
import sys
import json
from pathlib import Path

try:
    sys.path.append(str(Path("${pipelineDir.replace(/\\/g, '/')}").absolute()))
    from trellis_pipeline import TrellisPipeline

    pipeline = TrellisPipeline()
    result = pipeline.generate_3d_from_image(
        "${this.escapeForPython(imagePath.replace(/\\/g, '/'))}",
        "${this.escapeForPython(outputPath.replace(/\\/g, '/'))}",
        ${JSON.stringify(settings)}
    )
    print(json.dumps(result))
except Exception as e:
    print(json.dumps({"success": False, "error": str(e)}))
`;

        const tempScript = path.join(pipelineDir, 'temp_generate.py');
        await fsPromises.writeFile(tempScript, script);

        try {
            return await new Promise((resolve, reject) => {
                const childProcess = spawn(pythonExe, [tempScript], {
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    }
                });

                // Track this process for cancellation
                const processId = `trellis-3d-${Date.now()}`;
                this.trackProcess(processId, childProcess, 'trellis-3d');

                let stdout = '';
                let stderr = '';

                childProcess.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                childProcess.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                childProcess.on('close', (code) => {
                    // Untrack the process when it completes
                    this.untrackProcess(processId);

                    if (code === 0) {
                        try {
                            const result = JSON.parse(stdout);
                            resolve(result);
                        } catch {
                            reject(new Error(`Failed to parse generation output: ${stdout}`));
                        }
                    } else {
                        // Check if this process was cancelled
                        if (this.wasProcessCancelled(processId)) {
                            logger.info(`Trellis 3D generation process ${processId} was cancelled by user`);
                            resolve({
                                success: false,
                                cancelled: true,
                                message: 'Trellis 3D generation cancelled by user'
                            });
                        } else {
                            reject(new Error(`Generation failed: ${stderr}`));
                        }
                    }
                });

                childProcess.on('error', (error) => {
                    // Untrack the process on error
                    this.untrackProcess(processId);
                    reject(error);
                });
            });

        } finally {
            // Clean up temp script
            try {
                await fsPromises.unlink(tempScript);
            } catch {}
        }
    }

    async generateHunyaun3D(imagePath, outputPath, settings = {}) {
        const PIPELINE_NAME = 'Hunyaun3d-2';

        // Use the HunyaunServer for 3D generation
        const hunyaunServer = require('./hunyaunServer');

        try {
            // Generate 3D model using HunyaunServer
            const result = await hunyaunServer.generate3DModel(imagePath, (progress) => {
                // Optional: Handle progress updates if needed
                console.log(`Hunyaun generation progress: ${progress.progress}% - ${progress.message}`);
            }, settings);

            // Handle both old format (string) and new format (object with stats)
            const modelPath = typeof result === 'string' ? result : result.modelPath;
            const generationStats = typeof result === 'object' ? result.generationStats : null;

            const response = {
                success: true,
                model_path: modelPath,
                message: 'Hunyaun 3D model generated successfully'
            };

            if (generationStats) {
                response.generation_stats = generationStats;
            }

            return response;

        } catch (error) {
            console.error('Hunyaun 3D generation failed:', error);
            return {
                success: false,
                error: error.message || 'Unknown error during Hunyaun 3D generation'
            };
        }
    }

    async generateHunyuan3D21(imagePath, outputPath, settings = {}) {
        const PIPELINE_NAME = 'hunyuan3d-2.1-spz-101';

        try {
            // Use the Python API wrapper for Hunyuan3D-2.1
            const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
            const pipelineDir = path.join('pipelines', '3DPipelines', 'gen3d', PIPELINE_NAME);

            // Create a temporary script to run the generation
            const tempScript = path.join(pipelineDir, 'temp_generate.py');
            const scriptContent = `
import sys
import json
sys.path.insert(0, '${pipelineDir.replace(/\\/g, '/')}')
from api_wrapper import Hunyuan3D21Pipeline

def progress_callback(progress, message):
    print(f"PROGRESS: {progress}% - {message}", flush=True)

try:
    pipeline = Hunyuan3D21Pipeline()
    success, message = pipeline.generate_3d(
        image_path='${this.escapeForPython(imagePath)}',
        output_path='${this.escapeForPython(outputPath)}',
        quality_preset='${this.escapeForPython(settings.quality || 'standard')}',
        enable_texture=${settings.enableTexture !== false},
        progress_callback=progress_callback
    )

    result = {
        "success": success,
        "message": message,
        "model_path": "${outputPath}" if success else None
    }
    print("RESULT:", json.dumps(result))

except Exception as e:
    result = {
        "success": False,
        "error": str(e)
    }
    print("RESULT:", json.dumps(result))
`;

            await fsPromises.writeFile(tempScript, scriptContent);

            const result = await new Promise((resolve, reject) => {
                const process = spawn(pythonExe, [tempScript], {
                    cwd: pipelineDir,
                    env: {
                        ...process.env,
                        PYTHONPATH: pipelineDir,
                        PYTHONHOME: ''
                    }
                });

                // Track this process for cancellation
                const processId = `hunyuan3d-21-${Date.now()}`;
                this.trackProcess(processId, process, 'hunyuan3d-21');

                let stdout = '';
                let stderr = '';

                process.stdout.on('data', (data) => {
                    const output = data.toString();
                    stdout += output;

                    // Handle progress updates
                    const progressMatch = output.match(/PROGRESS: (\d+)% - (.+)/);
                    if (progressMatch) {
                        console.log(`Hunyuan3D-2.1 progress: ${progressMatch[1]}% - ${progressMatch[2]}`);
                    }
                });

                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                process.on('close', (code) => {
                    // Untrack the process when it completes
                    this.untrackProcess(processId);

                    // Clean up temp script
                    fsPromises.unlink(tempScript).catch(() => {});

                    if (code === 0) {
                        try {
                            const resultMatch = stdout.match(/RESULT: (.+)/);
                            if (resultMatch) {
                                const result = JSON.parse(resultMatch[1]);
                                resolve(result);
                            } else {
                                resolve({ success: true, message: 'Hunyuan3D-2.1 generation completed' });
                            }
                        } catch {
                            resolve({ success: true, message: 'Hunyuan3D-2.1 generation completed' });
                        }
                    } else {
                        // Check if this process was cancelled
                        if (this.wasProcessCancelled(processId)) {
                            logger.info(`Hunyuan3D-2.1 generation process ${processId} was cancelled by user`);
                            resolve({
                                success: false,
                                cancelled: true,
                                message: 'Hunyuan3D-2.1 generation cancelled by user'
                            });
                        } else {
                            reject(new Error(`Hunyuan3D-2.1 generation failed: ${stderr}`));
                        }
                    }
                });

                process.on('error', (error) => {
                    // Untrack the process on error
                    this.untrackProcess(processId);
                    reject(error);
                });
            });

            return result;

        } catch (error) {
            console.error('Hunyuan3D-2.1 generation failed:', error);
            return {
                success: false,
                error: error.message || 'Unknown error during Hunyuan3D-2.1 generation'
            };
        }
    }

    async generateFramepackVideo(imagePath, prompt, outputPath, settings = {}, progressCallback = null) {
        const PIPELINE_NAME = 'Video Generation';
        const pipelineDir = path.join('pipelines', 'VideoPipelines', 'framepack_cu126_torch26');

        try {
            // Get the Python executable from the pipeline's virtual environment
            const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);

            // Ensure Python executable exists
            if (!fs.existsSync(pythonExe)) {
                throw new Error(`Python executable not found: ${pythonExe}`);
            }

            // Use the direct worker script that exactly replicates demo_gradio.py
            const workerScript = path.join(pipelineDir, 'direct_worker.py');

            // Ensure worker script exists
            if (!fs.existsSync(workerScript)) {
                throw new Error(`Direct worker script not found: ${workerScript}`);
            }

            // Build command line arguments exactly like the working implementation
            const args = [
                'direct_worker.py', // Use relative path since cwd is set to pipelineDir
                '--image_path', imagePath,
                '--prompt', prompt,
                '--output_path', outputPath,
                '--n_prompt', settings.negative_prompt || '',
                '--seed', (settings.seed || 42).toString(),
                '--total_second_length', (settings.duration || 5.0).toString(),
                '--latent_window_size', (settings.latent_window_size || 16).toString(),
                '--steps', (settings.steps || 25).toString(),
                '--cfg', (settings.cfg || 3.0).toString(),
                '--gs', (settings.gs || 10.0).toString(),
                '--rs', (settings.rs || 0.0).toString(),
                '--gpu_memory_preservation', (settings.gpu_memory_preservation || 6.0).toString(),
                '--mp4_crf', (settings.mp4_crf || 16).toString()
            ];

            // Add teacache flag if enabled
            if (settings.use_teacache) {
                args.push('--use_teacache');
            }

            logger.info(`Starting Framepack video generation using exact Gradio method`);
            logger.info(`Command: ${pythonExe} ${args.join(' ')}`);

            return new Promise((resolve, reject) => {
                const process = spawn(pythonExe, args, {
                    cwd: pipelineDir,
                    stdio: ['ignore', 'pipe', 'pipe'],
                    windowsHide: true
                });

                let result = null;
                let hasError = false;

                // Handle stdout (progress and result)
                process.stdout.on('data', (data) => {
                    const lines = data.toString().split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            const jsonData = JSON.parse(line);

                            if (jsonData.type === 'progress' && progressCallback) {
                                // Forward progress to callback
                                progressCallback({
                                    percentage: jsonData.percentage,
                                    message: jsonData.message,
                                    preview: jsonData.preview,
                                    timestamp: jsonData.timestamp
                                });
                            } else if (jsonData.type === 'complete') {
                                result = jsonData;
                            } else if (jsonData.type === 'error') {
                                hasError = true;
                                result = jsonData;
                            } else if (jsonData.type === 'start') {
                                logger.info(`Framepack generation started: ${jsonData.message}`);
                                if (progressCallback) {
                                    progressCallback({
                                        percentage: 0,
                                        message: jsonData.message
                                    });
                                }
                            }
                        } catch (parseError) {
                            // Non-JSON output, log it
                            logger.info(`Framepack output: ${line}`);
                        }
                    }
                });

                // Handle stderr
                process.stderr.on('data', (data) => {
                    const errorOutput = data.toString();
                    logger.error(`Framepack stderr: ${errorOutput}`);
                });

                // Handle process completion
                process.on('close', (code) => {
                    if (hasError || code !== 0) {
                        const error = result?.error || `Process exited with code ${code}`;
                        logger.error(`Framepack generation failed: ${error}`);
                        reject(new Error(error));
                    } else if (result && result.success) {
                        logger.info(`Framepack generation completed: ${result.output_path}`);
                        resolve({
                            success: true,
                            videoPath: result.output_path,
                            message: result.message
                        });
                    } else {
                        reject(new Error('Video generation completed but no valid result received'));
                    }
                });

                // Handle process errors
                process.on('error', (error) => {
                    logger.error(`Framepack process error: ${error.message}`);
                    reject(error);
                });
            });

        } catch (error) {
            logger.error(`Framepack video generation failed: ${error.message}`);
            throw error;
        }
    }




    async generateImage(prompt, outputPath, settings = {}, progressCallback = null) {
        const PIPELINE_NAME = 'ImageGeneration';

        // Mark pipeline as initialized (dependency manager handles dependency checking)
        this.pipelines[PIPELINE_NAME] = true;

        const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
        const pipelineDir = path.join('pipelines', PIPELINE_NAME);
        // Use application-level generation script for portability
        const scriptPath = path.join('utils', 'helpers', 'generate_image.py');

        // Build command line arguments
        const args = [
            scriptPath,
            '--prompt', prompt,
            '--output', outputPath,
            '--model', settings.model || 'sdxl-turbo',
            '--app_root', process.cwd() // Pass current working directory as app root for portability
        ];

        // Add optional arguments
        if (settings.width) args.push('--width', settings.width.toString());
        if (settings.height) args.push('--height', settings.height.toString());
        if (settings.steps) args.push('--steps', settings.steps.toString());
        if (settings.guidance_scale !== undefined) args.push('--guidance_scale', settings.guidance_scale.toString());
        if (settings.seed !== undefined) args.push('--seed', settings.seed.toString());
        if (settings.negative_prompt) args.push('--negative_prompt', settings.negative_prompt);
        
        // SDXL Refiner arguments
        if (settings.use_refiner) args.push('--use_refiner');
        if (settings.refiner_steps) args.push('--refiner_steps', settings.refiner_steps.toString());

        // Preview quality argument
        if (settings.preview_quality) args.push('--preview_quality', settings.preview_quality);

        // Add result file for JSON output (use absolute path for portability)
        const resultFile = path.join(pipelineDir, 'temp_result.json');
        args.push('--result_file', resultFile);

        logger.info(`Starting image generation with command: ${pythonExe} ${args.join(' ')}`);

        // Start realtime logging for image generation
        const realtimeLogger = require('./realtimeLogger');
        const logInfo = realtimeLogger.createLogStream('imagegeneration', 'process_imagegeneration');
        logger.info(`[RealtimeLogger] Started logging imagegeneration to: ${logInfo.fileName}`);

        try {
            return await new Promise((resolve, reject) => {
                const childProcess = spawn(pythonExe, args, {
                    cwd: process.cwd(), // Use application root as working directory
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    },
                    windowsHide: true
                });

                // Track this process for cancellation
                const processId = `image-generation-${Date.now()}`;
                this.trackProcess(processId, childProcess, 'image-generation');

                // Store progress callback for stdout handler
                this.progressCallback = progressCallback;

                let stdout = '';
                let stderr = '';

                childProcess.stdout.on('data', (data) => {
                    const text = data.toString();
                    stdout += text;

                    // Log all stdout to realtime logger
                    const lines = text.split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        if (line.trim()) {
                            logger.info(`ImageGeneration stdout: ${line.trim()}`);
                            realtimeLogger.logProcessOutput('imagegeneration', line.trim(), 'stdout');
                        }
                    }

                    // Parse progress updates from the pipeline
                    for (const line of lines) {
                        if (line.startsWith('PROGRESS:')) {
                            try {
                                const progressJson = line.substring(9); // Remove "PROGRESS:" prefix
                                const progressData = JSON.parse(progressJson);

                                // Emit progress event that can be caught by the main process
                                if (this.progressCallback) {
                                    const callbackData = {
                                        type: 'progress',
                                        stage: progressData.stage,
                                        step: progressData.step,
                                        total: progressData.total,
                                        stage_progress: progressData.stage_progress,
                                        overall_progress: progressData.overall_progress,
                                        message: progressData.message,
                                        timestamp: progressData.timestamp
                                    };

                                    // Handle preview image from base64 data
                                    if (progressData.preview_image) {
                                        // Check if it's a data URL or just base64 string
                                        let base64Data;
                                        if (progressData.preview_image.startsWith('data:')) {
                                            // Extract base64 data from data URI
                                            base64Data = progressData.preview_image.split(',')[1];
                                        } else {
                                            // Already just base64 string
                                            base64Data = progressData.preview_image;
                                        }
                                        // Use preview_image field name to match Main Process expectations
                                        callbackData.preview_image = base64Data;
                                    }
                                    
                                    this.progressCallback(callbackData);
                                }
                            } catch (e) {
                                logger.warn(`Failed to parse progress data: ${line}`);
                            }
                        }
                    }
                });

                childProcess.stderr.on('data', (data) => {
                    const text = data.toString();
                    stderr += text;

                    // Log all stderr to realtime logger
                    const lines = text.split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        if (line.trim()) {
                            logger.info(`ImageGeneration stderr: ${line.trim()}`);
                            realtimeLogger.logProcessOutput('imagegeneration', line.trim(), 'stderr');
                        }
                    }
                });

                childProcess.on('error', (error) => {
                    // Untrack the process on error
                    this.untrackProcess(processId);
                    logger.error(`Image generation process error: ${error.message}`);
                    reject(new Error(`Failed to start image generation process: ${error.message}`));
                });

                childProcess.on('close', async (code) => {
                    try {
                        // Untrack the process when it completes
                        this.untrackProcess(processId);

                        logger.info(`Image generation process exited with code: ${code}`);
                        realtimeLogger.logProcessOutput('imagegeneration', `Process exited with code: ${code}`, 'stdout');

                        // Close the realtime log stream
                        realtimeLogger.closeLogStream('imagegeneration');

                        if (code === 0) {
                            try {
                                // Read result from JSON file (resultFile is already the full path)
                                logger.info(`Reading result file: ${resultFile}`);
                                const resultData = await fsPromises.readFile(resultFile, 'utf8');
                                const result = JSON.parse(resultData);
                                logger.info(`Image generation completed successfully: ${result.output_path}`);
                                logger.info(`Result details: success=${result.success}, model=${result.model}`);
                                realtimeLogger.logProcessOutput('imagegeneration', `Generation completed successfully: ${result.output_path}`, 'stdout');
                                resolve(result);
                            } catch (e) {
                                logger.warn(`Failed to read result file, using fallback: ${e.message}`);
                                // Fallback to stdout if JSON file doesn't exist
                                const fallbackResult = {
                                    success: true,
                                    output_path: outputPath,
                                    stdout: stdout,
                                    stderr: stderr
                                };
                                logger.info(`Using fallback result: ${JSON.stringify(fallbackResult)}`);
                                resolve(fallbackResult);
                            }
                        } else {
                            // Check if this process was cancelled (either explicitly marked or by detecting cancellation signals)
                            const wasCancelled = this.wasProcessCancelled(processId) ||
                                                code === null ||
                                                (stderr && stderr.includes('Process terminated')) ||
                                                (stderr && stderr.includes('KeyboardInterrupt')) ||
                                                (stderr && stderr.includes('SIGTERM'));

                            if (wasCancelled) {
                                logger.info(`Image generation process ${processId} was cancelled by user`);
                                realtimeLogger.logProcessOutput('imagegeneration', 'Generation cancelled by user', 'stdout');
                                resolve({
                                    success: false,
                                    cancelled: true,
                                    message: 'Generation cancelled by user'
                                });
                            } else {
                                // Clean up stderr for actual errors (remove excessive stack traces)
                                let cleanStderr = stderr;
                                if (stderr.length > 1000) {
                                    // Truncate very long error messages and keep only the essential parts
                                    const lines = stderr.split('\n');
                                    const importantLines = lines.filter(line =>
                                        line.includes('Error:') ||
                                        line.includes('Exception:') ||
                                        line.includes('Failed:') ||
                                        line.includes('RuntimeError:') ||
                                        line.includes('ValueError:') ||
                                        line.includes('TypeError:')
                                    ).slice(0, 3); // Keep only first 3 important error lines

                                    if (importantLines.length > 0) {
                                        cleanStderr = importantLines.join('\n');
                                    } else {
                                        cleanStderr = 'Image generation failed with an unknown error';
                                    }
                                }

                                const errorMsg = `Image generation failed with code ${code}: ${cleanStderr}`;
                                logger.error(errorMsg);
                                realtimeLogger.logProcessOutput('imagegeneration', errorMsg, 'stderr');
                                reject(new Error(errorMsg));
                            }
                        }
                    } catch (error) {
                        logger.error(`Error in image generation close handler: ${error.message}`);
                        reject(error);
                    }
                });
            });

        } finally {
            // Clean up temp result file
            try {
                await fsPromises.unlink(resultFile);
            } catch {}
        }
    }
}

module.exports = PipelineManager; 