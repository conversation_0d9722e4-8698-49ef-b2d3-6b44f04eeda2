========================================
AIStudio Real-time Log: main
Started: 2025-08-30T21:44:14.609Z
File: app_main_2025-08-30_16-44-14_001.log
========================================

[2025-08-30T21:44:14.835Z] [INFO] AIStudio application started successfully
[2025-08-30T21:44:14.835Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T21:44:14.868Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T21:44:15.801Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T21:44:29.027Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_16-44-29_001.log
[2025-08-30T21:44:29.027Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T21:44:31.373Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T21:44:31.374Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T21:44:32.389Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
