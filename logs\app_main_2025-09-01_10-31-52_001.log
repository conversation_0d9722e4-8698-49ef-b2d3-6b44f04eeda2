========================================
AIStudio Real-time Log: main
Started: 2025-09-01T15:31:52.436Z
File: app_main_2025-09-01_10-31-52_001.log
========================================

[2025-09-01T15:31:52.748Z] [INFO] AIStudio application started successfully
[2025-09-01T15:31:52.748Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T15:31:52.781Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T15:31:53.824Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T15:33:19.492Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-01_10-33-19_001.log
[2025-09-01T15:33:19.492Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
