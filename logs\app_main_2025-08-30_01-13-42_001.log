========================================
AIStudio Real-time Log: main
Started: 2025-08-30T06:13:42.957Z
File: app_main_2025-08-30_01-13-42_001.log
========================================

[2025-08-30T06:13:43.188Z] [INFO] AIStudio application started successfully
[2025-08-30T06:13:43.188Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T06:13:43.217Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T06:13:44.108Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T06:14:02.801Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_01-14-02_001.log
[2025-08-30T06:14:02.801Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T06:14:07.609Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T06:14:07.610Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T06:14:08.620Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T06:15:17.778Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_dependency check_2025-08-30_01-15-17_001.log
[2025-08-30T06:15:17.778Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting dependency check for ImageGeneration
