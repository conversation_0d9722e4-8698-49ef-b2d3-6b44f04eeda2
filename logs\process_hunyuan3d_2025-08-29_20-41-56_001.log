========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-30T01:41:56.450Z
File: process_hunyuan3d_2025-08-29_20-41-56_001.log
========================================

[2025-08-30T01:41:56.449Z] [STDOUT] N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-30T01:41:56.452Z] [STDOUT] "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
"N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-30T01:42:04.324Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-30T01:42:04.325Z] [STDOUT] "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-08-30T01:42:17.621Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 11801ms
  creator CPython3Windows(dest=N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.2, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-08-30T01:42:17.695Z] [STDOUT] 1 file(s) copied.
[2025-08-30T01:42:17.932Z] [STDOUT] Portable Python located at: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-08-30T01:42:17.933Z] [STDOUT] Virtual environment Python set to: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-30T01:42:17.936Z] [STDOUT] _
[2025-08-30T01:42:17.938Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-30T01:42:17.939Z] [STDOUT] Virtual Env: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-30T01:42:17.939Z] [STDOUT] _
[2025-08-30T01:42:17.948Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-30T01:42:17.949Z] [STDOUT] Virtual Env: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-30T01:42:17.950Z] [STDOUT] Starting the server, please wait...
[2025-08-30T01:45:39.050Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-08-30T01:45:39.051Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
[2025-08-30T01:45:39.382Z] [STDERR] N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Lib\site-packages\huggingface_hub\file_download.py:142: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.
To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development
  warnings.warn(message)
[2025-08-30T01:46:21.889Z] [STDERR] Fetching 13 files:  38%|###8      | 5/13 [00:42<01:12,  9.09s/it]
[2025-08-30T01:48:11.254Z] [STDERR] Fetching 13 files:  85%|########4 | 11/13 [02:32<00:29, 14.99s/it]
Fetching 13 files: 100%|##########| 13/13 [02:32<00:00, 11.71s/it]
[2025-08-30T01:48:11.409Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-08-30T01:50:02.543Z] [STDERR] Fetching 17 files:  35%|###5      | 6/17 [01:51<03:34, 19.47s/it]
[2025-08-30T01:53:25.549Z] [STDERR] Fetching 17 files:  71%|#######   | 12/17 [05:14<02:20, 28.10s/it]
Fetching 17 files: 100%|##########| 17/17 [05:14<00:00, 18.48s/it]
[2025-08-30T01:53:26.840Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-30T01:53:28.971Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:02<00:10,  2.13s/it]
[2025-08-30T01:53:29.088Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:02<00:01,  1.68it/s]
[2025-08-30T01:53:35.508Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:08<00:00,  1.88s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:08<00:00,  1.44s/it]
[2025-08-30T01:53:40.490Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-30T01:53:43.906Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:03<00:17,  3.42s/it]
[2025-08-30T01:54:12.247Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:31<00:00,  6.52s/it]
[2025-08-30T01:54:12.249Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:31<00:00,  5.29s/it]
[2025-08-30T01:54:45.176Z] [STDERR] 2025-08-29 20:54:45,174 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0-turbo
[2025-08-30T01:54:45.177Z] [STDERR] 2025-08-29 20:54:45,176 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-30T01:54:45.496Z] [STDERR] Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
[2025-08-30T02:00:01.864Z] [STDERR] Fetching 3 files:  67%|######6   | 2/3 [05:16<03:06, 186.04s/it]
[2025-08-30T02:00:05.950Z] [STDERR] Fetching 3 files: 100%|##########| 3/3 [05:20<00:00, 102.95s/it]
Fetching 3 files: 100%|##########| 3/3 [05:20<00:00, 106.81s/it]
2025-08-29 21:00:05,946 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2\snapshots\0798d3451a19a46491e9ba8ed9b935274ad70c9f\hunyuan3d-dit-v2-0-turbo\model.fp16.safetensors
[2025-08-30T02:01:04.029Z] [STDERR] 2025-08-29 21:01:04,029 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2\hunyuan3d-vae-v2-0-turbo
[2025-08-30T02:01:04.031Z] [STDERR] 2025-08-29 21:01:04,029 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-30T02:01:04.321Z] [STDERR] Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
[2025-08-30T02:01:29.899Z] [STDERR] Fetching 3 files:  67%|######6   | 2/3 [00:25<00:14, 14.96s/it]
[2025-08-30T02:01:30.949Z] [STDERR] Fetching 3 files: 100%|##########| 3/3 [00:26<00:00,  8.61s/it]
Fetching 3 files: 100%|##########| 3/3 [00:26<00:00,  8.88s/it]
[2025-08-30T02:01:30.954Z] [STDERR] 2025-08-29 21:01:30,954 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2\snapshots\0798d3451a19a46491e9ba8ed9b935274ad70c9f\hunyuan3d-vae-v2-0-turbo\model.fp16.safetensors
[2025-08-30T02:01:36.317Z] [STDERR] INFO:     Started server process [32576]
INFO:     Waiting for application startup.
[2025-08-30T02:01:36.319Z] [STDERR] INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
[2025-08-30T02:01:36.434Z] [STDOUT] After launched, open a browser and enter 0.0.0.0:8080 into url, as if it was a website:
INFO:     127.0.0.1:54907 - "GET / HTTP/1.1" 200 OK
[2025-08-30T02:01:36.533Z] [STDOUT] INFO:     127.0.0.1:54907 - "POST /upload HTTP/1.1" 200 OK
[2025-08-30T02:01:46.618Z] [STDOUT] Created new folder: gradio_cache\76d08c8d-7afa-4655-9a7c-f10f90b897d8
[2025-08-30T02:01:46.619Z] [STDERR] Diffusion Sampling::   0%|          | 0/25 [00:00<?, ?it/s]
[2025-08-30T02:01:49.798Z] [STDERR] Diffusion Sampling::  20%|##        | 5/25 [00:03<00:11,  1.81it/s]
[2025-08-30T02:01:52.391Z] [STDERR] Diffusion Sampling::  40%|####      | 10/25 [00:05<00:07,  1.93it/s]
[2025-08-30T02:01:54.960Z] [STDERR] Diffusion Sampling::  60%|######    | 15/25 [00:08<00:05,  1.95it/s]
[2025-08-30T02:01:57.531Z] [STDERR] Diffusion Sampling::  80%|########  | 20/25 [00:10<00:02,  1.95it/s]
[2025-08-30T02:02:00.112Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:13<00:00,  1.94it/s]
[2025-08-30T02:02:00.114Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:13<00:00,  1.85it/s]
[2025-08-30T02:02:00.177Z] [STDERR] 2025-08-29 21:02:00,178 - hy3dgen.shapgen - INFO - FlashVDMVolumeDecoding Resolution: [63, 126, 252]
[2025-08-30T02:02:00.560Z] [STDERR] FlashVDM Volume Decoding:   0%|          | 0/64 [00:00<?, ?it/s]
[2025-08-30T02:02:01.306Z] [STDERR] FlashVDM Volume Decoding: 100%|##########| 64/64 [00:00<00:00, 85.84it/s]
[2025-08-30T02:02:05.515Z] [STDERR] 2025-08-29 21:02:05,515 - hy3dgen.shapgen - INFO - ---Shape generation takes 23.851996183395386 seconds ---
[2025-08-30T02:02:07.526Z] [STDERR] 2025-08-29 21:02:07,526 - hy3dgen.shapgen - INFO - ---Postprocessing takes 1.5810885429382324 seconds ---
[2025-08-30T02:02:14.037Z] [STDERR] 2025-08-29 21:02:14,038 - hy3dgen.shapgen - INFO - ---Face Reduction takes 6.512263774871826 seconds ---
[2025-08-30T02:03:32.956Z] [STDERR] 2025-08-29 21:03:32,955 - hy3dgen.shapgen - INFO - ---Texture Generation takes 78.91729617118835 seconds ---
[2025-08-30T02:03:35.017Z] [STDOUT] Find html file gradio_cache\76d08c8d-7afa-4655-9a7c-f10f90b897d8\textured_mesh.html, True, relative HTML path is /static/76d08c8d-7afa-4655-9a7c-f10f90b897d8\textured_mesh.html
INFO:     127.0.0.1:54908 - "POST /api/predict HTTP/1.1" 200 OK
[2025-08-30T02:03:35.036Z] [STDOUT] INFO:     127.0.0.1:55061 - "GET /file%3DC%3A/Users/<USER>/AppData/Local/Temp/gradio/b87f0082ed9e139716087b128273e0adb80a5e02d52f1c17e14eddca6d7b8e73/textured_mesh.glb HTTP/1.1" 200 OK
[2025-08-30T02:03:40.998Z] [STDOUT] Something went wrong, consider removing code/hunyuan_init_done.txt and the venv folder to re-initialize from scratch
[2025-08-30T02:03:40.999Z] [STDOUT] Press any key to continue . . .
