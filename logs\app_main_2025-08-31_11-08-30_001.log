========================================
AIStudio Real-time Log: main
Started: 2025-08-31T16:08:30.887Z
File: app_main_2025-08-31_11-08-30_001.log
========================================

[2025-08-31T16:08:31.773Z] [INFO] AIStudio application started successfully
[2025-08-31T16:08:31.774Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T16:08:31.963Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T16:08:33.279Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T16:09:00.288Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_11-09-00_001.log
[2025-08-31T16:09:00.289Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
