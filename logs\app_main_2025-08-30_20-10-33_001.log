========================================
AIStudio Real-time Log: main
Started: 2025-08-31T01:10:33.543Z
File: app_main_2025-08-30_20-10-33_001.log
========================================

[2025-08-31T01:10:33.758Z] [INFO] AIStudio application started successfully
[2025-08-31T01:10:33.758Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T01:10:33.791Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T01:10:34.729Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T01:10:47.751Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_20-10-47_001.log
[2025-08-31T01:10:47.751Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T01:12:40.616Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_20-12-40_001.log
[2025-08-31T01:12:40.616Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:12:40.617Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:12:40.617Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:12:40.618Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:12:40.618Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T01:12:40.620Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_20-12-40_001.log
[2025-08-31T01:12:40.620Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
