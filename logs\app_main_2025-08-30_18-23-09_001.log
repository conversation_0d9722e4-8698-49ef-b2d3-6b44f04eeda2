========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:23:09.038Z
File: app_main_2025-08-30_18-23-09_001.log
========================================

[2025-08-30T23:23:09.277Z] [INFO] AIStudio application started successfully
[2025-08-30T23:23:09.277Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T23:23:09.317Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T23:23:10.160Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T23:23:29.195Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_18-23-29_001.log
[2025-08-30T23:23:29.195Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
