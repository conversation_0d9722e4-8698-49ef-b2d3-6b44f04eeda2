========================================
AIStudio Real-time Log: main
Started: 2025-08-30T00:06:02.681Z
File: app_main_2025-08-29_19-06-02_001.log
========================================

[2025-08-30T00:06:02.902Z] [INFO] AIStudio application started successfully
[2025-08-30T00:06:02.903Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T00:06:02.941Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T00:06:03.695Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T00:06:17.357Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_19-06-17_001.log
[2025-08-30T00:06:17.357Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T00:06:19.994Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T00:06:19.996Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T00:06:20.999Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T00:07:27.093Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_python installation_2025-08-29_19-07-27_001.log
[2025-08-30T00:07:27.094Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting python installation for ImageGeneration
[2025-08-30T00:07:27.095Z] [INFO] [dependency_imagegeneration] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-08-30T00:07:27.095Z] [INFO] [dependency_imagegeneration] DependencyManager: Component type: string, Component value: 'python'
[2025-08-30T00:07:27.095Z] [INFO] [dependency_imagegeneration] DependencyManager: Name type: string, Name value: 'all'
[2025-08-30T00:07:27.096Z] [INFO] [dependency_imagegeneration] DependencyManager: About to check routing for ImageGeneration with component python
[2025-08-30T00:07:27.097Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_bundled installation_2025-08-29_19-07-27_001.log
[2025-08-30T00:07:27.097Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:07:27.098Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:07:27.098Z] [INFO] [dependency_imagegeneration] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:07:27.098Z] [INFO] [dependency_imagegeneration] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:07:27.099Z] [INFO] [dependency_imagegeneration] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:07:27.099Z] [INFO] [dependency_imagegeneration] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:07:27.116Z] [INFO] [dependency_imagegeneration] DependencyManager: Existing directory removed successfully
[2025-08-30T00:07:27.118Z] [INFO] [dependency_imagegeneration] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:07:27.119Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
