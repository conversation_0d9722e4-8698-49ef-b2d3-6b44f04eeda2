========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-08-30T00:10:48.459Z
File: dependency_bundled installation_2025-08-29_19-10-48_001.log
========================================

[2025-08-30T00:10:48.459Z] [INFO] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:10:48.459Z] [INFO] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:10:48.460Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:10:48.460Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:10:48.461Z] [INFO] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:10:48.462Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:10:48.472Z] [INFO] DependencyManager: Existing directory removed successfully
[2025-08-30T00:10:48.473Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:10:48.474Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
