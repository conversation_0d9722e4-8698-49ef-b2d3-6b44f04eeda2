{"name": "Video Generation", "description": "Advanced video generation and editing using FramePack and HunyuanVideo", "dependencies": {"bundle": {"type": "bundled", "sourcePath": "N:\\AIStudio\\src\\module_source\\VideoGen\\framepack_cu126_torch26.zip", "targetPath": "N:\\AIStudio\\pipelines\\VideoPipelines"}, "python": {"framepack": {"name": "FramePack Server", "description": "FramePack API server with integrated environment", "installed": false, "required": "Bundled", "satisfied": false}}, "models": {"framepack-base": {"name": "FramePack Base Model", "description": "Core FramePack model for video generation", "installed": false, "required": true}, "framepack-hunyuan": {"name": "HunyuanVideo Integration", "description": "Enhanced video generation with HunyuanVideo", "installed": false, "required": true}}}, "features": ["Text-to-Video Generation", "Image-to-Video Generation", "Video-to-Video Translation", "High-Quality Video Output", "Support for Multiple Video Formats"]}