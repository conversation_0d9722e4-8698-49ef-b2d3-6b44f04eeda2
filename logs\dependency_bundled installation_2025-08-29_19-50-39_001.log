========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-08-30T00:50:39.104Z
File: dependency_bundled installation_2025-08-29_19-50-39_001.log
========================================

[2025-08-30T00:50:39.105Z] [INFO] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:50:39.105Z] [INFO] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:50:39.106Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:50:39.106Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:50:39.106Z] [INFO] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:50:39.107Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:50:39.115Z] [INFO] DependencyManager: Existing directory removed successfully
[2025-08-30T00:50:39.117Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:50:39.118Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
[2025-08-30T00:50:39.119Z] [ERROR] DependencyManager: Failed to extract zip file: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:50:39.121Z] [ERROR] DependencyManager: Stack: TypeError [ERR_INVALID_ARG_TYPE]: The "cb" argument must be of type function. Received undefined
    at makeCallback (node:fs:185:3)
    at mkdir (node:fs:1337:14)
    at t.mkdir (node:electron/js2c/node_init:2:16026)
    at DependencyManager._installImageGenerationModule (N:\AIStudio\src\main\dependencyManager.js:5797:18)
    at async DependencyManager.installDependencies (N:\AIStudio\src\main\dependencyManager.js:677:14)
    at async N:\AIStudio\src\main\index.js:1939:5
    at async WebContents.<anonymous> (node:electron/js2c/browser_init:2:83724)
[2025-08-30T00:50:39.122Z] [ERROR] DependencyManager: Error during installation: Failed to extract ImageGeneration module: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:50:39.122Z] [ERROR] DependencyManager: Failed bundled installation for ImageGeneration

========================================
Log ended: 2025-08-30T00:50:40.143Z
========================================
