{"name": "Hunyuan3D", "description": "Tencent Hunyuan3D - Production-ready 3D generation with PBR materials", "features": ["Single image to 3D", "PBR materials", "Production-ready assets", "High-quality textures", "GLB export", "Multiple quality presets"], "dependencies": {"python": ["diffusers>=0.30.0", "transformers>=4.44.0", "trimesh[easy]>=4.4.9", "xatlas>=0.0.9", "Pillow>=10.0.0", "opencv-python>=4.8.0", "numpy>=1.24.0", "scipy>=1.11.0", "requests>=2.31.0", "accelerate>=1.1.1", "huggingface-hub>=0.30.2", "safetensors>=0.4.4", "einops>=0.8.0", "imageio>=2.36.0", "scikit-image>=0.24.0", "rembg>=2.0.65", "pymeshlab>=2022.2.post3", "pygltflib>=1.16.3", "omegaconf>=2.3.0", "pyyaml>=6.0.2", "fastapi>=0.115.12", "uvicorn>=0.34.3", "tqdm>=4.66.5", "psutil>=6.0.0", "pydantic>=2.10.6", "timm"], "special_packages": [{"name": "pytorch-cuda", "description": "PyTorch with CUDA support", "install_command": "pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124", "required": true}], "models": [{"name": "Hunyuan3D Shape Model", "description": "Hunyuan3D shape generation model", "repo_id": "tencent/Hunyuan3D-2.1", "subfolder": "hunyuan3d-dit-v2-1", "local_path": "Hunyuan3D/tencent--Hunyuan3D-2.1/hunyuan3d-dit-v2-1", "size": "~8GB", "required": true, "auto_download": true}, {"name": "Hunyuan3D Texture Model", "description": "Hunyuan3D PBR texture generation model", "repo_id": "tencent/Hunyuan3D-2.1", "subfolder": "hunyuan3d-paintpbr-v2-1", "local_path": "Hunyuan3D/tencent--Hunyuan3D-2.1/hunyuan3d-paintpbr-v2-1", "size": "~4GB", "required": true, "auto_download": true}, {"name": "realesrgan-x4plus", "description": "Real-ESRGAN model for texture upscaling", "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth", "local_path": "upscaling/RealESRGAN_x4plus.pth", "size": "~64MB", "required": false, "auto_download": true}], "post_install": {"script": "install_official_hunyuan.bat", "description": "Install official Hunyuan3D repository and C++ extensions", "type": "batch"}}}