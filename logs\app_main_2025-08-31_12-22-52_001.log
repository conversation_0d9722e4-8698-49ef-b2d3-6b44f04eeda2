========================================
AIStudio Real-time Log: main
Started: 2025-08-31T17:22:52.072Z
File: app_main_2025-08-31_12-22-52_001.log
========================================

[2025-08-31T17:22:52.339Z] [INFO] AIStudio application started successfully
[2025-08-31T17:22:52.339Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T17:22:52.370Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T17:22:53.668Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T17:23:07.621Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_12-23-07_001.log
[2025-08-31T17:23:07.622Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T17:26:11.860Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_12-26-11_001.log
[2025-08-31T17:26:11.860Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:26:11.861Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:26:11.861Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:26:11.862Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:26:11.863Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T17:26:11.864Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_12-26-11_001.log
[2025-08-31T17:26:11.864Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
