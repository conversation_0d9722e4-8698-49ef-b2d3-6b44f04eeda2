========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:20:04.781Z
File: app_main_2025-08-31_20-20-04_001.log
========================================

[2025-09-01T01:20:05.032Z] [INFO] AIStudio application started successfully
[2025-09-01T01:20:05.032Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:20:05.066Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:20:06.012Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:21:11.647Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-21-11_001.log
[2025-09-01T01:21:11.647Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:22:05.973Z] [ERROR] Uncaught Exception: Error: spawn C:\WINDOWS\system32\cmd.exe ENOENT
[2025-09-01T01:22:05.973Z] [ERROR] [main] ERROR: Uncaught Exception: Error: spawn C:\WINDOWS\system32\cmd.exe ENOENT
[2025-09-01T01:22:05.974Z] [ERROR] Uncaught Exception: Error: spawn C:\WINDOWS\system32\cmd.exe ENOENT
[2025-09-01T01:22:16.124Z] [ERROR] Framepack video generation failed: Error: connect ECONNREFUSED 127.0.0.1:7860
