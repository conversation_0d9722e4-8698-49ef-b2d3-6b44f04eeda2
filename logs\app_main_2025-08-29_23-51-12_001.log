========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:51:12.354Z
File: app_main_2025-08-29_23-51-12_001.log
========================================

[2025-08-30T04:51:12.598Z] [INFO] AIStudio application started successfully
[2025-08-30T04:51:12.598Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:51:12.631Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:51:13.582Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T04:51:31.124Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-51-31_001.log
[2025-08-30T04:51:31.125Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:51:33.491Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:51:33.493Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:51:34.504Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
