========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T18:47:32.707Z
File: dependency_python installation_2025-08-31_13-47-32_001.log
========================================

[2025-08-31T18:47:32.707Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T18:47:32.708Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T18:47:32.709Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T18:47:32.709Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T18:47:32.710Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
