========================================
AIStudio Real-time Log: main
Started: 2025-09-03T19:23:25.027Z
File: app_main_2025-09-03_14-23-25_001.log
========================================

[2025-09-03T19:23:25.198Z] [ERROR] App threw an error during load
[2025-09-03T19:23:25.200Z] [ERROR] Error: Attempted to register a second handler for 'resolve-video-path'
    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:101132)
    at Object.<anonymous> (N:\AIStudio\src\main\index.js:1706:9)
    at Module._compile (node:internal/modules/cjs/loader:1484:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
    at Module.load (node:internal/modules/cjs/loader:1295:32)
    at Module._load (node:internal/modules/cjs/loader:1111:12)
    at c._load (node:electron/js2c/node_init:2:16955)
    at cjsLoader (node:internal/modules/esm/translators:350:17)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:286:7)
    at ModuleJob.run (node:internal/modules/esm/module_job:234:25)
[2025-09-03T19:23:25.200Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:23:25.200Z] [ERROR] [main] ERROR: Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:23:25.201Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T19:23:25.299Z] [INFO] AIStudio application started successfully
[2025-09-03T19:23:25.299Z] [INFO] [main] AIStudio application started successfully
[2025-09-03T19:23:25.336Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-03T19:23:26.280Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-03T19:23:45.066Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-03_14-23-45_001.log
[2025-09-03T19:23:45.066Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-03T19:26:23.109Z] [ERROR] Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
[2025-09-03T19:28:22.974Z] [ERROR] Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
