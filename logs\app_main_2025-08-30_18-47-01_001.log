========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:47:01.493Z
File: app_main_2025-08-30_18-47-01_001.log
========================================

[2025-08-30T23:47:01.721Z] [INFO] AIStudio application started successfully
[2025-08-30T23:47:01.722Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T23:47:01.758Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T23:47:02.673Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T23:47:19.450Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_18-47-19_001.log
[2025-08-30T23:47:19.450Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
