========================================
AIStudio Real-time Log: main
Started: 2025-09-01T02:48:24.571Z
File: app_main_2025-08-31_21-48-24_001.log
========================================

[2025-09-01T02:48:24.816Z] [INFO] AIStudio application started successfully
[2025-09-01T02:48:24.816Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T02:48:24.847Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T02:48:25.740Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-01T02:48:25.740Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                   37176 Console                    1    761,692 K
[2025-09-01T02:48:26.149Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-01T02:48:46.122Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_21-48-46_001.log
[2025-09-01T02:48:46.122Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T02:50:26.571Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T02:50:26.573Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T02:50:26.573Z] [INFO] [34m[Video Server][39m Batch file exists: true
[2025-09-01T02:50:26.575Z] [INFO] [34m[Video Server][39m Working directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-01T02:50:26.575Z] [INFO] [34m[Video Server][39m Command: cmd.exe /c run.bat
[2025-09-01T02:50:31.614Z] [INFO] [34m[Video Server][39m Status check error: this.isServerRunning is not a function debug
[2025-09-01T02:50:41.148Z] [INFO] [34m[Video Server][39m [STDOUT] Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']
[2025-09-01T02:50:41.152Z] [INFO] [34m[Video Server][39m [STDOUT] Xformers is not installed!
Flash Attn is not installed!
Sage Attn is not installed!
[2025-09-01T02:50:41.207Z] [INFO] [34m[Video Server][39m [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)
[2025-09-01T02:50:41.505Z] [INFO] [34m[Video Server][39m [STDOUT] Free VRAM 10.9814453125 GB
High-VRAM Mode: False
[2025-09-01T02:50:41.997Z] [INFO] [34m[Video Server][39m [STDERR] Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]
[2025-09-01T02:50:42.096Z] [INFO] [34m[Video Server][39m [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
