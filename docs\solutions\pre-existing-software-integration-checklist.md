# Pre-existing Software Integration Checklist

## Phase 1: Analysis and Discovery

### ✅ Source Code Analysis
- [ ] Identify the main entry point (e.g., `run.bat`, `main.py`, `demo.py`)
- [ ] Document the working directory structure
- [ ] Map all import dependencies and module paths
- [ ] Note environment variable requirements
- [ ] Identify the exact Python executable used
- [ ] Document any special initialization sequences

### ✅ Environment Analysis
- [ ] Check virtual environment setup
- [ ] Document Python version and package versions
- [ ] Note any system-specific dependencies
- [ ] Identify required environment variables
- [ ] Check for any special PATH requirements
- [ ] Document working directory expectations

### ✅ Execution Flow Analysis
- [ ] Trace the exact execution path from start to finish
- [ ] Identify all directory changes (`os.chdir()`)
- [ ] Note any subprocess calls or process spawning
- [ ] Document memory management strategies
- [ ] Identify progress reporting mechanisms
- [ ] Map error handling approaches

## Phase 2: Direct Replication

### ✅ Create Direct Worker Script
- [ ] Copy the exact working function/class
- [ ] Maintain identical import structure
- [ ] Preserve working directory changes
- [ ] Keep same environment variable setup
- [ ] Use identical model loading sequence
- [ ] Maintain same error handling

### ✅ Environment Setup
```python
# Template for environment setup
import sys
import os
from pathlib import Path

# Set working directory (if required)
script_dir = Path(__file__).parent
target_dir = script_dir / "target_subdir"
os.chdir(str(target_dir))

# Set environment variables (if required)
os.environ['SPECIAL_VAR'] = 'value'

# Add to Python path (if required)
sys.path.append(str(script_dir))
sys.path.append(str(target_dir))
```

### ✅ Command Line Interface
- [ ] Create argument parser for all settings
- [ ] Support all original configuration options
- [ ] Maintain same default values
- [ ] Add JSON output for progress/results
- [ ] Handle relative vs absolute paths correctly

## Phase 3: Integration

### ✅ Process Manager Integration
```javascript
// Template for process manager method
async generateWithExternalTool(inputPath, outputPath, settings = {}, progressCallback = null) {
    const PIPELINE_NAME = 'ToolName';
    const pipelineDir = path.join('pipelines', 'ToolDirectory');
    
    try {
        // Get Python executable
        const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
        
        // Verify paths exist
        if (!fs.existsSync(pythonExe)) {
            throw new Error(`Python executable not found: ${pythonExe}`);
        }
        
        const workerScript = path.join(pipelineDir, 'direct_worker.py');
        if (!fs.existsSync(workerScript)) {
            throw new Error(`Worker script not found: ${workerScript}`);
        }
        
        // Build arguments
        const args = [
            'direct_worker.py', // Relative path
            '--input_path', inputPath,
            '--output_path', outputPath,
            // ... other settings
        ];
        
        // Execute
        return new Promise((resolve, reject) => {
            const process = spawn(pythonExe, args, {
                cwd: pipelineDir,
                stdio: ['ignore', 'pipe', 'pipe'],
                windowsHide: true
            });
            
            // Handle stdout/stderr and completion
            // ...
        });
    } catch (error) {
        logger.error(`Tool execution failed: ${error.message}`);
        throw error;
    }
}
```

### ✅ Path Management
- [ ] Use relative script paths when `cwd` is set
- [ ] Create output directories before file operations
- [ ] Convert relative paths to absolute when needed
- [ ] Handle path separators correctly across platforms
- [ ] Validate input/output paths exist

### ✅ Error Handling
- [ ] Capture and parse JSON error messages
- [ ] Provide user-friendly error messages
- [ ] Log detailed errors for debugging
- [ ] Handle common exit codes appropriately
- [ ] Clean up temporary files on errors

## Phase 4: Testing and Validation

### ✅ Unit Testing
- [ ] Test original implementation works
- [ ] Test direct worker script independently
- [ ] Test integration through application
- [ ] Test with various input types/sizes
- [ ] Test error conditions and recovery

### ✅ Performance Testing
- [ ] Compare execution time with original
- [ ] Monitor memory usage patterns
- [ ] Test with different hardware configurations
- [ ] Validate output quality matches original
- [ ] Test concurrent execution if needed

### ✅ Integration Testing
- [ ] Test progress reporting works
- [ ] Test cancellation functionality
- [ ] Test with different user settings
- [ ] Test file cleanup on completion/failure
- [ ] Test with edge cases (empty inputs, large files, etc.)

## Common Issues and Solutions

### Issue: Module Import Errors
```python
# Solution: Match exact working directory and paths
import sys
from pathlib import Path

# Add paths exactly as the working implementation does
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / "submodule"))
```

### Issue: Path Duplication
```javascript
// Problem: Path gets duplicated
const process = spawn(pythonExe, [path.join(pipelineDir, 'script.py')], {
    cwd: pipelineDir  // Results in: pipelineDir/pipelineDir/script.py
});

// Solution: Use relative path
const process = spawn(pythonExe, ['script.py'], {
    cwd: pipelineDir  // Results in: pipelineDir/script.py
});
```

### Issue: File System Operations
```python
# Solution: Always ensure directories exist
from pathlib import Path
import shutil

def safe_copy(source, destination):
    dest_path = Path(destination).resolve()
    dest_path.parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(source, str(dest_path))
    return str(dest_path)
```

### Issue: Environment Variables
```python
# Solution: Set exactly as working implementation
import os

# Check what the working implementation sets
os.environ['HF_HOME'] = os.path.abspath('./hf_download')
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
# etc.
```

## Success Criteria

### ✅ Functional Success
- [ ] Original functionality works unchanged
- [ ] Integration provides same results
- [ ] Performance is comparable
- [ ] Error handling is robust
- [ ] Progress reporting works

### ✅ Code Quality
- [ ] Code is maintainable and documented
- [ ] Error messages are user-friendly
- [ ] Logging is comprehensive
- [ ] Resource cleanup is proper
- [ ] Configuration is flexible

### ✅ User Experience
- [ ] Integration feels seamless
- [ ] Progress feedback is clear
- [ ] Errors are actionable
- [ ] Performance is acceptable
- [ ] Results are reliable

## Maintenance Considerations

### ✅ Version Management
- [ ] Pin exact package versions that work
- [ ] Document any version-specific requirements
- [ ] Test updates in isolated environment first
- [ ] Maintain compatibility with original implementation

### ✅ Documentation
- [ ] Document the integration approach
- [ ] Explain any deviations from original
- [ ] Provide troubleshooting guide
- [ ] Document configuration options
- [ ] Include example usage

This checklist ensures systematic integration of pre-existing software while maintaining reliability and functionality.
