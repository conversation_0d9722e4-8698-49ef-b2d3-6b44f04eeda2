========================================
AIStudio Real-time Log: main
Started: 2025-08-31T04:45:46.476Z
File: app_main_2025-08-30_23-45-46_001.log
========================================

[2025-08-31T04:45:46.709Z] [INFO] AIStudio application started successfully
[2025-08-31T04:45:46.709Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T04:45:46.739Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T04:45:47.696Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T04:46:01.671Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_23-46-01_001.log
[2025-08-31T04:46:01.671Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T04:46:31.744Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_23-46-31_001.log
[2025-08-31T04:46:31.744Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T04:46:31.745Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T04:46:31.745Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T04:46:31.746Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T04:46:31.746Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T04:46:31.747Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_23-46-31_001.log
[2025-08-31T04:46:31.747Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
