========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:43:00.364Z
File: app_main_2025-08-31_00-43-00_001.log
========================================

[2025-08-31T05:43:00.646Z] [INFO] AIStudio application started successfully
[2025-08-31T05:43:00.646Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:43:00.677Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:43:01.619Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:43:18.512Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-43-18_001.log
[2025-08-31T05:43:18.512Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:44:10.559Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-44-10_001.log
[2025-08-31T05:44:10.559Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:44:10.560Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:44:10.560Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:44:10.560Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:44:10.561Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:44:10.562Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-44-10_001.log
[2025-08-31T05:44:10.562Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
