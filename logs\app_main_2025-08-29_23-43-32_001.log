========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:43:32.633Z
File: app_main_2025-08-29_23-43-32_001.log
========================================

[2025-08-30T04:43:32.862Z] [INFO] AIStudio application started successfully
[2025-08-30T04:43:32.862Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:43:32.899Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:43:33.776Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T04:43:53.179Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-43-53_001.log
[2025-08-30T04:43:53.179Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:43:55.490Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:43:55.491Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:43:56.501Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
