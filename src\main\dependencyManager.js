const path = require('path');
const fs = require('fs').promises;
const fsSync = require('fs');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const logger = require('./logger');
const realtimeLogger = require('./realtimeLogger');
const { sendStatusToSplash } = require('./splashScreen');
const { dialog, ipcMain } = require('electron');
const semver = require('semver');
const https = require('https');
const AdmZip = require('adm-zip');

// --- Configuration ---
// TODO: This needs a robust way to discover the correct python env for each pipeline
const WORKSPACE_ROOT = path.resolve(__dirname, '../..');
const PIPELINES_DIR = path.join(WORKSPACE_ROOT, 'pipelines');
const MODELS_DIR = path.join(WORKSPACE_ROOT, 'models');
const HELPERS_DIR = path.join(WORKSPACE_ROOT, 'utils', 'helpers');
const PYTHON_HELPERS_DIR = path.join(WORKSPACE_ROOT, 'src', 'main', 'python_helpers');
const TEMPLATE_DIR = path.join(WORKSPACE_ROOT, 'pipeline_templates');
const PYTHON_INSTALL_DIR = path.join(WORKSPACE_ROOT, '_InstallFirst');

// Global Python dependencies for helper scripts
const GLOBAL_PYTHON_DEPENDENCIES = [
  'pillow>=10.0.0',
  'numpy>=1.24.0',
  'opencv-python-headless>=4.7.0'
];

// Remove legacy hardcoded paths - now using dynamic paths for all pipelines

// Pipeline Configurations - Single Source of Truth
const PIPELINE_CONFIGS = {
  Core: {
    name: "Core",
    description: "Core utilities and base functionality",
    dependencies: {
      python: [
        "huggingface_hub>=0.20.3",
        "tqdm>=4.66.1",
        "requests>=2.31.0",
        "rembg>=2.0.50",
        "onnxruntime==1.15.1",
        "numpy==1.26.4",
        "opencv-python-headless==*********",
        "pillow>=10.0.0"
      ],
      npm: [
        "react-dropzone"
      ],
      models: []
    }
  },
  
  ImageGeneration: {
    name: "ImageGeneration", 
    description: "Advanced image generation using multiple Stable Diffusion models",
    dependencies: {
      type: "bundled",
      sourcePath: path.join(WORKSPACE_ROOT, 'src', 'module_source', 'ImageGeneration', 'dependencies', 'ImageGeneration.zip'),
      targetPath: path.join(PIPELINES_DIR, 'ImageGeneration'),
      models: [
        {
          name: "sdxl-turbo",
          repo_id: "stabilityai/sdxl-turbo", 
          required: true,
          description: "SDXL Turbo - Ultra-fast 1-step generation",
          local_path: "ImageGeneration/sdxl-turbo"
        },
        {
          name: "stable-diffusion-xl-base-1.0",
          repo_id: "stabilityai/stable-diffusion-xl-base-1.0",
          required: false,
          description: "SDXL Base 1.0 - High quality generation",
          local_path: "ImageGeneration/stable-diffusion-xl-base-1.0"
        },
        {
          name: "stable-diffusion-xl-refiner-1.0", 
          repo_id: "stabilityai/stable-diffusion-xl-refiner-1.0",
          required: false,
          description: "SDXL Refiner 1.0 - Quality enhancement",
          local_path: "ImageGeneration/stable-diffusion-xl-refiner-1.0"
        },
        {
          name: "flux-dev",
          repo_id: "black-forest-labs/FLUX.1-dev",
          required: false,
          description: "FLUX Dev - High quality advanced generation with guidance scale support",
          local_path: "ImageGeneration/fluxDev"
        },
        {
          name: "stable-diffusion-v1-5",
          repo_id: "runwayml/stable-diffusion-v1-5",
          required: false,
          description: "Stable Diffusion v1.5 - Classic model",
          local_path: "ImageGeneration/stable-diffusion-v1-5"
        },

      ]
    }
  },

  ImageUpscaling: {
    name: "ImageUpscaling",
    description: "Image upscaling using multiple state-of-the-art models (SwinIR, Real-ESRGAN, UltraSharp, etc.)",
    dependencies: {
      python: [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "pillow>=10.0.0",
        "numpy<2.0.0",
        "opencv-python>=4.8.0",
        "timm>=0.9.0",
        "einops>=0.6.0",
        "requests>=2.25.0",
        "tqdm>=4.64.0",
        "hf_transfer>=0.1.4"
      ],
      models: [
        {
          name: "swinir-real-sr-x4",
          repo_id: "valhalla/SwinIR-real-sr-L-x4-GAN",
          required: true,
          description: "SwinIR Real-World Super-Resolution x4 - High quality upscaler",
          local_path: "upscaling/swinir-real-sr-x4",
          files: ["003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth"]
        },
        {
          name: "realesrgan-x4plus",
          repo_id: "xinntao/Real-ESRGAN",
          required: false,
          description: "Real-ESRGAN x4plus - General purpose upscaler (photo, art, etc.)",
          local_path: "upscaling/realesrgan-x4plus",
          files: ["RealESRGAN_x4plus.pth"]
        },
        {
          name: "realesrgan-x4plus-anime",
          repo_id: "xinntao/Real-ESRGAN",
          required: false,
          description: "Real-ESRGAN x4plus anime - Anime/illustration upscaler",
          local_path: "upscaling/realesrgan-x4plus-anime",
          files: ["RealESRGAN_x4plus_anime_6B.pth"]
        },
        {
          name: "swinir-m-x4",
          repo_id: "JingyunLiang/SwinIR",
          required: false,
          description: "SwinIR-M x4 - Medium SwinIR model for classical super-resolution",
          local_path: "upscaling/swinir-m-x4",
          files: ["001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth"]
        },
        {
          name: "4xlsdir",
          repo_id: "Phhofm/models",
          required: false,
          description: "4xLSDIR - High-quality photo upscaler trained on LSDIR dataset",
          local_path: "upscaling/4xlsdir",
          files: ["4xLSDIR.pth"]
        }
      ]
    }
  },

  ImageEdit: {
    name: "ImageEdit",
    description: "AI-powered image editing using Qwen-Image-Edit model for semantic and appearance editing, style transfer, and text editing",
    dependencies: {
      python: [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "transformers>=4.51.3",
        "accelerate>=0.20.0",
        "safetensors>=0.3.0",
        "pillow>=10.0.0",
        "numpy>=1.24.0",
        "requests>=2.25.0",
        "tqdm>=4.64.0",
        "huggingface_hub>=0.20.0"
      ],
      special_packages: [
        {
          name: "diffusers-git",
          type: "pip_git",
          url: "git+https://github.com/huggingface/diffusers",
          description: "Latest diffusers with Qwen-Image-Edit support"
        }
      ],
      models: [
        {
          name: "qwen-image-edit",
          repo_id: "Qwen/Qwen-Image-Edit",
          required: true,
          description: "Qwen Image Edit model - 20B parameter model for AI-powered image editing",
          local_path: "ImageEdit/qwen-image-edit",
          size: "~40GB",
          torch_dtype: "bfloat16"
        }
      ]
    }
  },

  "trellis-stable-projectorz-101": {
    name: "Microsoft TRELLIS",
    description: "Microsoft TRELLIS - Advanced 3D generation from images",
    dependencies: {
      system: [
        {
          name: "Trellis Server",
          description: "Trellis API server with integrated virtual environment and models",
          check_command: "run.bat",
          install_path: "pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run.bat",
          required: true,
          system_package: true
        }
      ]
    }
  },

  "Video Generation": {
    name: "Video Generation",
    description: "Advanced video generation and editing using FramePack and HunyuanVideo",
    dependencies: {
      bundle: {
        type: "bundled",
        sourcePath: path.join(WORKSPACE_ROOT, 'src', 'module_source', 'VideoGen', 'framepack_cu126_torch26.zip'),
        targetPath: path.join(PIPELINES_DIR, 'VideoPipelines')
      },
      python: {
        "framepack": {
          name: "FramePack Server",
          description: "FramePack API server with integrated environment",
          installed: false,
          required: "Bundled",
          satisfied: false
        }
      },
      models: {
        "framepack-base": {
          name: "FramePack Base Model",
          description: "Core FramePack model for video generation",
          installed: false,
          required: true
        },
        "framepack-hunyuan": {
          name: "HunyuanVideo Integration",
          description: "Enhanced video generation with HunyuanVideo",
          installed: false,
          required: true
        }
      }
    },
    features: [
      "Text-to-Video Generation",
      "Image-to-Video Generation",
      "Video-to-Video Translation",
      "High-Quality Video Output",
      "Support for Multiple Video Formats"
    ]
  },

  "HunyuanVideo": {
    name: "HunyuanVideo",
    description: "AI video generation from images using HunyuanVideo models",
    dependencies: {
      python: [
        "torch>=2.7.1+cu126",
        "torchvision>=0.18.1+cu126",
        "torchaudio>=2.7.1+cu126",
        "accelerate==1.6.0",
        "diffusers==0.33.1",
        "transformers==4.46.2",
        "sentencepiece==0.2.0",
        "pillow==11.1.0",
        "av==12.1.0",
        "numpy==1.26.2",
        "scipy==1.12.0",
        "requests==2.31.0",
        "torchsde==0.2.6",
        "einops",
        "opencv-contrib-python",
        "safetensors",
        "huggingface_hub>=0.20.3"
      ],

    }
  },

  "v13_hunyuan2-stableprojectorz": {
    name: "Hunyuan3D",
    description: "Tencent Hunyuan3D - Production-ready 3D generation with PBR materials",
    features: [
      "Single image to 3D",
      "PBR materials",
      "Production-ready assets",
      "High-quality textures",
      "GLB export",
      "Multiple quality presets",
    ],
    dependencies: {
      python: [
        "diffusers>=0.30.0",
        "transformers>=4.44.0",
        "trimesh[easy]>=4.4.9",
        "xatlas>=0.0.9",
        "Pillow>=10.0.0",
        "opencv-python>=4.8.0",
        "numpy>=1.24.0",
        "scipy>=1.11.0",
        "requests>=2.31.0",
        "accelerate>=1.1.1",
        "huggingface-hub>=0.30.2",
        "safetensors>=0.4.4",
        "einops>=0.8.0",
        "imageio>=2.36.0",
        "scikit-image>=0.24.0",
        "rembg>=2.0.65",
        "pymeshlab>=2022.2.post3",
        "pygltflib>=1.16.3",
        "omegaconf>=2.3.0",
        "pyyaml>=6.0.2",
        "fastapi>=0.115.12",
        "uvicorn>=0.34.3",
        "tqdm>=4.66.5",
        "psutil>=6.0.0",
        "pydantic>=2.10.6",
        "timm"
      ],
      special_packages: [
        {
          name: "pytorch-cuda",
          description: "PyTorch with CUDA support",
          install_command: "pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124",
          required: true
        }
      ],
      models: [
        {
          name: "Hunyuan3D Shape Model",
          description: "Hunyuan3D shape generation model",
          repo_id: "tencent/Hunyuan3D-2.1",
          subfolder: "hunyuan3d-dit-v2-1",
          local_path: "Hunyuan3D/tencent--Hunyuan3D-2.1/hunyuan3d-dit-v2-1",
          size: "~8GB",
          required: true,
          auto_download: true
        },
        {
          name: "Hunyuan3D Texture Model",
          description: "Hunyuan3D PBR texture generation model",
          repo_id: "tencent/Hunyuan3D-2.1",
          subfolder: "hunyuan3d-paintpbr-v2-1",
          local_path: "Hunyuan3D/tencent--Hunyuan3D-2.1/hunyuan3d-paintpbr-v2-1",
          size: "~4GB",
          required: true,
          auto_download: true
        },
        {
          name: "realesrgan-x4plus",
          description: "Real-ESRGAN model for texture upscaling",
          url: "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth",
          local_path: "upscaling/RealESRGAN_x4plus.pth",
          size: "~64MB",
          required: false,
          auto_download: true
        }
      ],
      post_install: {
        script: "install_official_hunyuan.bat",
        description: "Install official Hunyuan3D repository and C++ extensions",
        type: "batch"
      }
    }
  }
};

class DependencyManager {
  constructor(store) {
    // Use canonical PIPELINE_CONFIGS as the source of truth for pipeline definitions
    this.pipelines = Object.assign({}, PIPELINE_CONFIGS);
    this.dependencyStatus = {};
    this.dependencyCache = {}; // Cache for dependency check results
    this.store = store;
    this.huggingFaceToken = null;
    this.mainWindow = null;
    this.currentLogStream = null;

    // Initialize helpers
    this._initializeHelpers().catch(error => {
      logger.error('Failed to initialize helpers:', error);
    });
  }

  setMainWindow(win) {
    this.mainWindow = win;
  }

  setStore(store) {
    this.store = store;
  }

  // --- Dependency Manager Logging Methods ---
  _startDependencyLogging(pipelineName, operation) {
    const streamName = `dependency_${pipelineName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
    const prefix = `dependency_${operation}`;

    this.currentLogStream = realtimeLogger.createLogStream(streamName, prefix);

    const header = `DependencyManager: Starting ${operation} for ${pipelineName}`;
    realtimeLogger.log(streamName, header, 'info');
    logger.info(header);

    return streamName;
  }

  _logDependency(streamName, message, level = 'info') {
    if (streamName) {
      realtimeLogger.log(streamName, `DependencyManager: ${message}`, level);
    }
    // Also log to main logger
    logger[level](`[DependencyManager] ${message}`);
  }

  _endDependencyLogging(streamName, pipelineName, operation, success = true) {
    if (streamName) {
      const footer = `DependencyManager: ${success ? 'Completed' : 'Failed'} ${operation} for ${pipelineName}`;
      realtimeLogger.log(streamName, footer, success ? 'info' : 'error');
      logger.info(footer);

      // Close the stream after a brief delay to ensure all logs are written
      setTimeout(() => {
        realtimeLogger.closeStream(streamName);
      }, 1000);
    }
    this.currentLogStream = null;
  }

  setHuggingFaceToken(token) {
    if (this.store) {
      this.store.set('huggingface-token', token);
      logger.info(`Hugging Face token updated: ${token ? `[${token.length} chars]` : 'null/undefined'}`);
      return { success: true, message: 'Token saved successfully!' };
    } else {
      logger.error('Store not available in DependencyManager.');
      return { success: false, error: 'Internal error: Store not configured.' };
    }
  }

  getHuggingFaceToken() {
    if (this.store) {
      const token = this.store.get('huggingface-token');
      logger.info(`Retrieved HF token from store: ${token ? `[${token.length} chars]` : 'null/undefined'}`);
      return token;
    }
    logger.warn('Store not available in DependencyManager for token retrieval.');
    return null;
  }

  async scanAndLoadPipelines() {
    logger.info('Loading pipelines from embedded configurations...');
    
    try {
      // Load pipelines from embedded configurations
      for (const [pipelineName, config] of Object.entries(PIPELINE_CONFIGS)) {
        this.pipelines[pipelineName] = config;
        this.dependencyStatus[pipelineName] = {
          name: pipelineName,
          python: { installed: false, details: {} },
          models: { installed: false, details: {} }
        };
        logger.info(`Loaded pipeline: ${pipelineName}`);
      }
      
      // Ensure pipeline directories exist
      for (const pipelineName of Object.keys(PIPELINE_CONFIGS)) {
        await this._ensurePipelineDirectoryExists(pipelineName);
      }
      
    } catch (error) {
      logger.error(`Error loading pipelines: ${error.message}`);
    }
  }

  async _ensurePipelineDirectoryExists(pipelineName) {
    try {
      // Skip creating directories for system package pipelines that use bat files
      const systemPackagePipelines = ['trellis-stable-projectorz-101'];
      if (systemPackagePipelines.includes(pipelineName)) {
        logger.info(`Skipping directory creation for system package pipeline: ${pipelineName}`);
        return;
      }

      const pipelineDir = this._getPipelineDir(pipelineName);
      await fs.mkdir(pipelineDir, { recursive: true });

      // Write config.json to the pipeline directory for reference
      const config = PIPELINE_CONFIGS[pipelineName];
      if (config) {
        const configPath = path.join(pipelineDir, 'config.json');
        await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      }

      logger.info(`Ensured pipeline directory exists: ${pipelineName}`);
    } catch (error) {
      logger.error(`Failed to create pipeline directory for ${pipelineName}:`, error);
      throw error;
    }
  }

  async getDependencyStatus() {
    logger.info('Getting detailed dependency status for all pipelines...');

    const total = Object.keys(this.pipelines).length;
    let done = 0;

    const emitProgress = () => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('status-progress', {
          done,
          total,
          percent: Math.round((done / total) * 100)
        });
      }
    };

    emitProgress();

    const status = [];
    for (const [name, pipeline] of Object.entries(this.pipelines)) {
      // Handle different dependency structures
      let pythonDeps = [];
      let modelDeps = [];
      
      if (pipeline.dependencies?.system) {
        // System package pipelines (Hunyuan, Trellis)
        pythonDeps = pipeline.dependencies.system || [];
        modelDeps = []; // Models are included in system packages
      } else {
        // Standard pipelines
        pythonDeps = pipeline.dependencies?.python || [];
        modelDeps = pipeline.dependencies?.models || [];
      }
      
      try {
        // Special handling for Hunyuan3D-2.1 and Trellis modules
        if (name === 'v13_hunyuan2-stableprojectorz') {
          const hunyuanInstalled = await this._validateHunyuan21Installation();
          status.push({
            name,
            description: pipeline.description || '',
            python: {
              installed: hunyuanInstalled,
              details: hunyuanInstalled ? { 'Hunyuan3D System Package': { satisfied: true, installed: 'System Package' } } : { 'Hunyuan3D System Package': { satisfied: false, installed: 'Not installed' } },
              dependencies: pythonDeps
            },
            models: {
              installed: hunyuanInstalled,
              details: hunyuanInstalled ? { 'Hunyuan3D Models': { satisfied: true, installed: 'System Package' } } : { 'Hunyuan3D Models': { satisfied: false, installed: 'Not installed' } },
              dependencies: modelDeps
            }
          });
        } else if (name === 'trellis-stable-projectorz-101' || name.toLowerCase().includes('trellis')) {
          const trellisInstalled = await this._validateTrellisInstallation();
          status.push({
            name,
            description: pipeline.description || '',
            python: {
              installed: trellisInstalled,
              details: trellisInstalled ? { 'Trellis Server System Package': { satisfied: true, installed: 'System Package' } } : { 'Trellis Server System Package': { satisfied: false, installed: 'Not installed' } },
              dependencies: pythonDeps
            },
            models: {
              installed: trellisInstalled,
              details: trellisInstalled ? { 'Trellis Models': { satisfied: true, installed: 'System Package' } } : { 'Trellis Models': { satisfied: false, installed: 'Not installed' } },
              dependencies: modelDeps
            }
          });
        } else {
          // Standard dependency checking for other pipelines
          const timeoutMs = name === 'ImageGeneration' ? 30000 : 10000;
          const [pythonStatus, modelStatus] = await Promise.race([
            Promise.all([
              this._checkPythonDependencies(name, pythonDeps),
              this._checkModelDependencies(name, modelDeps)
            ]),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error(`Timeout checking ${name} dependencies`)), timeoutMs)
            )
          ]);

          status.push({
            name,
            description: pipeline.description || '',
            python: {
              installed: pythonStatus.satisfied,
              details: pythonStatus.details,
              dependencies: pythonDeps
            },
            models: {
              installed: modelStatus.satisfied,
              details: modelStatus.details,
              dependencies: modelDeps
            }
          });
        }
      } catch (error) {
        const errorMessage = error?.message || error?.toString() || String(error);
        logger.warn(`Failed to check dependencies for ${name}: ${errorMessage}`);
        // Add pipeline with error status instead of failing completely
        status.push({
          name,
          description: pipeline.description || '',
          python: {
            installed: false,
            details: { error: errorMessage },
            dependencies: pythonDeps
          },
          models: {
            installed: false,
            details: { error: errorMessage },
            dependencies: modelDeps
          }
        });
      }

      done += 1;
      emitProgress();
    }

    return status;
  }

  async installDependencies(pipelineName, component = 'python', name = 'all') {
    // Start dependency logging
    const logStream = this._startDependencyLogging(pipelineName, `${component} installation`);

    logger.info(`Installing dependencies for ${pipelineName} (${component}:${name})`);
    this._logDependency(logStream, `Installing dependencies for ${pipelineName} (${component}:${name})`);
    this._logDependency(logStream, `Component type: ${typeof component}, Component value: '${component}'`);
    this._logDependency(logStream, `Name type: ${typeof name}, Name value: '${name}'`);
    this._logDependency(logStream, `About to check routing for ${pipelineName} with component ${component}`);

    


    // --- Custom 3D Modules (Zip-based installation) ---
    if (pipelineName === 'trellis-stable-projectorz-101') {
      return await this._install3DModuleFromZip(pipelineName, component, name);
    }



    // --- Custom ImageGeneration install ---
    if (pipelineName === 'ImageGeneration') {
      return await this._installImageGenerationModule(pipelineName, component, name);
    }

    // --- Custom ImageUpscaling install ---
    if (pipelineName === 'ImageUpscaling') {
      return await this._installImageUpscalingModule(pipelineName, component, name);
    }

    // --- Custom ImageEdit install ---
    if (pipelineName === 'ImageEdit') {
      return await this._installImageEditModule(pipelineName, component, name);
    }

    // --- Custom Video Generation install ---
    if (pipelineName === 'VideoPipelines' || pipelineName === 'Video Generation') {
      return await this._installVideoGenerationModule('Video Generation', component, name);
    }



    // --- Custom Hunyuan3D install (Zip-based) ---
    if (pipelineName === 'v13_hunyuan2-stableprojectorz') {
      return await this._install3DModuleFromZip(pipelineName, component, name);
    }

    try {
      // Ensure pipeline exists and is properly configured
      await this._ensurePipelineExists(pipelineName);

      const pipeline = this.pipelines[pipelineName];
      if (!pipeline) {
        throw new Error(`Pipeline ${pipelineName} not found`);
      }
      
      // Handle different dependency structures
      let allDeps = [];
      let specialPackages = [];
      let allModels = [];
      
      if (pipeline.dependencies?.system) {
        // System package pipelines (Hunyuan, Trellis)
        allDeps = pipeline.dependencies.system || [];
        specialPackages = [];
        allModels = []; // Models are included in system packages
        logger.info(`Pipeline found. Dependencies available: system=${!!pipeline.dependencies?.system}`);
      } else {
        // Standard pipelines
        allDeps = pipeline.dependencies?.python || [];
        specialPackages = pipeline.dependencies?.special_packages || [];
        allModels = pipeline.dependencies?.models || [];
        logger.info(`Pipeline found. Dependencies available: python=${!!pipeline.dependencies?.python}, models=${!!pipeline.dependencies?.models}`);
      }

      logger.info(`Checking component branch - component === 'python': ${component === 'python'}`);
      logger.info(`Checking component branch - component === 'models': ${component === 'models'}`);
      
      if (component === 'python') {
        logger.info(`Entering Python installation branch`);
        
        if (name === 'all') {
          // Install both Python dependencies AND special packages AND models when installing "all"
          this._logDependency(logStream, `Installing Python dependencies for ${pipelineName}...`);
          await this._installPythonDependencies(pipelineName, allDeps, true);

          // Install special packages after Python dependencies
          if (specialPackages.length > 0) {
            logger.info(`Installing special packages for ${pipelineName}...`);
            this._logDependency(logStream, `Installing ${specialPackages.length} special packages for ${pipelineName}...`);
            await this._installSpecialPackages(pipelineName, specialPackages);
          }

          // Run post-install script if it exists
          if (pipeline.dependencies.post_install) {
            logger.info(`Running post-install script for ${pipelineName}...`);
            try {
              await this._runPostInstallScript(pipelineName, pipeline.dependencies.post_install);
              logger.info(`Post-install script completed successfully for ${pipelineName}`);
            } catch (postInstallError) {
              logger.error(`Post-install script failed for ${pipelineName}:`, postInstallError);
              throw postInstallError;
            }
          }

          // Install models after special packages are complete
          if (allModels.length > 0) {
            logger.info(`Installing ${allModels.length} models for ${pipelineName}: ${allModels.map(m => m.name).join(', ')}`);
            this._logDependency(logStream, `Installing ${allModels.length} models for ${pipelineName}: ${allModels.map(m => m.name).join(', ')}`);
            logger.info(`Model details: ${JSON.stringify(allModels.map(m => ({name: m.name, repo_id: m.repo_id})), null, 2)}`);
            try {
              await this._installModelDependencies(pipelineName, allModels);
              logger.info(`Successfully completed model installation for ${pipelineName}`);
              this._logDependency(logStream, `Successfully completed model installation for ${pipelineName}`);
            } catch (modelError) {
              logger.error(`Model installation failed for ${pipelineName}:`, modelError);
              this._logDependency(logStream, `Model installation failed for ${pipelineName}: ${modelError.message}`, 'error');
              throw modelError;
            }
          } else {
            logger.info(`No models configured for ${pipelineName}`);
            this._logDependency(logStream, `No models configured for ${pipelineName}`);
          }
        } else {
          // Check if it's a special package first
          const specialPkg = specialPackages.find(p => p.name === name);
          if (specialPkg) {
            await this._installSpecialPackages(pipelineName, [specialPkg]);
          } else {
            // Install individual dependency
            const dep = allDeps.find(d => d.startsWith(name));
            if (!dep) {
              throw new Error(`Dependency ${name} not found in pipeline ${pipelineName}`);
            }
            await this._installPythonDependencies(pipelineName, [dep], false);
          }
        }
      } else if (component === 'models') {
        logger.info(`Entering Models installation branch`);
        this._logDependency(logStream, `Entering Models installation branch`);
        const allModels = pipeline.dependencies?.models || [];
        logger.info(`Found ${allModels.length} models in config: ${allModels.map(m => m.name).join(', ')}`);
        this._logDependency(logStream, `Found ${allModels.length} models in config: ${allModels.map(m => m.name).join(', ')}`);

        if (name === 'all') {
          logger.info(`Attempting to install ${allModels.length} models for ${pipelineName}`);
          this._logDependency(logStream, `Attempting to install ${allModels.length} models for ${pipelineName}`);
          await this._installModelDependencies(pipelineName, allModels);
        } else {
          // Install individual model
          logger.info(`Looking for individual model: ${name}`);
          this._logDependency(logStream, `Looking for individual model: ${name}`);
          const model = allModels.find(m => m.name === name);
          if (!model) {
            logger.error(`Model ${name} not found in pipeline ${pipelineName}. Available models: ${allModels.map(m => m.name).join(', ')}`);
            this._logDependency(logStream, `Model ${name} not found in pipeline ${pipelineName}. Available models: ${allModels.map(m => m.name).join(', ')}`, 'error');
            throw new Error(`Model ${name} not found in pipeline ${pipelineName}`);
          }
          logger.info(`Attempting to install model ${model.name} (repo: ${model.repo_id}) for ${pipelineName}`);
          this._logDependency(logStream, `Attempting to install model ${model.name} (repo: ${model.repo_id}) for ${pipelineName}`);
          await this._installModelDependencies(pipelineName, [model]);
        }
      } else {
        logger.error(`Unknown component: ${component}. Expected 'python' or 'models'`);
        throw new Error(`Unknown component: ${component}`);
      }

      // Update dependency status after installation
      const status = await this.checkDependencies(pipelineName, true);
      this._logDependency(logStream, `Dependency status updated for ${pipelineName}`);

      // Notify UI to refresh dependency status
      setTimeout(() => {
        if (this.mainWindow) {
          logger.info(`Sending dependency-status-changed event for ${pipelineName} (${component})`);
          this._logDependency(logStream, `Sending dependency-status-changed event for ${pipelineName} (${component})`);
          this.mainWindow.webContents.send('dependency-status-changed', {
            pipeline: pipelineName,
            type: component,
            status: 'installed'
          });
        }
      }, 500);

      // End dependency logging with success
      this._endDependencyLogging(logStream, pipelineName, `${component} installation`, true);
      return status;

    } catch (error) {
      logger.error(`Failed to install dependencies for ${pipelineName} (${component}:${name}):`, error);
      this._logDependency(logStream, `Failed to install dependencies: ${error.message}`, 'error');

      // Send error progress update for models
      if (component === 'models') {
        this._sendProgress(pipelineName, 'models', name, {
          status: 'Error',
          message: error.message,
          progress: 0
        });
      }

      // End dependency logging with failure
      this._endDependencyLogging(logStream, pipelineName, `${component} installation`, false);
      throw error;
    }
  }

  // --- Modified Checkers ---
  async checkDependencies(pipelineName, duringInstallation = false) {
    logger.info(`Checking dependencies for ${pipelineName}${duringInstallation ? ' (during installation)' : ''}`);

    // Start dependency check logging if not during installation
    let logStream = null;
    if (!duringInstallation) {
      logStream = this._startDependencyLogging(pipelineName, 'dependency check');
    }

    if (!this.pipelines[pipelineName]) {
      if (logStream) {
        this._logDependency(logStream, `Pipeline ${pipelineName} not found`, 'error');
        this._endDependencyLogging(logStream, pipelineName, 'dependency check', false);
      }
      throw new Error(`Pipeline ${pipelineName} not found`);
    }



    if (pipelineName === 'v13_hunyuan2-stableprojectorz') {
      const hunyuanInstalled = await this._validateHunyuan21Installation();
      this.dependencyStatus[pipelineName] = {
        python: {
          installed: hunyuanInstalled,
          details: hunyuanInstalled ? { 'Hunyuan3D System Package': { satisfied: true, installed: 'System Package' } } : { 'Hunyuan3D System Package': { satisfied: false, installed: 'Not installed' } }
        },
        models: {
          installed: hunyuanInstalled,
          details: hunyuanInstalled ? { 'Hunyuan3D Models': { satisfied: true, installed: 'System Package' } } : { 'Hunyuan3D Models': { satisfied: false, installed: 'Not installed' } }
        }
      };
      return;
    }

    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      const trellisInstalled = await this._validateTrellisInstallation();
      this.dependencyStatus[pipelineName] = {
        python: {
          installed: trellisInstalled,
          details: trellisInstalled ? { 'Trellis Server System Package': { satisfied: true, installed: 'System Package' } } : { 'Trellis Server System Package': { satisfied: false, installed: 'Not installed' } }
        },
        models: {
          installed: trellisInstalled,
          details: trellisInstalled ? { 'Trellis Models': { satisfied: true, installed: 'System Package' } } : { 'Trellis Models': { satisfied: false, installed: 'Not installed' } }
        }
      };
      return { satisfied: trellisInstalled };
    }



    const config = this.pipelines[pipelineName];
    
    // Check if this is a bundled dependency pipeline
    if (config.dependencies?.type === 'bundled') {
      const targetPath = path.join(PIPELINES_DIR, pipelineName);
      const envPath = path.join(targetPath, 'env');
      const isInstalled = fsSync.existsSync(targetPath) && fsSync.existsSync(envPath);

      this.dependencyStatus[pipelineName] = {
        python: {
          installed: isInstalled,
          details: {
            'Bundled Environment': {
              satisfied: isInstalled,
              installed: isInstalled ? 'Installed' : 'Not installed',
              required: 'Bundled'
            }
          }
        },
        models: {
          installed: isInstalled
        }
      };

      return { satisfied: isInstalled };
    }
    
    // Handle different dependency structures
    let pythonDeps = [];
    // Check if this is a system package pipeline (like Hunyuan, Trellis, Video Generation)
    if (config.dependencies?.system) {
      // System package pipelines (Hunyuan, Trellis)
      pythonDeps = config.dependencies.system || [];
      specialPackages = [];
      modelDeps = [];
    } else if (pipelineName === 'Video Generation') {
      // Special handling for Video Generation - check if framepack_cu126_torch26 is actually installed
      const framepackDir = path.join(PIPELINES_DIR, 'VideoPipelines', 'framepack_cu126_torch26');
      const requiredItems = [
        { path: path.join(framepackDir, 'webui'), type: 'directory' },
        { path: path.join(framepackDir, 'system'), type: 'directory' },
        { path: path.join(framepackDir, 'environment.bat'), type: 'file' },
        { path: path.join(framepackDir, 'run.bat'), type: 'file' },
        { path: path.join(framepackDir, 'update.bat'), type: 'file' },
        { path: path.join(framepackDir, 'config.json'), type: 'file' }
      ];
      
      let isInstalled = true;
      const missingItems = [];
      
      for (const item of requiredItems) {
        try {
          const stats = await fs.stat(item.path);
          if ((item.type === 'directory' && !stats.isDirectory()) || 
              (item.type === 'file' && !stats.isFile())) {
            missingItems.push(`${item.path} (wrong type)`);
            isInstalled = false;
          }
        } catch (error) {
          missingItems.push(item.path);
          isInstalled = false;
        }
      }
      
      if (!isInstalled) {
        console.log('Video Generation module is missing required items:', missingItems);
      }
      
      if (!isInstalled) {
        // If not installed, set up dependencies to trigger installation
        pythonDeps = ['framepack-install'];
      }
      
      specialPackages = [];
      modelDeps = [];
    } else {
      // Standard pipelines
      pythonDeps = config.dependencies?.python || [];
      specialPackages = config.dependencies?.special_packages || [];
      modelDeps = config.dependencies?.models || [];
    }

    try {
      // Check Python dependencies
      if (pythonDeps.length > 0) {
        const pythonStatus = await this._checkPythonDependencies(pipelineName, pythonDeps);
        this.dependencyStatus[pipelineName].python = {
          installed: pythonStatus.satisfied,
          details: pythonStatus.details
        };

        if (!pythonStatus.satisfied) {
          logger.warn(`Python dependencies not satisfied for ${pipelineName}`);
          if (!duringInstallation) return { satisfied: false };
        }
      }

      // Check special packages
      if (specialPackages.length > 0) {
        const requiredSpecialPackages = specialPackages.filter(pkg => pkg.required !== false);
        const optionalSpecialPackages = specialPackages.filter(pkg => pkg.required === false);
        
        // Check required special packages
        if (requiredSpecialPackages.length > 0) {
          const requiredPackageNames = requiredSpecialPackages.map(pkg => {
            if (pkg.version) {
              return `${pkg.name}==${pkg.version}`;
            } else {
              // For packages with install_command (like PyTorch), just check the base package name
              return pkg.name;
            }
          });
          const requiredStatus = await this._checkPythonDependencies(pipelineName, requiredPackageNames);
          
          // Merge required special package status into python dependencies
          if (!this.dependencyStatus[pipelineName].python) {
            this.dependencyStatus[pipelineName].python = { installed: true, details: {} };
          }
          
          Object.assign(this.dependencyStatus[pipelineName].python.details, requiredStatus.details);
          this.dependencyStatus[pipelineName].python.installed = 
            this.dependencyStatus[pipelineName].python.installed && requiredStatus.satisfied;

          if (!requiredStatus.satisfied) {
            logger.warn(`Required special package dependencies not satisfied for ${pipelineName}`);
            if (!duringInstallation) return { satisfied: false };
          }
        }
        
        // Check optional special packages (don't fail if missing)
        if (optionalSpecialPackages.length > 0) {
          const optionalPackageNames = optionalSpecialPackages.map(pkg => {
            if (pkg.version) {
              return `${pkg.name}==${pkg.version}`;
            } else {
              // For packages with install_command (like PyTorch), just check the base package name
              return pkg.name;
            }
          });
          const optionalStatus = await this._checkPythonDependencies(pipelineName, optionalPackageNames);
          
          // Merge optional special package status into python dependencies (but don't affect overall status)
          if (!this.dependencyStatus[pipelineName].python) {
            this.dependencyStatus[pipelineName].python = { installed: true, details: {} };
          }
          
          // Mark optional packages as satisfied even if not installed (for UI purposes)
          for (const pkg of optionalSpecialPackages) {
            const pkgName = `${pkg.name}==${pkg.version}`;
            if (optionalStatus.details[pkgName] && !optionalStatus.details[pkgName].satisfied) {
              optionalStatus.details[pkgName].satisfied = true;
              optionalStatus.details[pkgName].installed = `Optional (using fallback: ${pkg.fallback_backend || 'none'})`;
            }
          }
          
          Object.assign(this.dependencyStatus[pipelineName].python.details, optionalStatus.details);
          
          logger.info(`Optional special packages for ${pipelineName}: ${optionalSpecialPackages.map(p => p.name).join(', ')}`);
        }
      }

      // Check model dependencies
      if (modelDeps.length > 0) {
        const modelStatus = await this._checkModelDependencies(pipelineName, modelDeps);
        this.dependencyStatus[pipelineName].models = {
          installed: modelStatus.satisfied,
          details: modelStatus.details
        };

        if (!modelStatus.satisfied) {
          logger.warn(`Model dependencies not satisfied for ${pipelineName}`);
          if (!duringInstallation) return { satisfied: false };
        }
      }

      // During installation, always return true to allow the process to continue
      // Outside of installation, return true only if all dependencies are satisfied
      if (duringInstallation) {
        logger.info(`Dependency check completed during installation for ${pipelineName}`);
        return { satisfied: true };
      }

      // Special handling for Video Generation module
      if (pipelineName === 'Video Generation') {
        const framepackDir = path.join(PIPELINES_DIR, 'VideoPipelines', 'framepack_cu126_torch26');
        const requiredItems = [
          { path: path.join(framepackDir, 'webui'), type: 'directory' },
          { path: path.join(framepackDir, 'system'), type: 'directory' },
          { path: path.join(framepackDir, 'environment.bat'), type: 'file' },
          { path: path.join(framepackDir, 'run.bat'), type: 'file' },
          { path: path.join(framepackDir, 'update.bat'), type: 'file' },
          { path: path.join(framepackDir, 'config.json'), type: 'file' }
        ];
        
        let isFullyInstalled = true;
        const missingItems = [];
        
        for (const item of requiredItems) {
          try {
            const stats = fsSync.statSync(item.path);
            if ((item.type === 'directory' && !stats.isDirectory()) || 
                (item.type === 'file' && !stats.isFile())) {
              missingItems.push(`${item.path} (wrong type)`);
              isFullyInstalled = false;
            }
          } catch (error) {
            missingItems.push(item.path);
            isFullyInstalled = false;
          }
        }
        
        if (!isFullyInstalled) {
          logger.warn(`Video Generation module is missing required items: ${missingItems.join(', ')}`);
          this.dependencyStatus[pipelineName] = this.dependencyStatus[pipelineName] || {};
          this.dependencyStatus[pipelineName].python = {
            installed: false,
            status: 'missing',
            message: `Missing required files: ${missingItems.join(', ')}`,
            details: {}
          };
          this.dependencyStatus[pipelineName].models = { installed: false };
          this.dependencyStatus[pipelineName].bundled = { installed: false };
          
          if (logStream) {
            this._logDependency(logStream, `Video Generation module is missing required items: ${missingItems.join(', ')}`, 'warn');
          }
          
          return { satisfied: false };
        } else {
          // Mark as installed if all files are present
          this.dependencyStatus[pipelineName] = this.dependencyStatus[pipelineName] || {};
          this.dependencyStatus[pipelineName].python = {
            installed: true,
            status: 'installed',
            message: 'Video Generation module is installed',
            details: {
              'framepack': {
                satisfied: true,
                installed: 'Installed',
                required: 'Required'
              }
            }
          };
          this.dependencyStatus[pipelineName].models = { installed: true };
          this.dependencyStatus[pipelineName].bundled = { installed: true };
          
          if (logStream) {
            this._logDependency(logStream, 'Video Generation module is fully installed');
          }
          
          return { satisfied: true };
        }
      }
      
      // Check if all dependency types are satisfied
      const pythonSatisfied = !pythonDeps.length || this.dependencyStatus[pipelineName]?.python?.installed;
      const modelsSatisfied = !modelDeps.length || this.dependencyStatus[pipelineName]?.models?.installed;

      const allSatisfied = pythonSatisfied && modelsSatisfied;
      logger.info(`Final dependency check for ${pipelineName}: Python=${pythonSatisfied}, Models=${modelsSatisfied}, Overall=${allSatisfied}`);

      if (logStream) {
        this._logDependency(logStream, `Final dependency check for ${pipelineName}: Python=${pythonSatisfied}, Models=${modelsSatisfied}, Overall=${allSatisfied}`);
        this._endDependencyLogging(logStream, pipelineName, 'dependency check', allSatisfied);
      }

      return { satisfied: allSatisfied };
    } catch (error) {
      logger.error(`Error checking dependencies for ${pipelineName}:`, error);
      if (logStream) {
        this._logDependency(logStream, `Error checking dependencies: ${error.message}`, 'error');
        this._endDependencyLogging(logStream, pipelineName, 'dependency check', false);
      }
      return { satisfied: false };
    }
  }

  // --- Installation Logic ---
  async _installPythonDependencies(pipelineName, depsToInstall, installAll = false) {
    // Special case: Microsoft_TRELLIS uses the new module installation approach
    if (pipelineName === 'Microsoft_TRELLIS' || pipelineName.toLowerCase().includes('trellis')) {
      return await this._installTrellisModule(pipelineName, 'python', 'all');
    }

    // Special case: Hunyuan3D-2.1 uses the new module installation approach
    if (pipelineName === 'v13_hunyuan2-stableprojectorz') {
      return await this._installHunyuanModule(pipelineName, 'python', 'all');
    }

    // Special case: Video Generation uses the new module installation approach
    if (pipelineName === 'Video Generation') {
      // First check if framepack_cu126_torch26 is already installed with all required files
      const framepackDir = path.join(PIPELINES_DIR, 'VideoPipelines', 'framepack_cu126_torch26');
      const requiredItems = [
        { path: path.join(framepackDir, 'webui'), type: 'directory' },
        { path: path.join(framepackDir, 'system'), type: 'directory' },
        { path: path.join(framepackDir, 'environment.bat'), type: 'file' },
        { path: path.join(framepackDir, 'run.bat'), type: 'file' },
        { path: path.join(framepackDir, 'update.bat'), type: 'file' },
        { path: path.join(framepackDir, 'config.json'), type: 'file' }
      ];
      
      console.log('Checking Video Generation installation at:', framepackDir);
      
      let isInstalled = true;
      const missingItems = [];
      
      for (const item of requiredItems) {
        try {
          const stats = fsSync.statSync(item.path);
          if ((item.type === 'directory' && !stats.isDirectory()) || 
              (item.type === 'file' && !stats.isFile())) {
            missingItems.push(`${item.path} (wrong type)`);
            isInstalled = false;
          }
        } catch (error) {
          missingItems.push(item.path);
          isInstalled = false;
        }
      }
      
      if (!isInstalled) {
        console.log('Video Generation module is missing required items:', missingItems);
      } else {
        console.log('Video Generation module is fully installed');
      }
      
      if (!isInstalled) {
        console.log('Initiating Video Generation module installation...');
        return await this._installVideoGenerationModule(pipelineName, 'python', 'all');
      }
      
      // If already installed, just return success
      return { success: true, message: 'Video Generation module is already installed' };
    }



    // Special case: ImageGeneration uses the new module installation approach
    if (pipelineName === 'ImageGeneration') {
      return await this._installImageGenerationModule(pipelineName, 'python', 'all');
    }

    // Special case: ImageUpscaling uses the new module installation approach
    if (pipelineName === 'ImageUpscaling') {
      return await this._installImageUpscalingModule(pipelineName, 'python', 'all');
    }

    // Special case: ImageEdit uses the new module installation approach
    if (pipelineName === 'ImageEdit') {
      return await this._installImageEditModule(pipelineName, 'python', 'all');
    }

    try {
      // Ensure virtual environment exists
      await this._ensureVenvExists(pipelineName);
    } catch (error) {
      logger.error(`Failed to create/verify virtual environment for ${pipelineName}:`, error);
      throw new Error(`Failed to create/verify virtual environment: ${error.message}`);
    }

    const SKIP_PKGS = new Set(['nvidia-index-url']); // Skip packages that are not actual dependencies
    depsToInstall = depsToInstall.filter(pkg => {
      // Ensure pkg is a string
      if (typeof pkg !== 'string') {
        logger.warn(`Skipping non-string dependency: ${JSON.stringify(pkg)}`);
        return false;
      }
      const match = pkg.match(/^[a-zA-Z0-9_-]+/);
      return match && !SKIP_PKGS.has(match[0]);
    });

    const pythonExe = this._getPythonExe(pipelineName);
    const progressName = installAll ? 'all' : depsToInstall.join(' ');

    // Initialize progress
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Starting installation...',
      progress: 0,
    });

    // --- Ensure pip, setuptools & wheel are current ---
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Updating pip and core packages...',
      progress: 5,
    });

    await new Promise((resolve) => {
      const env = { ...process.env };
      const scriptsDir = path.dirname(pythonExe);
      delete env.PYTHONHOME;
      delete env.PYTHONPATH;
      env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

      const upgrader = spawn(pythonExe, ['-m', 'pip', 'install', '--upgrade', 'pip', 'setuptools', 'wheel'], { env });
      upgrader.on('error', () => resolve());
      upgrader.on('close', () => resolve());
    });

    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Checking existing packages...',
      progress: 10,
    });

    // Filter out packages that are already satisfied so we don't reinstall.
    try {
      const check = await this._checkPythonDependencies(pipelineName, depsToInstall);
      const unsatisfied = [];
      for (const dep of depsToInstall) {
        const detail = check.details[dep];
        if (detail && detail.satisfied) {
          // Emit instant complete event for this dep if installing individually.
          if (!installAll) {
            this._sendProgress(pipelineName, 'python', dep, {
              status: 'Complete',
              message: 'Already installed',
              progress: 100,
            });
          }
        } else {
          unsatisfied.push(dep);
        }
      }
      depsToInstall = unsatisfied;
      if (depsToInstall.length === 0) {
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'All selected packages already installed.',
          progress: 100,
        });
        return;
      }
    } catch (e) {
      logger.warn('Unable to pre-check python deps:', e.message);
    }

    if (depsToInstall.length === 0) {
      this._sendProgress(pipelineName, 'python', progressName, { 
        status: 'Complete', 
        message: 'No dependencies to install.', 
        progress: 100 
      });
      return;
    }
    
    // Check if any packages need CUDA support (before normalization)
    const originalHasCudaPackages = depsToInstall.some(pkg => 
      pkg.includes('+cu') || pkg.includes('torch') || pkg.includes('torchvision')
    );
    
    // Determine CUDA version from packages
    const detectedCudaVersion = depsToInstall.some(pkg => pkg.includes('+cu121')) ? 'cu121' : 'cu128';

    // Normalize CUDA package requirements (remove +cu128 from version ranges)
    const normalizeCudaPackage = (pkg) => {
      // If package has CUDA label with version range operators, strip the CUDA part
      // pip doesn't allow ranges like >=2.7.1+cu128, only exact versions like ==2.7.1+cu128
      if (pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg)) {
        return pkg.replace(/\+cu\d+/, '');
      }
      return pkg;
    };
    
    depsToInstall = depsToInstall.map(normalizeCudaPackage);

    // Calculate progress weights for each phase
    const priorityPkgs = depsToInstall.filter((d) => d.startsWith('torch'));
    const pytorch3dPkgs = depsToInstall.filter((d) => d.startsWith('pytorch3d'));
    const remainingPkgs = depsToInstall.filter((d) => !d.startsWith('torch') && !d.startsWith('pytorch3d'));
    
    const totalPkgs = depsToInstall.length;
    const progressWeights = {
      priority: priorityPkgs.length / totalPkgs * 0.4,  // 40% for torch packages
      pytorch3d: pytorch3dPkgs.length / totalPkgs * 0.3, // 30% for pytorch3d
      remaining: remainingPkgs.length / totalPkgs * 0.3  // 30% for remaining
    };

    let currentPhase = 0;
    const updateProgress = (phase, phaseProgress) => {
      const baseProgress = {
        0: 10,  // After initial checks
        1: progressWeights.priority * 100,
        2: (progressWeights.priority + progressWeights.pytorch3d) * 100,
        3: 100
      }[currentPhase];

      const phaseWeight = {
        0: progressWeights.priority,
        1: progressWeights.pytorch3d,
        2: progressWeights.remaining
      }[phase];

      let progress;
      if (phase === 2) {
        // For remaining packages, calculate progress based on current phase completion
        progress = Math.min(
          Math.round((progressWeights.priority + progressWeights.pytorch3d) * 100 + (phaseProgress * progressWeights.remaining * 100)),
          100
        );
      } else {
        progress = Math.min(
          Math.round(baseProgress + (phaseProgress * phaseWeight * 100)),
          phase === 2 ? 100 : baseProgress + (phaseWeight * 100)
        );
      }

      return progress;
    };

            const installBatch = (pkgs, batchLabel, extraArgs = [], phase) => {
          // Use CUDA detection from original packages (before normalization)
          const cudaIndexUrl = originalHasCudaPackages ? ['--index-url', 'https://download.pytorch.org/whl/cu128'] : [];
      return new Promise((resolve, reject) => {
        if (pkgs.length === 0) return resolve();

        let stderr = '';  // Initialize stderr variable
        let installationStarted = false;
        let lastProgress = 0;

        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Running',
          message: `Installing ${batchLabel}: ${pkgs.join(', ')}`,
          progress: updateProgress(phase, 0),
        });

        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
        
        // Add pipeline-specific environment variables
        const pipeline = this.pipelines[pipelineName];
        if (pipeline?.environment) {
          Object.assign(env, pipeline.environment);
        }

        let args = ['-m', 'pip', 'install', '--no-cache-dir', '--progress-bar', 'on', ...extraArgs];
        if (originalHasCudaPackages) {
          args.push('--extra-index-url', `https://download.pytorch.org/whl/${detectedCudaVersion}`);
        }
        args = [...args, ...pkgs];

        const installer = spawn(pythonExe, args, { env });

        const handleChunk = (chunk) => {
          const text = chunk.toString();
          stderr += text;

          // Look for pip installation progress indicators
          if (text.includes('Installing collected packages')) {
            installationStarted = true;
          }

          if (installationStarted) {
            // Update progress based on package installation status
            const newProgress = text.includes('Successfully installed') ? 1 : 0.5;
            if (newProgress > lastProgress) {
              lastProgress = newProgress;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Running',
                message: `Installing ${batchLabel}...`,
                progress: updateProgress(phase, newProgress),
            });
            }
          }
        };

        installer.stderr.on('data', handleChunk);
        installer.stdout.on('data', handleChunk);

        installer.on('error', (err) => {
          stderr += err.message;
        });

        installer.on('close', (code) => {
          if (code !== 0) {
            const fullErrorMessage = `Installation failed for ${batchLabel}. Stderr: ${stderr}`;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Error',
              message: `Installation failed for ${batchLabel}. See main logs for details.`,
              progress: updateProgress(phase, 1),
            });
            logger.error(fullErrorMessage);
            reject(new Error(fullErrorMessage));
          } else {
            currentPhase++;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: phase === 2 ? 'Complete' : 'Running',
              message: phase === 2 ? 'Installation complete.' : `${batchLabel} installed successfully.`,
              progress: phase === 2 ? 100 : updateProgress(phase, 1), // Always 100% when complete
            });
            resolve();
          }
        });
      });
    };

    try {
      // Install priority packages first (torch, etc.)
      if (priorityPkgs.length > 0) {
        await installBatch(priorityPkgs, 'Priority packages', [], 0);
      } else {
        currentPhase++;
      }

      // Install PyTorch3D if needed
      if (pytorch3dPkgs.length > 0) {
        // Determine Python and Torch versions
        let pyMajor = 3, pyMinor = 11;
        try {
          const res = await new Promise((resolve) => {
            const proc = spawn(pythonExe, ['-c', 'import sys; print(f"{sys.version_info.major},{sys.version_info.minor}")']);
            let out = '';
            proc.stdout.on('data', (d) => out += d.toString());
            proc.on('close', () => resolve(out.trim()));
            proc.on('error', () => resolve(''));
          });
          const parts = res.split(',');
          if (parts.length === 2) {
            pyMajor = parts[0];
            pyMinor = parts[1];
          }
        } catch {}

        const wheelUrl = `https://dl.fbaipublicfiles.com/pytorch3d/packaging/wheels/py${pyMajor}.${pyMinor}_cu118_pyt2.0/download.html`;
        await installBatch(pytorch3dPkgs, 'PyTorch3D', ['-f', wheelUrl], 1);
         } else {
        currentPhase++;
      }

      // Install remaining packages
      if (remainingPkgs.length > 0) {
        await installBatch(remainingPkgs, 'Remaining packages', [], 2);
        } else {
        currentPhase++;
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'Installation complete.',
          progress: 100,
        });
      }

    } catch (error) {
      throw error;
    }
  }

  // Direct Python dependencies installation without special case redirections
  // Used by module installation methods to avoid recursion
  async _installPythonDependenciesDirect(pipelineName, depsToInstall, installAll = false) {
    try {
      // Ensure virtual environment exists
      await this._ensureVenvExists(pipelineName);
    } catch (error) {
      logger.error(`Failed to create/verify virtual environment for ${pipelineName}:`, error);
      throw new Error(`Failed to create/verify virtual environment: ${error.message}`);
    }

    const SKIP_PKGS = new Set(['nvidia-index-url']); // Skip packages that are not actual dependencies
    depsToInstall = depsToInstall.filter(pkg => {
      // Ensure pkg is a string
      if (typeof pkg !== 'string') {
        logger.warn(`Skipping non-string dependency: ${JSON.stringify(pkg)}`);
        return false;
      }
      const match = pkg.match(/^[a-zA-Z0-9_-]+/);
      return match && !SKIP_PKGS.has(match[0]);
    });

    const pythonExe = this._getPythonExe(pipelineName);
    const progressName = installAll ? 'all' : depsToInstall.join(' ');

    // Initialize progress
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Starting installation...',
      progress: 0,
    });

    // --- Ensure pip, setuptools & wheel are current ---
    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Updating pip and core packages...',
      progress: 5,
    });

    await new Promise((resolve) => {
      const env = { ...process.env };
      const scriptsDir = path.dirname(pythonExe);
      delete env.PYTHONHOME;
      delete env.PYTHONPATH;
      env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

      const upgrader = spawn(pythonExe, ['-m', 'pip', 'install', '--upgrade', 'pip', 'setuptools', 'wheel'], { env });
      upgrader.on('error', () => resolve());
      upgrader.on('close', () => resolve());
    });

    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Running',
      message: 'Checking existing packages...',
      progress: 10,
    });

    // Filter out packages that are already satisfied so we don't reinstall.
    try {
      const check = await this._checkPythonDependencies(pipelineName, depsToInstall);
      const unsatisfied = [];
      for (const dep of depsToInstall) {
        const detail = check.details[dep];
        if (detail && detail.satisfied) {
          // Emit instant complete event for this dep if installing individually.
          if (!installAll) {
            this._sendProgress(pipelineName, 'python', dep, {
              status: 'Complete',
              message: 'Already installed',
              progress: 100,
            });
          }
        } else {
          unsatisfied.push(dep);
        }
      }
      depsToInstall = unsatisfied;
      if (depsToInstall.length === 0) {
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'All selected packages already installed.',
          progress: 100,
        });
        return;
      }
    } catch (e) {
      logger.warn('Unable to pre-check python deps:', e.message);
    }

    if (depsToInstall.length === 0) {
      this._sendProgress(pipelineName, 'python', progressName, {
        status: 'Complete',
        message: 'No dependencies to install.',
        progress: 100
      });
      return;
    }

    // Check if any packages need CUDA support (before normalization)
    const originalHasCudaPackages = depsToInstall.some(pkg =>
      pkg.includes('+cu') || pkg.includes('torch') || pkg.includes('torchvision')
    );

    // Determine CUDA version from packages
    const detectedCudaVersion = depsToInstall.some(pkg => pkg.includes('+cu121')) ? 'cu121' : 'cu128';

    // Normalize CUDA package requirements (remove +cu128 from version ranges)
    const normalizeCudaPackage = (pkg) => {
      // If package has CUDA label with version range operators, strip the CUDA part
      // pip doesn't allow ranges like >=2.7.1+cu128, only exact versions like ==2.7.1+cu128
      if (pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg)) {
        return pkg.replace(/\+cu\d+/, '');
      }
      return pkg;
    };

    depsToInstall = depsToInstall.map(normalizeCudaPackage);

    // Calculate progress weights for each phase
    const priorityPkgs = depsToInstall.filter((d) => d.startsWith('torch'));
    const pytorch3dPkgs = depsToInstall.filter((d) => d.startsWith('pytorch3d'));
    const remainingPkgs = depsToInstall.filter((d) => !d.startsWith('torch') && !d.startsWith('pytorch3d'));

    const totalPkgs = depsToInstall.length;
    const progressWeights = {
      priority: priorityPkgs.length / totalPkgs * 0.4,  // 40% for torch packages
      pytorch3d: pytorch3dPkgs.length / totalPkgs * 0.3, // 30% for pytorch3d
      remaining: remainingPkgs.length / totalPkgs * 0.3  // 30% for remaining
    };

    let currentPhase = 0;
    const updateProgress = (phase, phaseProgress) => {
      const baseProgress = {
        0: 10,  // After initial checks
        1: progressWeights.priority * 100,
        2: (progressWeights.priority + progressWeights.pytorch3d) * 100,
        3: 100
      }[currentPhase];

      const phaseWeight = {
        0: progressWeights.priority,
        1: progressWeights.pytorch3d,
        2: progressWeights.remaining
      }[phase];

      let progress;
      if (phase === 2) {
        // For remaining packages, calculate progress based on current phase completion
        progress = Math.min(
          Math.round((progressWeights.priority + progressWeights.pytorch3d) * 100 + (phaseProgress * progressWeights.remaining * 100)),
          100
        );
      } else {
        progress = Math.min(
          Math.round(baseProgress + (phaseProgress * phaseWeight * 100)),
          phase === 2 ? 100 : baseProgress + (phaseWeight * 100)
        );
      }

      return progress;
    };

    const installBatch = (pkgs, batchLabel, extraArgs = [], phase) => {
      return new Promise((resolve, reject) => {
        if (pkgs.length === 0) return resolve();

        let stderr = '';  // Initialize stderr variable
        let installationStarted = false;
        let lastProgress = 0;

        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Running',
          message: `Installing ${batchLabel}: ${pkgs.join(', ')}`,
          progress: updateProgress(phase, 0),
        });

        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

        // Add pipeline-specific environment variables
        const pipeline = this.pipelines[pipelineName];
        if (pipeline?.environment) {
          Object.assign(env, pipeline.environment);
        }

        let args = ['-m', 'pip', 'install', '--no-cache-dir', '--progress-bar', 'on', ...extraArgs];
        if (originalHasCudaPackages) {
          args.push('--extra-index-url', `https://download.pytorch.org/whl/${detectedCudaVersion}`);
        }
        args = [...args, ...pkgs];

        const installer = spawn(pythonExe, args, { env });

        const handleChunk = (chunk) => {
          const text = chunk.toString();
          stderr += text;

          // Look for pip installation progress indicators
          if (text.includes('Installing collected packages')) {
            installationStarted = true;
          }

          if (installationStarted) {
            // Update progress based on package installation status
            const newProgress = text.includes('Successfully installed') ? 1 : 0.5;
            if (newProgress > lastProgress) {
              lastProgress = newProgress;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Running',
                message: `Installing ${batchLabel}...`,
                progress: updateProgress(phase, newProgress),
            });
            }
          }
        };

        installer.stderr.on('data', handleChunk);
        installer.stdout.on('data', handleChunk);

        installer.on('error', (err) => {
          stderr += err.message;
        });

        installer.on('close', (code) => {
          if (code !== 0) {
            const fullErrorMessage = `Installation failed for ${batchLabel}. Stderr: ${stderr}`;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: 'Error',
              message: `Installation failed for ${batchLabel}. See main logs for details.`,
              progress: updateProgress(phase, 1),
            });
            logger.error(fullErrorMessage);
            reject(new Error(fullErrorMessage));
          } else {
            currentPhase++;
            this._sendProgress(pipelineName, 'python', progressName, {
              status: phase === 2 ? 'Complete' : 'Running',
              message: phase === 2 ? 'Installation complete.' : `${batchLabel} installed successfully.`,
              progress: phase === 2 ? 100 : updateProgress(phase, 1), // Always 100% when complete
            });
            resolve();
          }
        });
      });
    };

    try {
      // Install priority packages first (torch, etc.)
      if (priorityPkgs.length > 0) {
        await installBatch(priorityPkgs, 'Priority packages', [], 0);
      } else {
        currentPhase++;
      }

      // Install PyTorch3D if needed
      if (pytorch3dPkgs.length > 0) {
        // Determine Python and Torch versions
        let pyMajor = 3, pyMinor = 11;
        try {
          const res = await new Promise((resolve) => {
            const proc = spawn(pythonExe, ['-c', 'import sys; print(f"{sys.version_info.major},{sys.version_info.minor}")']);
            let out = '';
            proc.stdout.on('data', (d) => out += d.toString());
            proc.on('close', () => resolve(out.trim()));
            proc.on('error', () => resolve(''));
          });
          const parts = res.split(',');
          if (parts.length === 2) {
            pyMajor = parts[0];
            pyMinor = parts[1];
          }
        } catch {}

        const wheelUrl = `https://dl.fbaipublicfiles.com/pytorch3d/packaging/wheels/py${pyMajor}.${pyMinor}_cu118_pyt2.0/download.html`;
        await installBatch(pytorch3dPkgs, 'PyTorch3D', ['-f', wheelUrl], 1);
         } else {
        currentPhase++;
      }

      // Install remaining packages
      if (remainingPkgs.length > 0) {
        await installBatch(remainingPkgs, 'Remaining packages', [], 2);
        } else {
        currentPhase++;
        this._sendProgress(pipelineName, 'python', progressName, {
          status: 'Complete',
          message: 'Installation complete.',
          progress: 100,
        });
      }

    } catch (error) {
      throw error;
    }
  }

  async _createVenv(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    
    // Ensure Python is installed for this pipeline first
    await this._ensurePythonInstalled(pipelineName);
    const pythonPath = this._getPythonPath(pipelineName);
    
    // Create the pipeline directory structure if it doesn't exist
    await fs.mkdir(path.dirname(venvPath), { recursive: true });
    
    try {
      await this._runCommand(pythonPath, ['-m', 'virtualenv', venvPath]);
      return venvPath;
    } catch (error) {
      logger.error(`Failed to create virtual environment for ${pipelineName}:`, error);
      throw error;
    }
  }

  async _installSpecialPackages(pipelineName, specialPackages) {
    try {
      // Ensure virtual environment exists
      await this._ensureVenvExists(pipelineName);
    } catch (error) {
      logger.error(`Failed to create/verify virtual environment for ${pipelineName}:`, error);
      throw new Error(`Failed to create/verify virtual environment: ${error.message}`);
    }

    const pythonExe = this._getPythonExe(pipelineName);
    const progressName = specialPackages.length === 1 ? specialPackages[0].name : 'special_packages';
    const installedPackages = [];
    const failedPackages = [];

    for (let i = 0; i < specialPackages.length; i++) {
      const pkg = specialPackages[i];
      const progress = Math.round(((i + 1) / specialPackages.length) * 100);

      this._sendProgress(pipelineName, 'python', progressName, {
        status: 'Running',
        message: `Installing special package: ${pkg.name}${pkg.version ? '@' + pkg.version : ''}`,
        progress: Math.round((i / specialPackages.length) * 100),
      });

      try {
        await new Promise((resolve, reject) => {
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }

          let args;
          let command = pythonExe;
          let cwd = this._getPipelineDir(pipelineName);

          // Handle git_clone type packages
          if (pkg.type === 'git_clone') {
            const targetPath = path.join(cwd, pkg.target_dir);

            // Check if already cloned
            if (fsSync.existsSync(targetPath)) {
              logger.info(`Repository ${pkg.name} already exists at ${targetPath}, skipping clone`);
              resolve();
              return;
            }

            logger.info(`Cloning repository ${pkg.url} to ${targetPath}`);

            // Get Git executable (system or portable)
            command = this._getGitExecutable();
            args = ['clone', pkg.url, pkg.target_dir];

          } else if (pkg.install_command) {
            // Handle different types of install commands
            if (pkg.install_command.startsWith('pip install')) {
              // Standard pip install command
              const commandParts = pkg.install_command.split(' ');
              commandParts.shift(); // Remove 'pip'
              args = ['-m', 'pip', ...commandParts];
            } else if (pkg.install_command.includes('cd ') && pkg.install_command.includes('&&')) {
              // Handle complex commands with cd and multiple steps
              const fullCommand = pkg.install_command;

              // For Windows, use cmd.exe to handle cd and && properly
              command = 'cmd.exe';
              args = ['/c', fullCommand];

              // Set working directory to pipeline directory
              cwd = this._getPipelineDir(pipelineName);
            } else if (pkg.install_command.startsWith('mkdir') || pkg.install_command.startsWith('curl')) {
              // Handle shell commands directly
              command = 'cmd.exe';
              args = ['/c', pkg.install_command];
            } else {
              // Default: assume it's a pip command
              const commandParts = pkg.install_command.split(' ');
              if (commandParts[0] === 'pip') {
                commandParts.shift();
              }
              args = ['-m', 'pip', ...commandParts];
            }
          } else if (pkg.install_url && pkg.install_url.endsWith('.whl')) {
            // Direct wheel installation
            args = ['-m', 'pip', 'install', pkg.install_url];
          } else if (pkg.install_url) {
            // Installation with find-links
            args = ['-m', 'pip', 'install', `${pkg.name}==${pkg.version}`, '-f', pkg.install_url];
          } else if (pkg.type !== 'git_clone') {
            throw new Error(`Package ${pkg.name} has no install_command, install_url, or valid type specified`);
          }

          logger.info(`[${pipelineName}] Executing: ${command} ${args.join(' ')}`);
          logger.info(`[${pipelineName}] Working directory: ${cwd}`);

          const installer = spawn(command, args, { env, cwd });

          let stderr = '';
          const handleChunk = (chunk) => {
            stderr += chunk.toString();
          };

          installer.stderr.on('data', handleChunk);
          installer.stdout.on('data', handleChunk);

          installer.on('error', (err) => {
            stderr += err.message;
          });

          installer.on('close', (code) => {
            if (code !== 0) {
              const errorMessage = `Failed to install special package ${pkg.name}. Stderr: ${stderr}`;
              logger.error(errorMessage);
              reject(new Error(errorMessage));
            } else {
              logger.info(`Successfully installed special package ${pkg.name}@${pkg.version}`);
              resolve();
            }
          });
        });
        
        installedPackages.push(pkg);
        logger.info(`Successfully installed special package ${pkg.name}@${pkg.version}`);
      } catch (error) {
        if (pkg.required === false) {
          logger.warn(`Optional special package ${pkg.name} failed to install: ${error.message}`);
          failedPackages.push(pkg);
          
          // Handle fallback behavior for optional packages
          if (pkg.fallback_backend) {
            logger.info(`Using fallback backend '${pkg.fallback_backend}' for ${pkg.name}`);
            // Update environment variables for fallback
            const pipeline = this.pipelines[pipelineName];
            if (pipeline?.environment) {
              if (pkg.name === 'flash-attn' && pkg.fallback_backend === 'math') {
                pipeline.environment.TRELLIS_ATTN_BACKEND = 'math';
                logger.info(`Set TRELLIS_ATTN_BACKEND to 'math' as fallback for flash-attn`);
              }
            }
          }
          
          this._sendProgress(pipelineName, 'python', progressName, {
            status: 'Running',
            message: `Skipped optional package ${pkg.name} (using fallback: ${pkg.fallback_backend || 'none'})`,
            progress: progress,
          });
        } else {
          this._sendProgress(pipelineName, 'python', progressName, {
            status: 'Error',
            message: `Failed to install required ${pkg.name}: ${error.message}`,
            progress: progress,
          });
          throw error;
        }
      }
    }

    const successMessage = installedPackages.length > 0 
      ? `Special packages installed: ${installedPackages.map(p => p.name).join(', ')}`
      : 'No special packages were installed';
    
    if (failedPackages.length > 0) {
      logger.info(`Failed optional packages: ${failedPackages.map(p => p.name).join(', ')}`);
    }

    this._sendProgress(pipelineName, 'python', progressName, {
      status: 'Complete',
      message: successMessage,
      progress: 100,
    });
  }

  async _checkPythonDependencies(pipelineName, requiredDeps) {
    // Special case: Microsoft_TRELLIS uses validation based on venv folder contents
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      const isValid = await this._validateTrellisInstallation();
      const result = { satisfied: isValid, details: {} };

      // Handle system package dependencies
      for (const dep of requiredDeps) {
        if (typeof dep === 'object' && dep.system_package) {
          // This is a system package - check if Trellis server environment is installed
          result.details[dep.name] = {
            satisfied: isValid,
            installed: isValid ? 'System Package Installed' : 'System Package Not Installed',
            required: dep.name,
            description: dep.description
          };
        } else {
          // Legacy string dependency format
          result.details[dep] = {
            satisfied: isValid,
            installed: isValid ? 'Installed' : 'Not installed',
            required: dep
          };
        }
      }

      logger.info(`Microsoft_TRELLIS dependency validation: ${isValid ? 'PASSED' : 'FAILED'}`);
      return result;
    }

    // Special case: Hunyuan3D-2.1 uses validation based on installation marker
    if (pipelineName === 'v13_hunyuan2-stableprojectorz') {
      const isValid = await this._validateHunyuan21Installation();
      const result = { satisfied: isValid, details: {} };

      // Handle system package dependencies
      for (const dep of requiredDeps) {
        if (typeof dep === 'object' && dep.system_package) {
          // This is a system package - check if Hunyuan server environment is installed
          result.details[dep.name] = {
            satisfied: isValid,
            installed: isValid ? 'System Package Installed' : 'System Package Not Installed',
            required: dep.name,
            description: dep.description
          };
        } else {
          // Legacy string dependency format
          result.details[dep] = {
            satisfied: isValid,
            installed: isValid ? 'Installed' : 'Not installed',
            required: dep
          };
        }
      }

      logger.info(`Hunyuan3D-2.1 dependency validation: ${isValid ? 'PASSED' : 'FAILED'}`);
      return result;
    }

  // Special case: ImageGeneration - check all dependencies but with optimized checking
  if (pipelineName === 'ImageGeneration') {
      // First check if we have a cached status that's recent (within 5 minutes)
      const cacheKey = `deps_${pipelineName}`;
      const now = Date.now();
      const cache = this.dependencyCache && this.dependencyCache[cacheKey];
      
      if (cache && (now - cache.timestamp < 5 * 60 * 1000)) {
        logger.info(`Using cached dependency status for ${pipelineName}`);
        return cache.status;
      }
      
      // For ImageGeneration, we'll check all dependencies but with a timeout
      const pythonExe = this._getPythonExe(pipelineName);
      
      try {
        // Check if Python is accessible
        await fs.access(pythonExe);
        
        // Build full result.details with all configured packages so UI displays the full list
        const result = { satisfied: true, details: {} };
        for (const dep of requiredDeps) {
          const pkgName = dep.split(/[<>=!]/)[0].trim();
          result.details[pkgName] = { satisfied: false, installed: 'Not checked', required: dep };
        }

        // Check a few key packages first for quick validation and update the full list
        const keyDeps = ['torch', 'diffusers', 'transformers', 'pillow', 'numpy', 'opencv-python-headless'];
        const toCheck = requiredDeps.filter(dep => 
          keyDeps.some(key => dep.toLowerCase().startsWith(key.split('>')[0].split('<')[0].split('=')[0]))
        );

        if (toCheck.length > 0) {
          logger.info(`ImageGeneration: Checking ${toCheck.length} key dependencies for validation`);
          // Local quick-check helper (uses same technique as the generic checker)
          const checkPkg = async (pkg) => {
            const normalizedPkg = pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg) 
              ? pkg.replace(/\+cu\d+/, '') 
              : pkg;
            const name = normalizedPkg.match(/^[a-zA-Z0-9_-]+/)[0];
            try {
              const env = { ...process.env };
              const scriptsDir = path.dirname(pythonExe);
              delete env.PYTHONHOME;
              delete env.PYTHONPATH;
              env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
              const output = await Promise.race([
                new Promise((resolve) => {
                  const proc = spawn(pythonExe, [
                    '-c',
                    `import importlib.metadata as m; print(m.version('${name}'))`,
                  ], { env });
                  let out = '';
                  proc.stdout.on('data', (d) => out += d.toString());
                  proc.on('close', () => resolve(out.trim()));
                  proc.on('error', () => resolve(''));
                }),
                new Promise((resolve) => setTimeout(() => resolve(''), 5000))
              ]);

              if (!output) return { satisfied: false, installed: 'Not installed', required: pkg };
              return { satisfied: true, installed: output.trim(), required: pkg };
            } catch (e) {
              return { satisfied: false, installed: 'Not installed', required: pkg };
            }
          };

          const checks = await Promise.all(toCheck.map(dep => checkPkg(dep)));
          toCheck.forEach((dep, idx) => {
            const pkgName = dep.split(/[<>=!]/)[0].trim();
            const res = checks[idx];
            result.details[pkgName] = res;
            if (!res.satisfied) result.satisfied = false;
          });
        }
        
  // Update cache with full details (some entries may remain 'Not checked')
  this.dependencyCache = this.dependencyCache || {};
  this.dependencyCache[cacheKey] = { timestamp: now, status: result };
  return result;
        
      } catch (error) {
        logger.error(`Error checking ImageGeneration dependencies: ${error.message}`);
        // Return a failed status but don't cache it
        return {
          satisfied: false,
          details: requiredDeps.reduce((acc, dep) => {
            const pkgName = dep.split(/[<>=!]/)[0].trim();
            acc[pkgName] = {
              satisfied: false,
              installed: 'Error checking',
              required: dep,
              error: error.message
            };
            return acc;
          }, {})
        };
      }
    }

    const pythonExe = this._getPythonExe(pipelineName);
    const result = { satisfied: true, details: {} };

    // Check if Python executable exists first
    try {
      await fs.access(pythonExe);
    } catch {
      // Python executable doesn't exist, mark all dependencies as not installed
      for (const dep of requiredDeps) {
        result.details[dep] = { satisfied: false, installed: 'Python not found', required: dep };
        result.satisfied = false;
      }
      return result;
    }

    // Helper to check a single package with timeout
    const checkPackage = async (pkg) => {
      // Normalize CUDA package requirements for checking (same as installation)
      const normalizedPkg = pkg.includes('+cu') && /[><=]/.test(pkg) && !/==/.test(pkg) 
        ? pkg.replace(/\+cu\d+/, '') 
        : pkg;
      
      const name = normalizedPkg.match(/^[a-zA-Z0-9_-]+/)[0];
      const version = normalizedPkg.match(/[><=]=\s*([0-9.]+)/)?.[1];
      const cudaTag = pkg.match(/\+cu(\d+)/)?.[1]; // Keep original for CUDA detection

      try {
          const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
        delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }
          
        // Add timeout to prevent hanging
        const output = await Promise.race([
          new Promise((resolve) => {
            const proc = spawn(pythonExe, [
              '-c',
              `import importlib.metadata as m; print(m.version('${name}'))`,
            ], { env });
            let out = '';
            proc.stdout.on('data', (d) => out += d.toString());
            proc.on('close', () => resolve(out.trim()));
            proc.on('error', () => resolve(''));
          }),
          new Promise((resolve) => setTimeout(() => resolve(''), 5000)) // 5 second timeout
        ]);

        if (!output) {
          return { satisfied: false, installed: 'Not installed', required: pkg };
        }

        const installed = output.trim();
        if (version) {
          // Normalize both installed and required versions for comparison
          // Strip CUDA suffixes like +cu128 for semver comparison
          const normalizedInstalled = installed.replace(/\+cu\d+/, '');
          const normalizedRequired = version.replace(/\+cu\d+/, '');
          
          // Use custom version comparison for multi-component versions (like *********)
          const compareVersions = (installed, required) => {
            const installedParts = installed.split('.').map(Number);
            const requiredParts = required.split('.').map(Number);
            
            const maxLength = Math.max(installedParts.length, requiredParts.length);
            
            for (let i = 0; i < maxLength; i++) {
              const installedPart = installedParts[i] || 0;
              const requiredPart = requiredParts[i] || 0;
              
              if (installedPart > requiredPart) return 1;
              if (installedPart < requiredPart) return -1;
            }
            return 0;
          };
          
          const satisfied = compareVersions(normalizedInstalled, normalizedRequired) >= 0;
          
          // Debug logging for version comparison
          if (!satisfied) {
            logger.warn(`Version mismatch for ${name}: installed ${installed} (normalized: ${normalizedInstalled}) does not satisfy >=${normalizedRequired}`);
          }
          
          return {
            satisfied,
            installed,
            required: pkg
          };
        }

        return { satisfied: true, installed, required: pkg };
      } catch (e) {
        return { satisfied: false, installed: 'Not installed', required: pkg };
      }
    };

    // Check each required dependency
    for (const dep of requiredDeps) {
      try {
        const status = await checkPackage(dep);
        result.details[dep] = status;
        if (!status.satisfied) {
          result.satisfied = false;
        }
      } catch (error) {
        logger.warn(`Failed to check dependency ${dep} for ${pipelineName}:`, error);
        result.details[dep] = { satisfied: false, installed: 'Check failed', required: dep };
        result.satisfied = false;
      }
    }

    return result;
  }

  async _checkModelDependencies(pipelineName, requiredModels) {
    const result = { satisfied: true, details: {} };
    for (const model of requiredModels) {
      try {
        let localPath;
        // For upscaler models, always check models/upscaling/{model.name}/
        const upscalerNames = [
          'swinir-real-sr-x4',
          'realesrgan-x4plus',
          'realesrgan-x4plus-anime',
          'swinir-m-x4',
          '4xultrasharp'
        ];
        if (upscalerNames.includes(model.name)) {
          localPath = path.join(MODELS_DIR, 'upscaling', model.name);
        } else if (model.local_path) {
          // Prioritize local_path when specified
          localPath = path.isAbsolute(model.local_path)
            ? model.local_path
            : path.join(MODELS_DIR, model.local_path);
        } else if (model.repo_id) {
          // Fallback to repo_id-based path for Hugging Face models
          const cacheDir = path.join(MODELS_DIR, pipelineName);
          if (model.subfolder) {
            localPath = path.join(cacheDir, model.repo_id.replace('/', '--'), model.subfolder);
          } else {
            localPath = path.join(cacheDir, model.repo_id.replace('/', '--'));
          }
        } else {
          throw new Error(`Model ${model.name} has no repo_id or local_path specified`);
        }
        logger.info(`Checking model ${model.name} at path: ${localPath}`);

        // Special debug logging for FramePack models
        if (model.name === 'framepack-transformer') {
          logger.info(`[DEBUG] FramePack model validation - checking path: ${localPath}`);
          logger.info(`[DEBUG] FramePack model config: ${JSON.stringify(model, null, 2)}`);
        }
        
        // Check if model directory exists and contains required files
        let isInstalled = false;
        try {
          await fs.access(localPath);
          const files = await fs.readdir(localPath);
          
          // Special handling for FLUX model - check for specific required files
          if (model.name === 'flux-dev') {
            const requiredFiles = [
              'vae/config.json',
              'text_encoder_2',
              'transformer'
            ];
            
            // Check if all required files/directories exist
            const fileChecks = await Promise.all(requiredFiles.map(async (filePath) => {
              try {
                await fs.access(path.join(localPath, filePath));
                return true;
              } catch {
                return false;
              }
            }));
            
            isInstalled = fileChecks.every(exists => exists);
            logger.info(`FLUX model check - Required files: ${requiredFiles.join(', ')}`);
            logger.info(`FLUX model check - Files found: ${fileChecks.map((found, i) => `${requiredFiles[i]}: ${found}`).join(', ')}`);
          }
          // For Stable Diffusion models, check for basic model structure
          else if (model.name.startsWith('stable-diffusion') || model.name.startsWith('sdxl')) {
            // First check for model_index.json (primary indicator)
            try {
              await fs.access(path.join(localPath, 'model_index.json'));
              isInstalled = true;
              logger.info(`Stable Diffusion model check - model_index.json found for ${model.name}`);
            } catch {
              // Fallback: Check for basic model structure - should have at least one of these directories
              const sdModelDirs = ['unet', 'vae', 'text_encoder', 'scheduler'];
              const dirChecks = await Promise.all(sdModelDirs.map(async (dirName) => {
                try {
                  await fs.access(path.join(localPath, dirName));
                  return true;
                } catch {
                  return false;
                }
              }));

              isInstalled = dirChecks.some(exists => exists);
              logger.info(`Stable Diffusion model check - Required dirs: ${sdModelDirs.join(', ')}`);
              logger.info(`Stable Diffusion model check - Dirs found: ${dirChecks.map((found, i) => `${sdModelDirs[i]}: ${found}`).join(', ')}`);
            }
          }
          // For Hunyuan3D-2.1 models, check for specific model files
          else if (model.name.includes('Hunyuan3D-2.1')) {
            // Check for common model files that should exist
            const modelFiles = ['config.json', 'model.safetensors', 'pytorch_model.bin'];
            const fileChecks = await Promise.all(modelFiles.map(async (fileName) => {
              try {
                await fs.access(path.join(localPath, fileName));
                return true;
              } catch {
                return false;
              }
            }));

            // Model is installed if at least one of the expected files exists
            isInstalled = fileChecks.some(exists => exists) || files.length > 5; // Or if directory has substantial content
            logger.info(`Hunyuan3D-2.1 model check - Expected files: ${modelFiles.join(', ')}`);
            logger.info(`Hunyuan3D-2.1 model check - Files found: ${fileChecks.map((found, i) => `${modelFiles[i]}: ${found}`).join(', ')}`);
            logger.info(`Hunyuan3D-2.1 model check - Total files in directory: ${files.length}`);
          }
          // For upscaler models, check for required file(s)
          else if (upscalerNames.includes(model.name) && model.files && model.files.length > 0) {
            isInstalled = model.files.every(f => files.includes(f));
          } else if (files.length > 0) {
            isInstalled = true;
          } else {
            logger.warn(`Model ${model.name} directory exists but is empty: ${localPath}`);
          }
        } catch (accessError) {
          logger.info(`Model ${model.name} directory does not exist: ${localPath}`);
        }
        
        result.details[model.name] = {
          name: model.name,
          repo_id: model.repo_id,
          installed: isInstalled,
          required: model.required,
          description: model.description,
          local_path: localPath
        };
        if (!isInstalled && model.required) {
          logger.warn(`Required model ${model.name} is not properly installed`);
          result.satisfied = false;
        }
      } catch (error) {
        logger.error(`Error checking model ${model.name}:`, error);
        result.details[model.name] = {
          name: model.name,
          repo_id: model.repo_id,
          installed: false,
          required: model.required,
          description: model.description || '',
          error: error.message
        };
        if (model.required) {
          result.satisfied = false;
        }
      }
    }
    logger.info(`Model dependency check for ${pipelineName}: ${result.satisfied ? 'satisfied' : 'not satisfied'}`);
    return result;
  }

  async _ensurePythonInstalled(pipelineName) {
    const pythonPath = this._getPythonPath(pipelineName);
    const pythonDir = path.dirname(pythonPath);
    
    try {
      // Check if Python is already installed for this pipeline
      await fs.access(pythonPath);
      logger.info(`Python detected for pipeline ${pipelineName}, checking version...`);
      
      // Check Python version to ensure it's 3.11.8
      let pythonVersion = null;
      try {
        pythonVersion = await new Promise((resolve, reject) => {
          const versionCheck = spawn(pythonPath, ['--version'], {
            stdio: 'pipe'
          });
          let output = '';
          versionCheck.stdout.on('data', (data) => {
            output += data.toString();
          });
          versionCheck.stderr.on('data', (data) => {
            output += data.toString();
          });
          versionCheck.on('close', (code) => {
            if (code === 0) {
              const match = output.match(/Python (\d+\.\d+\.\d+)/);
              resolve(match ? match[1] : null);
            } else {
              reject(new Error('Failed to get Python version'));
            }
          });
          versionCheck.on('error', () => reject(new Error('Failed to run Python version check')));
        });
      } catch (error) {
        logger.warn(`Failed to check Python version for ${pipelineName}: ${error.message}`);
        pythonVersion = null;
      }
      
      if (pythonVersion !== '3.11.8') {
        logger.warn(`Incorrect Python version detected for ${pipelineName}: ${pythonVersion || 'unknown'}, expected 3.11.8`);
        logger.info(`Removing existing Python installation and reinstalling 3.11.8 for ${pipelineName}`);
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Running',
          message: `Upgrading Python to 3.11.8...`,
          progress: 10
        });
        
        // Remove existing Python installation
        try {
          await fs.rm(pythonDir, { recursive: true, force: true });
          logger.info(`Removed existing Python installation for ${pipelineName}`);
        } catch (error) {
          logger.warn(`Failed to remove existing Python for ${pipelineName}: ${error.message}`);
        }
        
        // Fall through to reinstall Python 3.11.8
        throw new Error('Python version mismatch, reinstalling');
      }
      
      logger.info(`Correct Python version (3.11.8) confirmed for pipeline ${pipelineName}`);
      
      // Also check if virtualenv is available, install if missing
      try {
        await new Promise((resolve, reject) => {
          const checkVenv = spawn(pythonPath, ['-m', 'virtualenv', '--version'], {
            stdio: 'pipe'
          });
          checkVenv.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error('virtualenv not found'));
            }
          });
          checkVenv.on('error', () => reject(new Error('virtualenv not found')));
        });
        logger.info(`Virtualenv already available for pipeline ${pipelineName}`);
      } catch {
        // Virtualenv not available, install it
        logger.info(`Installing virtualenv for existing Python in pipeline ${pipelineName}`);
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Running',
          message: `Installing virtualenv...`,
          progress: 50
        });
        
        await new Promise((resolve, reject) => {
          const installer = spawn(pythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
            stdio: 'pipe'
          });

          installer.on('error', (err) => {
            reject(new Error(`Failed to install virtualenv: ${err.message}`));
          });

          installer.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Virtualenv installation failed with code ${code}`));
            }
          });
        });
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Complete',
          message: `Virtualenv installed for ${pipelineName}`,
          progress: 100
        });
      }
      
      return true;
    } catch {
      // Python not found, download and install it
      logger.info(`Installing Python 3.11.8 for pipeline ${pipelineName}...`);
      
      // Send progress update
      this._sendProgress(pipelineName, 'python', 'setup', {
        status: 'Running',
        message: `Setting up Python 3.11.8 for ${pipelineName}...`,
        progress: 10
      });
      
      try {
        // Create Python directory for this pipeline
        await fs.mkdir(pythonDir, { recursive: true });
        
        // Check if we have the installer in _InstallFirst
        const installerPath = path.join(PYTHON_INSTALL_DIR, 'python-3.11.9-amd64.exe');
        
                try {
          await fs.access(installerPath);
          
          // Use embedded Python distribution instead of installer for better control
          logger.info(`Setting up Python from embedded distribution for ${pipelineName}...`);
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Setting up Python environment...`,
            progress: 30
          });
          
          // Download Python embedded distribution (more portable and reliable)
          const embedUrl = 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-embed-amd64.zip';
          const tempZip = path.join(pythonDir, 'python-embed.zip');
          
          // Download embedded Python
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(tempZip);
            https.get(embedUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(tempZip).catch(() => {});
              reject(new Error(`Failed to download Python embedded: ${err.message}`));
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Extracting Python...`,
            progress: 50
          });
          
          // Extract Python embedded
          const zip = new AdmZip(tempZip);
          zip.extractAllTo(pythonDir, true);
          
          // Clean up zip file
          await fs.unlink(tempZip).catch(() => {});
          
          // Enable pip by modifying python311._pth file
          const pthFile = path.join(pythonDir, 'python311._pth');
          try {
            let pthContent = await fs.readFile(pthFile, 'utf8');
            // Uncomment the import site line to enable pip
            pthContent = pthContent.replace('#import site', 'import site');
            await fs.writeFile(pthFile, pthContent);
          } catch (err) {
            logger.warn(`Could not modify python311._pth: ${err.message}`);
          }
          
          // Download and install pip manually for embedded Python
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing pip...`,
            progress: 70
          });
          
          const getpipUrl = 'https://bootstrap.pypa.io/get-pip.py';
          const getpipPath = path.join(pythonDir, 'get-pip.py');
          
          // Download get-pip.py
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(getpipPath);
            https.get(getpipUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(getpipPath).catch(() => {});
              reject(new Error(`Failed to download get-pip.py: ${err.message}`));
            });
          });
          
          // Install pip
          const currentPythonPath = this._getPythonPath(pipelineName);
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, [getpipPath], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install pip: ${err.message}`));
            });

            installer.on('close', (code) => {
              // Clean up get-pip.py
              fs.unlink(getpipPath).catch(() => {});
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Pip installation failed with code ${code}`));
              }
            });
          });
          
          // Install virtualenv since embedded Python doesn't include venv module
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing virtualenv...`,
            progress: 80
          });
          
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install virtualenv: ${err.message}`));
            });

            installer.on('close', (code) => {
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Virtualenv installation failed with code ${code}`));
              }
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Python setup completed, verifying...`,
            progress: 85
          });
           
                 } catch {
          // No local installer, use embedded Python distribution directly
          logger.info(`Downloading Python embedded distribution for ${pipelineName}...`);
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Downloading Python 3.11.8...`,
            progress: 20
          });
          
          const embedUrl = 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-embed-amd64.zip';
          const tempZip = path.join(pythonDir, 'python-embed.zip');
          
          // Download embedded Python
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(tempZip);
            https.get(embedUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(tempZip).catch(() => {});
              reject(new Error(`Failed to download Python embedded: ${err.message}`));
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Extracting Python...`,
            progress: 40
          });
          
          // Extract Python embedded
          const zip = new AdmZip(tempZip);
          zip.extractAllTo(pythonDir, true);
          
          // Clean up zip file
          await fs.unlink(tempZip).catch(() => {});
          
          // Enable pip by modifying python311._pth file
          const pthFile = path.join(pythonDir, 'python311._pth');
          try {
            let pthContent = await fs.readFile(pthFile, 'utf8');
            // Uncomment the import site line to enable pip
            pthContent = pthContent.replace('#import site', 'import site');
            await fs.writeFile(pthFile, pthContent);
          } catch (err) {
            logger.warn(`Could not modify python311._pth: ${err.message}`);
          }
          
          // Download and install pip manually for embedded Python
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing pip...`,
            progress: 70
          });
          
          const getpipUrl = 'https://bootstrap.pypa.io/get-pip.py';
          const getpipPath = path.join(pythonDir, 'get-pip.py');
          
          // Download get-pip.py
          await new Promise((resolve, reject) => {
            const file = fsSync.createWriteStream(getpipPath);
            https.get(getpipUrl, (response) => {
              response.pipe(file);
              file.on('finish', () => {
                file.close();
                resolve();
              });
            }).on('error', (err) => {
              fs.unlink(getpipPath).catch(() => {});
              reject(new Error(`Failed to download get-pip.py: ${err.message}`));
            });
          });
          
          // Install pip
          const currentPythonPath = this._getPythonPath(pipelineName);
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, [getpipPath], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install pip: ${err.message}`));
            });

            installer.on('close', (code) => {
              // Clean up get-pip.py
              fs.unlink(getpipPath).catch(() => {});
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Pip installation failed with code ${code}`));
              }
            });
          });
          
          // Install virtualenv since embedded Python doesn't include venv module
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Installing virtualenv...`,
            progress: 80
          });
          
          await new Promise((resolve, reject) => {
            const installer = spawn(currentPythonPath, ['-m', 'pip', 'install', 'virtualenv'], {
              cwd: pythonDir,
              stdio: 'pipe'
            });

            installer.on('error', (err) => {
              reject(new Error(`Failed to install virtualenv: ${err.message}`));
            });

            installer.on('close', (code) => {
              if (code === 0) {
                resolve();
              } else {
                reject(new Error(`Virtualenv installation failed with code ${code}`));
              }
            });
          });
          
          this._sendProgress(pipelineName, 'python', 'setup', {
            status: 'Running',
            message: `Python setup completed, verifying...`,
            progress: 85
          });
        }
         
         // Verify Python installation
        const finalPythonPath = this._getPythonPath(pipelineName);
        await fs.access(finalPythonPath);
        logger.info(`Python 3.11.8 successfully installed for pipeline ${pipelineName}`);
        
        this._sendProgress(pipelineName, 'python', 'setup', {
          status: 'Complete',
          message: `Python 3.11.8 ready for ${pipelineName}`,
          progress: 100
        });
        
        return true;
        
      } catch (error) {
        logger.error(`Failed to install Python for pipeline ${pipelineName}:`, error);
        throw new Error(`Failed to install Python 3.11.8: ${error.message}`);
      }
    }
  }

  async _ensureVenvExists(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    const pythonExe = this._getPythonExe(pipelineName);
    const pipeline = this.pipelines[pipelineName];
    const requiredPythonVersion = pipeline?.dependencies?.python_version;

    try {
      // Check if venv exists and is valid
      await fs.access(pythonExe);

      // If a specific Python version is required, verify it
      if (requiredPythonVersion) {
        try {
          const versionCheck = await new Promise((resolve, reject) => {
            const versionProcess = spawn(pythonExe, ['--version']);
            let output = '';

            versionProcess.stdout.on('data', (data) => {
              output += data.toString();
            });

            versionProcess.stderr.on('data', (data) => {
              output += data.toString();
            });

            versionProcess.on('close', (code) => {
              if (code === 0) {
                resolve(output.trim());
              } else {
                reject(new Error(`Failed to check Python version: ${output}`));
              }
            });
          });

          const currentVersion = versionCheck.match(/Python (\d+\.\d+)/)?.[1];
          if (currentVersion !== requiredPythonVersion) {
            logger.info(`[${pipelineName}] Python version mismatch. Required: ${requiredPythonVersion}, Current: ${currentVersion}. Recreating venv...`);
            throw new Error('Python version mismatch');
          }
        } catch (error) {
          logger.info(`[${pipelineName}] Python version check failed: ${error.message}. Recreating venv...`);
          throw error;
        }
      }

      return true;
    } catch {
      // Create new venv if it doesn't exist or is invalid
      try {
        // Ensure Python is installed for this pipeline
        await this._ensurePythonInstalled(pipelineName);
        let pythonPath = this._getPythonPath(pipelineName);

        // If a specific Python version is required, try to find it
        if (requiredPythonVersion) {
          const specificPythonPath = await this._findPythonVersion(requiredPythonVersion);
          if (specificPythonPath) {
            pythonPath = specificPythonPath;
            logger.info(`[${pipelineName}] Using Python ${requiredPythonVersion} at: ${pythonPath}`);
          } else {
            logger.warn(`[${pipelineName}] Python ${requiredPythonVersion} not found, using default: ${pythonPath}`);
          }
        }

        // Remove existing venv if it's corrupted
        try {
          await fs.rm(venvPath, { recursive: true, force: true });
        } catch {}

        // Create venv directory
        await fs.mkdir(path.dirname(venvPath), { recursive: true });

        // Create new venv using virtualenv (since embedded Python doesn't have venv module)
        await new Promise((resolve, reject) => {
          const pipelineEnv = { ...process.env };
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(pipelineEnv, pipeline.environment);
          }
          
          // Clear Python environment variables to avoid conflicts
          pipelineEnv.PYTHONPATH = '';
          pipelineEnv.PYTHONHOME = '';
          
          const venvProcess = spawn(pythonPath, ['-m', 'virtualenv', venvPath], {
            env: pipelineEnv
          });

          venvProcess.stderr.on('data', (data) => {
            logger.error(`Venv creation stderr: ${data}`);
          });

          venvProcess.on('error', (err) => {
            reject(new Error(`Failed to create virtual environment: ${err.message}`));
          });

          venvProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
        } else {
              reject(new Error(`Virtual environment creation failed with code ${code}`));
            }
          });
        });

        // Upgrade pip in the new venv
        await new Promise((resolve, reject) => {
          const pipelineEnv = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(pipelineEnv, pipeline.environment);
          }
          
          pipelineEnv.PYTHONPATH = '';
          pipelineEnv.PYTHONHOME = '';
          pipelineEnv.PATH = `${scriptsDir}${path.delimiter}${pipelineEnv.PATH}`;
          
          const pipUpgrade = spawn(pythonExe, ['-m', 'pip', 'install', '--no-cache-dir', '--upgrade', 'pip'], {
            env: pipelineEnv
          });

          pipUpgrade.on('error', (err) => {
            reject(new Error(`Failed to upgrade pip: ${err.message}`));
          });

          pipUpgrade.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Pip upgrade failed with code ${code}`));
            }
          });
        });

        return true;
            } catch (error) {
        logger.error(`Failed to create virtual environment for ${pipelineName}:`, error);
        throw error;
      }
    }
  }

  async _installFluxModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    logger.info(`Installing FLUX model ${model.name} using diffusers to: ${localPath}`);
    logger.info(`Token provided: ${hfToken ? 'Yes' : 'No'}`);

    const scriptContent = `
import os
import sys
import json
import time
import traceback

def report_progress(progress_type, message, progress=0, **kwargs):
    data = {
        'type': progress_type,
        'message': message,
        'progress': progress,
        'model': '${model.name}',
        'timestamp': time.time(),
        **kwargs
    }
    print(json.dumps(data), flush=True)

try:
    # Import required libraries
    from diffusers import FluxPipeline
    import torch
    
    repo_id = "${model.repo_id}"
    local_dir = "${localPath.replace(/\\/g, '/')}"

    # Get token from command line argument if provided
    token = sys.argv[1] if len(sys.argv) > 1 else None

    # Only use token if it's not empty
    if not token or token.strip() == "":
        token = None
        report_progress('warning', 'No Hugging Face token provided - using anonymous access', 3)
    else:
        report_progress('info', f'Using Hugging Face token (length: {len(token)})', 3)

    report_progress('init', f'Initializing FLUX installation for {repo_id}', 5)
    
    # Create directory if it doesn't exist
    os.makedirs(local_dir, exist_ok=True)
    
    report_progress('checking', 'Checking FLUX repository access...', 10)
    
    # Download complete FLUX model using diffusers
    report_progress('downloading', 'Downloading complete FLUX model...', 20)
    
    # Use CPU-friendly settings for download
    pipeline = FluxPipeline.from_pretrained(
        repo_id,
        torch_dtype=torch.float32,  # Use float32 for compatibility during download
        low_cpu_mem_usage=True,
        token=token
    )
    
    report_progress('saving', 'Saving FLUX model to local directory...', 70)
    
    # Save the complete pipeline to local directory
    pipeline.save_pretrained(local_dir)
    
    report_progress('verifying', 'Verifying FLUX installation...', 90)
    
    # Verify the installation
    saved_files = []
    total_size = 0
    for root, dirs, files in os.walk(local_dir):
        for file in files:
            if not file.startswith('.'):
                file_path = os.path.join(root, file)
                saved_files.append(os.path.relpath(file_path, local_dir))
                try:
                    total_size += os.path.getsize(file_path)
                except:
                    pass
    
    if len(saved_files) == 0:
        report_progress('error', 'No FLUX files were saved', 90)
        sys.exit(1)
    
    # Check for essential FLUX components
    has_model_index = any('model_index.json' in f for f in saved_files)
    has_transformer = any('transformer' in f for f in saved_files)
    has_vae = any('vae' in f for f in saved_files)
    
    if not (has_model_index and has_transformer and has_vae):
        report_progress('error', 'FLUX model appears incomplete - missing essential components', 95)
        sys.exit(1)
    
    size_mb = round(total_size / (1024 * 1024), 1)
    report_progress('complete', f'Successfully installed complete FLUX model', 100, 
        files_saved=len(saved_files), total_size_mb=size_mb)
    
except ImportError as import_error:
    report_progress('error', f'Required libraries not available: {import_error}', 0)
    print(f"Please ensure diffusers and torch are installed: {import_error}", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    report_progress('error', f'FLUX installation failed: {e}', 0)
    traceback.print_exc()
    sys.exit(1)
`;

    // Write the FLUX installation script
    const scriptPath = path.join(HELPERS_DIR, `install_flux_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}.py`);
    await fs.writeFile(scriptPath, scriptContent);
    logger.info(`Created FLUX installation script at: ${scriptPath}`);

    // Execute the FLUX installation script
    await new Promise((resolve, reject) => {
      const downloadTimeout = setTimeout(() => {
        logger.error(`FLUX model installation for ${model.name} timed out after 90 minutes`);
        reject(new Error(`FLUX model installation timed out after 90 minutes`));
      }, 90 * 60 * 1000); // Extended timeout for FLUX
      
      const cleanup = () => {
        clearTimeout(downloadTimeout);
      };
      
      const env = { ...process.env };
      const scriptsDir = path.dirname(pythonExe);
      delete env.PYTHONHOME;
      delete env.PYTHONPATH;
      env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
      env.HF_HUB_ENABLE_HF_TRANSFER = '1';
      
      const args = [scriptPath];
      if (hfToken) {
        args.push(hfToken);
      }
      const installer = spawn(pythonExe, args, { env });
      
      let stdout = '';
      let stderr = '';
      
      installer.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        
        // Parse JSON progress updates
        const lines = text.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
          try {
            const progressData = JSON.parse(line.trim());
            
            if (progressData.type && progressData.model === model.name) {
              logger.info(`FLUX installation progress (${model.name}): ${progressData.type} - ${progressData.message} (${progressData.progress}%)`);
              
              let status = 'Running';
              let progress = progressData.progress || 0;
              let message = progressData.message || `Installing ${model.name}...`;
              
              if (progressData.type === 'complete') {
                status = 'Running';
                progress = 100;
                message = `${model.name} installed successfully`;
              } else if (progressData.type === 'error') {
                status = 'Error';
                progress = 100;
                message = progressData.message || `Failed to install ${model.name}`;
              }
              
              this._sendProgress(pipelineName, 'models', progressName, {
                status,
                message,
                progress: Math.round(progress)
              });
            }
          } catch (parseError) {
            // Not JSON, treat as regular log output
            if (line.trim()) {
              logger.info(`FLUX installation stdout (${model.name}): ${line.trim()}`);
            }
          }
        }
      });

      installer.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        logger.info(`FLUX installation stderr (${model.name}): ${text.trim()}`);
      });

      installer.on('error', (err) => {
        logger.error(`Failed to spawn FLUX installation process for ${model.name}:`, err);
        cleanup();
        reject(new Error(`Failed to install FLUX model ${model.name}: ${err.message}`));
      });

      installer.on('close', async (code) => {
        cleanup();
        logger.info(`FLUX installation script for ${model.name} completed with code ${code}`);
        
        if (code !== 0) {
          logger.error(`FLUX installation failed for ${model.name}. Stdout: ${stdout}`);
          logger.error(`FLUX installation failed for ${model.name}. Stderr: ${stderr}`);

          // Clean up on failure
          try {
            const isEmpty = (await fs.readdir(localPath)).length === 0;
            if (isEmpty) {
              await fs.rmdir(localPath);
              logger.info(`Cleaned up empty FLUX directory: ${localPath}`);
            }
          } catch (cleanupError) {
            logger.warn(`Could not clean up FLUX directory: ${cleanupError.message}`);
          }

          // Provide more specific error messages based on the stderr content and exit code
          let errorMessage = `FLUX installation failed with code ${code}.`;
          if (code === 3221225477 || code === -1073741819) {
            // Windows access violation error
            errorMessage = `FLUX installation crashed (access violation). This may be due to insufficient memory, GPU driver issues, or corrupted installation. Try restarting the application and ensure you have enough free RAM and VRAM.`;
          } else if (stderr.includes('401') && stderr.includes('Unauthorized')) {
            errorMessage = `FLUX installation failed: Invalid or missing Hugging Face token. Please check your token in Settings.`;
          } else if (stderr.includes('ModuleNotFoundError')) {
            errorMessage = `FLUX installation failed: Missing Python dependencies. Please install Python dependencies first.`;
          } else if (stderr.includes('CUDA') || stderr.includes('GPU')) {
            errorMessage = `FLUX installation failed: GPU/CUDA related issue. Check your GPU drivers and CUDA installation.`;
          } else if (stderr.includes('OutOfMemoryError') || stderr.includes('out of memory')) {
            errorMessage = `FLUX installation failed: Out of memory. FLUX requires significant RAM/VRAM. Try closing other applications or use a machine with more memory.`;
          }

          reject(new Error(errorMessage));
        } else {
          // Verify the installation
          try {
            const files = await fs.readdir(localPath);
            if (files.length === 0) {
              logger.error(`FLUX directory is empty after installation: ${localPath}`);
              reject(new Error(`FLUX installation appeared to succeed but no files were saved`));
            } else {
              logger.info(`Successfully installed FLUX model ${model.name}, ${files.length} files/folders created`);
              
              // Send success progress
              this._sendProgress(pipelineName, 'models', progressName, {
                status: 'Complete',
                message: `${model.name} installed successfully`,
                progress: 100
              });
              
              resolve();
            }
          } catch (verifyError) {
            logger.error(`Could not verify FLUX installation: ${verifyError.message}`);
            reject(new Error(`Could not verify FLUX installation: ${verifyError.message}`));
          }
        }
      });
    });

    // Clean up the installation script
    try {
      await fs.unlink(scriptPath);
      logger.info(`Cleaned up FLUX installation script: ${scriptPath}`);
    } catch (cleanupError) {
      logger.warn(`Could not clean up FLUX installation script: ${cleanupError.message}`);
    }
  }

  async _installUpscalerModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    logger.info(`Installing upscaler model ${model.name} to: ${localPath}`);

    // Define the specific download URLs for upscaler model weights
    const upscalerDownloads = {
      'realesrgan-x4plus': {
        url: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        filename: 'RealESRGAN_x4plus.pth'
      },
      'realesrgan-x4plus-anime': {
        url: 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.2.4/RealESRGAN_x4plus_anime_6B.pth',
        filename: 'RealESRGAN_x4plus_anime_6B.pth'
      },
      'swinir-m-x4': {
        url: 'https://github.com/JingyunLiang/SwinIR/releases/download/v0.0/001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth',
        filename: '001_classicalSR_DF2K_s64w8_SwinIR-M_x4.pth'
      },
      '4xlsdir': {
        url: 'https://github.com/Phhofm/models/raw/main/4xLSDIR/4xLSDIR.pth',
        filename: '4xLSDIR.pth'
      },
      'swinir-real-sr-x4': {
        url: 'https://github.com/JingyunLiang/SwinIR/releases/download/v0.0/003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth',
        filename: '003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth'
      }
    };

    const downloadInfo = upscalerDownloads[model.name];
    if (!downloadInfo) {
      throw new Error(`Unknown upscaler model: ${model.name}`);
    }

    // Clean up any existing empty directory
    try {
      const stats = await fs.stat(localPath);
      if (stats.isDirectory()) {
        const files = await fs.readdir(localPath);
        if (files.length === 0) {
          await fs.rmdir(localPath);
          logger.info(`Cleaned up empty directory: ${localPath}`);
        }
      }
    } catch {
      // Directory doesn't exist, which is fine
    }

    // Create directory if it doesn't exist
    await fs.mkdir(localPath, { recursive: true });

    const targetPath = path.join(localPath, downloadInfo.filename);

    // Check if already downloaded
    try {
      const stats = await fs.stat(targetPath);
      if (stats.isFile() && stats.size > 0) {
        logger.info(`Upscaler model ${model.name} already exists at ${targetPath}`);

        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Complete',
          message: `${model.name} already installed`,
          progress: 100
        });
        return;
      }
    } catch {
      // File doesn't exist, proceed with download
    }

    this._sendProgress(pipelineName, 'models', progressName, {
      status: 'Running',
      message: `Downloading ${model.name} weights...`,
      progress: 10
    });

    // Use the more robust _downloadFile method
    try {
      await this._downloadFile(downloadInfo.url, targetPath, hfToken, (progressData) => {
        const progress = Math.round(progressData.progress * 0.8) + 10; // 10-90%
        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Running',
          message: `Downloading ${model.name}... ${Math.round(progressData.downloaded / 1024 / 1024)}MB${progressData.total ? ` / ${Math.round(progressData.total / 1024 / 1024)}MB` : ''}`,
          progress: progress
        });
      });

      logger.info(`Successfully downloaded upscaler model ${model.name}`);
      this._sendProgress(pipelineName, 'models', progressName, {
        status: 'Complete',
        message: `${model.name} installed successfully`,
        progress: 100
      });
    } catch (error) {
      logger.error(`Failed to download upscaler model ${model.name}:`, error);
      throw new Error(`Download failed: ${error.message}`);
    }
  }

  async _installFlux4BitModel(model, localPath, pythonExe, pipelineName, progressName, hfToken) {
    const gitRepo = 'https://github.com/HighCWu/flux-4bit.git';
    const targetDir = path.join(MODELS_DIR, 'ImageGeneration', 'flux-dev-4bit-git');
    logger.info(`[FLUX 4-bit install] Cloning ${gitRepo} to ${targetDir}`);

    // Remove old directory if it exists
    if (fsSync.existsSync(targetDir)) {
      logger.info(`[FLUX 4-bit install] Removing old directory: ${targetDir}`);
      await fs.rm(targetDir, { recursive: true, force: true });
    }

    // Clone the repo
    await new Promise((resolve, reject) => {
      const gitExecutable = this._getGitExecutable();
      const git = spawn(gitExecutable, ['clone', '--depth', '1', gitRepo, targetDir]);
      git.stdout.on('data', d => logger.info(`[FLUX 4-bit install][git] ${d}`));
      git.stderr.on('data', d => logger.info(`[FLUX 4-bit install][git] ${d}`));
      git.on('close', code => {
        if (code === 0) resolve();
        else reject(new Error(`git clone failed with code ${code}`));
      });
    });
    logger.info(`[FLUX 4-bit install] Repo cloned.`);

    // Install requirements.txt
    const reqPath = path.join(targetDir, 'requirements.txt');
    if (fsSync.existsSync(reqPath)) {
      logger.info(`[FLUX 4-bit install] Installing requirements.txt in venv for ${pipelineName}`);
      await new Promise((resolve, reject) => {
        const pip = spawn(pythonExe, ['-m', 'pip', 'install', '-r', reqPath]);
        pip.stdout.on('data', d => logger.info(`[FLUX 4-bit install][pip] ${d}`));
        pip.stderr.on('data', d => logger.info(`[FLUX 4-bit install][pip] ${d}`));
        pip.on('close', code => {
          if (code === 0) resolve();
          else reject(new Error(`pip install failed with code ${code}`));
        });
      });
      logger.info(`[FLUX 4-bit install] requirements.txt installed.`);
    } else {
      logger.warn(`[FLUX 4-bit install] No requirements.txt found in repo!`);
    }

    // Send progress
    this._sendProgress(pipelineName, 'models', progressName, {
      status: 'Complete',
      message: `FLUX 4-bit model installed from GitHub repo`,
      progress: 100
    });
  }

  async _installModelDependencies(pipelineName, modelsToInstall) {
    logger.info(`Starting model installation for ${pipelineName} - ${modelsToInstall.length} models to install`);

    // Special handling for Video Generation pipelines: use custom modules
    if (pipelineName === 'Video Generation') {
      logger.info(`Redirecting ${pipelineName} model installation to Video Generation module`);
      return await this._installVideoGenerationModule(pipelineName, 'models', 'all');
    }



    await this._ensureVenvExists(pipelineName);
    const pythonExe = this._getPythonExe(pipelineName);
    await fs.mkdir(HELPERS_DIR, { recursive: true });

    // Special handling for ImageGeneration: use enhanced model repair script
    if (pipelineName === 'ImageGeneration') {
      logger.info(`Using enhanced model repair script for ImageGeneration`);

      // Ensure Python dependencies are installed first (creates virtual environment)
      logger.info(`Ensuring Python dependencies are installed for ImageGeneration...`);
      const pipeline = this.pipelines[pipelineName];
      if (pipeline?.dependencies?.python) {
        await this._installPythonDependencies(pipelineName, pipeline.dependencies.python, true);
        logger.info(`Python dependencies installed successfully for ImageGeneration`);
      }

      // Get HuggingFace token from store
      let hfToken = null;
      logger.info(`Store available: ${this.store ? 'yes' : 'no'}`);
      
      try {
        hfToken = this.store ? this.store.get('huggingface-token') : null;
        logger.info(`Retrieved HF token from store: ${hfToken ? `[${hfToken.length} chars]` : 'null/undefined'}`);
        
        // Debug: Check if token looks like a real HF token
        if (hfToken) {
          if (hfToken === 'stable_diffusion' || hfToken === 'hf_token' || hfToken.length < 10) {
            logger.warn(`Token appears to be a placeholder or invalid: "${hfToken}"`);
            hfToken = null;
          } else {
            logger.info(`Token appears valid (${hfToken.length} chars, starts with: ${hfToken.substring(0, 4)}...)`);
          }
        } else {
          logger.warn(`No HF token found in store`);
        }
      } catch (error) {
        logger.warn(`Error retrieving HF token from store: ${error.message}`);
        hfToken = null;
      }
      const authMessage = hfToken ? ' (with HuggingFace authentication)' : ' (no authentication - some models may fail)';

      this._sendProgress(pipelineName, 'models', 'all', {
        status: 'Running',
        message: `Starting enhanced model download and repair...${authMessage}`,
        progress: 0
      });

      try {
        const modelsPath = path.join(MODELS_DIR, 'ImageGeneration');
        const modelRepairScript = path.join(process.cwd(), 'utils', 'helpers', 'model_repair.py');

        // Ensure models directory exists
        await fs.mkdir(modelsPath, { recursive: true });

        // Use the enhanced model repair script to download ALL models
        const args = [
          modelRepairScript,
          '--models-path', modelsPath,
          '--download-all'  // Download all models as requested by user
        ];

        // Add HuggingFace token if available
        if (hfToken) {
          args.push('--hf-token', hfToken);
          logger.info('Using stored HuggingFace token for model downloads');
        } else {
          logger.warn('No HuggingFace token found - some models may fail to download');
        }
        
        logger.info(`Final args array: ${JSON.stringify(args)}`);
        
        // Validate Python executable exists (should exist after Python dependencies are installed)
        if (!fsSync.existsSync(pythonExe)) {
          throw new Error(`Python executable not found: ${pythonExe}. Please ensure Python dependencies are installed first.`);
        }
        
        // Validate model repair script exists
        if (!fsSync.existsSync(modelRepairScript)) {
          throw new Error(`Model repair script not found: ${modelRepairScript}`);
        }

        logger.info(`Executing enhanced model repair script: ${pythonExe} ${args.join(' ')}`);

        await new Promise((resolve, reject) => {
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

          let downloadProcess;
          try {
            downloadProcess = spawn(pythonExe, args, {
              env,
              cwd: process.cwd()
            });
          } catch (spawnError) {
            logger.error(`Failed to spawn model repair process: ${spawnError.message}`);
            throw new Error(`Failed to start model download process: ${spawnError.message}`);
          }

          // No timeout for model downloads - they can take a very long time
          // Large models like FLUX can be 20+ GB and take hours to download

          let progressCount = 0;
          const totalModels = modelsToInstall.length;
          let stderrOutput = '';

          downloadProcess.stdout.on('data', (data) => {
            const output = data.toString();
            logger.info(`Model repair output: ${output.trim()}`);

            // Parse progress from output with better tracking for large downloads
            if (output.includes('--- Processing')) {
              const modelMatch = output.match(/--- Processing (.+) ---/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `Processing ${modelName}...`,
                progress: Math.min((progressCount / totalModels) * 70, 70)
              });
            } else if (output.includes('Downloading complete model')) {
              const modelMatch = output.match(/Downloading complete model: (.+) from/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `Downloading ${modelName}... (This may take a long time for large models)`,
                progress: Math.min((progressCount / totalModels) * 70, 70)
              });
            } else if (output.includes('Successfully downloaded')) {
              progressCount++;
              const modelMatch = output.match(/Successfully downloaded (.+)/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `✓ Downloaded ${modelName}`,
                progress: Math.min((progressCount / totalModels) * 80, 80)
              });
            } else if (output.includes('already exists and is complete')) {
              progressCount++;
              const modelMatch = output.match(/(.+) already exists and is complete/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `✓ ${modelName} already installed`,
                progress: Math.min((progressCount / totalModels) * 80, 80)
              });
            } else if (output.includes('CREATING MISSING CONFIG FILES')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: 'Creating missing configuration files...',
                progress: 90
              });
            } else if (output.includes('Download Summary') || output.includes('Repair Summary')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: 'Finalizing model installation...',
                progress: 95
              });
            } else if (output.includes('Creating config.json')) {
              const modelMatch = output.match(/Creating config\.json for (.+)/);
              const modelName = modelMatch ? modelMatch[1] : 'model';
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: `Creating config for ${modelName}...`,
                progress: 92
              });
            }
          });

          downloadProcess.stderr.on('data', (data) => {
            const error = data.toString();
            stderrOutput += error;
            logger.warn(`Model repair stderr: ${error.trim()}`);

            // Check for authentication errors and provide helpful messages
            if (error.includes('401') || error.includes('authentication') || error.includes('token')) {
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Running',
                message: 'Authentication error detected - some models may require HuggingFace token',
                progress: Math.min((progressCount / totalModels) * 70, 70)
              });
            }
          });



          downloadProcess.on('error', (error) => {
            logger.error(`Enhanced model repair process error:`, error);
            this._sendProgress(pipelineName, 'models', 'all', {
              status: 'Error',
              message: `Process error: ${error.message}`,
              progress: 100
            });
            reject(error);
          });

          // No timeout for model downloads - they can take hours for large models
          // Large models like FLUX can be 20+ GB and take hours to download

          downloadProcess.on('close', (code) => {

            if (code === 0) {
              logger.info(`Enhanced model repair completed successfully for ${pipelineName}`);
              this._sendProgress(pipelineName, 'models', 'all', {
                status: 'Complete',
                message: 'All models downloaded and configured successfully!',
                progress: 100
              });

              // Force refresh dependency status to update UI
              setTimeout(async () => {
                try {
                  await this.checkDependencies(pipelineName, false);
                  logger.info(`Dependency status refreshed for ${pipelineName} after model installation`);

                  // Notify UI to refresh dependency status
                  if (this.mainWindow) {
                    logger.info(`Sending dependency-status-changed event for ${pipelineName}`);
                    this.mainWindow.webContents.send('dependency-status-changed', {
                      pipeline: pipelineName,
                      type: 'models',
                      status: 'installed'
                    });
                  }
                } catch (error) {
                  logger.warn(`Failed to refresh dependency status for ${pipelineName}:`, error);
                }
              }, 1000);

              resolve();
            } else {
              // Check if this is an authentication error
              const isAuthError = stderrOutput.includes('401') || stderrOutput.includes('authentication') || stderrOutput.includes('token');
              
              if (isAuthError) {
                const errorMsg = `Model download failed due to authentication issues. Some models require a valid HuggingFace token with proper permissions. Please check your token in Settings and ensure it has access to the required models.`;
                const helpMsg = `To fix this: 1) Go to Settings (gear icon), 2) Enter your HuggingFace token from https://huggingface.co/settings/tokens, 3) Make sure your token has "Read" permissions and access to gated models.`;
                logger.error(errorMsg);
                logger.info(helpMsg);
                this._sendProgress(pipelineName, 'models', 'all', {
                  status: 'Error',
                  message: `${errorMsg}\n\n${helpMsg}`,
                  progress: 100
                });
                reject(new Error(errorMsg));
              } else {
                // Check if any models were successfully downloaded
                const hasSuccessfulDownloads = stderrOutput.includes('Successfully downloaded') || stderrOutput.includes('already exists and is complete');
                
                if (hasSuccessfulDownloads) {
                  const successMsg = `Model installation completed with partial success. Some models were installed successfully, but others failed. Check the logs for details.`;
                  logger.warn(successMsg);
                  this._sendProgress(pipelineName, 'models', 'all', {
                    status: 'Complete',
                    message: successMsg,
                    progress: 100
                  });
                  
                  // Force refresh dependency status to update UI
                  setTimeout(async () => {
                    try {
                      await this.checkDependencies(pipelineName, false);
                      logger.info(`Dependency status refreshed for ${pipelineName} after partial model installation`);

                      // Notify UI to refresh dependency status
                      if (this.mainWindow) {
                        logger.info(`Sending dependency-status-changed event for ${pipelineName}`);
                        this.mainWindow.webContents.send('dependency-status-changed', {
                          pipeline: pipelineName,
                          type: 'models',
                          status: 'installed'
                        });
                      }
                    } catch (error) {
                      logger.warn(`Failed to refresh dependency status for ${pipelineName}:`, error);
                    }
                  }, 1000);
                  
                  resolve();
                } else {
                  const errorMsg = `Enhanced model repair failed with exit code ${code}`;
                  logger.error(errorMsg);
                  this._sendProgress(pipelineName, 'models', 'all', {
                    status: 'Error',
                    message: errorMsg,
                    progress: 100
                  });
                  reject(new Error(errorMsg));
                }
              }
            }
          });

        }); // Close the Promise

        return; // Exit early for ImageGeneration
      } catch (error) {
        logger.error(`Enhanced model repair failed for ${pipelineName}:`, error);
        this._sendProgress(pipelineName, 'models', 'all', {
          status: 'Error',
          message: `Enhanced model repair failed: ${error.message}`,
          progress: 100
        });
        throw error;
      }
    }

    // Special handling for Hunyuan3D models: ensure proper directory structure
    if ((pipelineName === 'v13_hunyuan2-stableprojectorz' || pipelineName.toLowerCase().includes('hunyuan')) && pipelineName !== 'HunyuanVideo') {
      logger.info(`Setting up Hunyuan3D models directory structure for ${pipelineName}`);
      const hunyuanModelsDir = path.join(MODELS_DIR, 'Hunyuan3D');
      await fs.mkdir(hunyuanModelsDir, { recursive: true });
      logger.info(`Created Hunyuan3D models directory: ${hunyuanModelsDir}`);
    }

    // Special handling for ImageUpscaling: install all models into models/upscaling/{model.name}/
    if (pipelineName === 'ImageUpscaling') {
      const config = this.pipelines[pipelineName];
      const modelDeps = config.dependencies?.models || [];
      const pythonDeps = config.dependencies?.python || [];
      const path = require('path');
      const fs = require('fs');
      const venvSitePackages = path.join(__dirname, '../../pipelines/ImageUpscaling/env/Lib/site-packages');
      const modelsDir = path.join(__dirname, '../../models/upscaling');
      let pythonSatisfied = true;
      let modelsSatisfied = true;
      let pythonDetails = {};
      let modelDetails = {};

      // Check Python dependencies
      for (const dep of pythonDeps) {
        // Only check package name (not version) for presence in site-packages
        const pkgName = dep.split('>=')[0].split('==')[0].split('<')[0].split('~')[0].replace(/[^a-zA-Z0-9_\-]/g, '');
        let found = false;
        try {
          const entries = fs.readdirSync(venvSitePackages);
          found = entries.some(e => e.startsWith(pkgName + '-') || e === pkgName || e.startsWith(pkgName + '_'));
        } catch (e) {
          found = false;
        }
        pythonDetails[dep] = { satisfied: found, installed: found ? 'Installed' : 'Not installed', required: dep };
        if (!found) pythonSatisfied = false;
      }

      // Check model dependencies (only required ones)
      for (const model of modelDeps) {
        if (model.required === false) {
          modelDetails[model.name] = { satisfied: true, installed: 'Optional', required: false };
          continue;
        }
        const modelPath = path.join(modelsDir, model.name);
        let modelOk = false;
        try {
          const files = fs.readdirSync(modelPath);
          if (model.files && model.files.length > 0) {
            modelOk = model.files.every(f => files.includes(f));
          } else {
            modelOk = files.length > 0;
          }
        } catch (e) {
          modelOk = false;
        }
        modelDetails[model.name] = { satisfied: modelOk, installed: modelOk ? 'Installed' : 'Not installed', required: true };
        if (!modelOk) modelsSatisfied = false;
      }

      this.dependencyStatus[pipelineName] = {
        python: { installed: pythonSatisfied, details: pythonDetails },
        models: { installed: modelsSatisfied, details: modelDetails }
      };
      const allSatisfied = pythonSatisfied && modelsSatisfied;
      logger.info(`Special ImageUpscaling dependency check: Python=${pythonSatisfied}, Models=${modelsSatisfied}, Overall=${allSatisfied}`);
      return { satisfied: allSatisfied };
    }

    // Verify huggingface_hub is installed before attempting model downloads
    logger.info(`Verifying huggingface_hub is available for ${pipelineName}...`);
    try {
      await new Promise((resolve, reject) => {
        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExe);
        delete env.PYTHONHOME;
        delete env.PYTHONPATH;
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
        
        const checkHF = spawn(pythonExe, ['-c', 'import huggingface_hub; print("huggingface_hub available")'], { env });
        checkHF.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error('huggingface_hub not available'));
          }
        });
        checkHF.on('error', () => reject(new Error('Failed to check huggingface_hub')));
      });
    } catch (error) {
      logger.error(`huggingface_hub not available for ${pipelineName}:`, error);
      throw new Error(`Cannot download models: huggingface_hub not installed. Please install Python dependencies first.`);
    }

    for (const model of modelsToInstall) {
      const progressName = model.name;
      logger.info(`Starting download of model ${model.name} for ${pipelineName}`);
      
      this._sendProgress(pipelineName, 'models', progressName, {
        status: 'Running',
        message: `Downloading ${model.name}...`,
        progress: 0
      });

      try {
        const localPath = path.isAbsolute(model.local_path)
          ? model.local_path
          : path.join(MODELS_DIR, model.local_path);

        logger.info(`Model ${model.name} will be downloaded to: ${localPath}`);

        // Create directory if it doesn't exist
        await fs.mkdir(path.dirname(localPath), { recursive: true });
        await fs.mkdir(localPath, { recursive: true });

        // Clean up any existing script first
        let scriptPath = path.join(HELPERS_DIR, `download_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}.py`);
        try {
          await fs.unlink(scriptPath);
          logger.info(`Removed old download script: ${scriptPath}`);
        } catch (error) {
          // Ignore if file doesn't exist
        }

        // Get the Hugging Face token from settings
        const hfToken = this.store.get('huggingface-token');
        logger.info(`Retrieved HF token from store: ${hfToken ? `[${hfToken.length} chars]` : 'null/undefined'}`);

        // Special handling for FLUX models - use diffusers to download complete model
        if (model.repo_id === 'black-forest-labs/FLUX.1-dev') {
          logger.info(`Using special FLUX installation for ${model.name}`);

          // Check if Hugging Face token is available
          if (!hfToken || hfToken.trim() === '') {
            const errorMsg = `FLUX model requires a valid Hugging Face token. Please set your token in Settings before installing FLUX models.`;
            logger.error(errorMsg);
            this._sendProgress(pipelineName, 'models', progressName, {
              status: 'Error',
              message: errorMsg,
              progress: 100
            });
            throw new Error(errorMsg);
          }

          try {
            await this._installFluxModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          } catch (fluxError) {
            logger.error(`FLUX installation failed: ${fluxError.message}`);

            // If FLUX installation fails, we can continue with other models
            // but mark this specific model as failed
            this._sendProgress(pipelineName, 'models', progressName, {
              status: 'Error',
              message: `FLUX installation failed: ${fluxError.message}. You can try installing other models first.`,
              progress: 100
            });

            // Don't throw the error - continue with other models
            logger.warn(`Continuing with other model installations despite FLUX failure`);
          }
          continue; // Skip normal download process for FLUX
        }
        
        // Special handling for FLUX 4-bit model (official HighCWu repo)
        if (model.repo_id === 'HighCWu/FLUX.1-dev-4bit') {
          logger.info(`Using special FLUX 4-bit installation for ${model.name}`);
          await this._installFlux4BitModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          continue; // Skip normal download process for FLUX 4-bit
        }

        // Special handling for upscaler models - these need specific weight files
        const upscalerModels = ['realesrgan-x4plus', 'realesrgan-x4plus-anime', 'swinir-m-x4', '4xlsdir', 'swinir-real-sr-x4'];
        if (upscalerModels.includes(model.name)) {
          logger.info(`Using special upscaler installation for ${model.name}`);
          await this._installUpscalerModel(model, localPath, pythonExe, pipelineName, progressName, hfToken);
          continue; // Skip normal download process for upscaler models
        }
        
        // Use the enhanced download script with proper progress tracking
        const enhancedScriptPath = path.join(__dirname, 'python_helpers', 'download_hf_model_with_progress.py');
        
        // Check if enhanced script exists, if not use fallback
        let useEnhancedScript = true;
        try {
          await fs.access(enhancedScriptPath);
          logger.info(`Using enhanced download script: ${enhancedScriptPath}`);
        } catch {
          useEnhancedScript = false;
          logger.warn(`Enhanced script not found, falling back to basic script`);
        }
        
        let args; // <-- Always define args in this scope
        if (useEnhancedScript) {
          // Use enhanced script with JSON progress reporting
          scriptPath = enhancedScriptPath;
          // Debug: Log the model object to see what properties it has
          logger.info(`Model object for ${model.name}:`, JSON.stringify(model, null, 2));
          
          // Fix argument order: repo_id, local_dir, model_name, token
          args = [scriptPath, model.repo_id, localPath, model.name];
          if (hfToken && hfToken.trim() !== '') {
            args.push(hfToken);
            logger.info(`Using HF token for ${model.name} (length: ${hfToken.length})`);
          } else {
            args.push(''); // Empty token
            logger.info(`No HF token provided for ${model.name} - using anonymous access`);
          }
          // Add include_patterns after token to maintain correct argument order
          if (model.files && Array.isArray(model.files) && model.files.length > 0) {
            args.push('--include_patterns');
            args.push(model.files.join(','));
          }
          // Final fix: ensure token is always the 4th argument for sdxl-turbo
          if (model.name === 'sdxl-turbo') {
            // Reconstruct args to ensure correct order: script, repo_id, local_path, model_name, token
            args = [scriptPath, model.repo_id, localPath, model.name, hfToken || ''];
          }
          logger.info(`Executing enhanced model download script for ${model.name}: ${pythonExe} ${args.join(' ')}`);
          logger.info(`Full args array for ${model.name}: [${args.map(arg => `"${arg}"`).join(', ')}]`);
        } else {
          // Fallback: Create a basic script
          // Escape the token properly for Python
          const escapedToken = hfToken ? hfToken.replace(/\\/g, '\\\\').replace(/"/g, '\\"') : '';
          logger.info(`Using fallback script for ${model.name}, token: ${hfToken ? `[${hfToken.length} chars]` : 'none'}`);

          const scriptContent = `
import os
import sys
import traceback
import json
import time

try:
    from huggingface_hub import snapshot_download, HfApi
    
    def report_progress(progress_type, message, progress=0, **kwargs):
        data = {
            'type': progress_type,
            'message': message,
            'progress': progress,
            'model': '${model.name}',
            'timestamp': time.time(),
            **kwargs
        }
        print(json.dumps(data), flush=True)
    
    def download_model():
        repo_id = "${model.repo_id}"
        local_dir = "${localPath.replace(/\\/g, '/')}"
        token = "${escapedToken}"

        # Only use token if it's not empty
        if not token or token.strip() == "":
            token = None
            report_progress('warning', 'No Hugging Face token provided - using anonymous access', 3)
        else:
            report_progress('info', f'Using Hugging Face token (length: {len(token)})', 3)
        
        report_progress('init', f'Initializing download for {repo_id}', 0)
        
        # Create directory if it doesn't exist
        os.makedirs(local_dir, exist_ok=True)
        
        # Check repository access
        try:
            report_progress('checking', 'Checking repository access...', 5)
            api = HfApi(token=token)
            repo_info = api.repo_info(repo_id=repo_id, token=token)
            report_progress('info', f'Repository found: {repo_id}', 10)
        except Exception as e:
            error_msg = str(e).lower()
            if ("gated" in error_msg or "access" in error_msg) and ${model.requires_auth ? 'True' : 'False'}:
                report_progress('error', "${model.auth_error_message || 'Repository requires authentication. Please check your Hugging Face token.'}", 0)
                sys.exit(1)
            else:
                report_progress('warning', f'Could not check repository info: {e}', 10)
        
        # Download model
        try:
            report_progress('downloading', 'Starting model download...', 25)
            snapshot_download(
                repo_id=repo_id,
                local_dir=local_dir${model.include_pattern ? `,
                include_patterns=["${model.include_pattern}"]` : ''}${model.exclude_pattern ? `,
                ignore_patterns=["${model.exclude_pattern}"]` : ''}${hfToken ? `,
                token=token` : ''},
                resume_download=True  # Enable resuming interrupted downloads
            )
            
            report_progress('verifying', 'Verifying download...', 90)
            
            # Verify download completed successfully
            downloaded_files = []
            total_size = 0
            for root, dirs, files in os.walk(local_dir):
                for file in files:
                    if not file.startswith('.'):
                        file_path = os.path.join(root, file)
                        downloaded_files.append(os.path.relpath(file_path, local_dir))
                        try:
                            total_size += os.path.getsize(file_path)
                        except:
                            pass
            
            if len(downloaded_files) == 0:
                report_progress('error', 'No files were downloaded', 90)
                sys.exit(1)
            
            size_mb = round(total_size / (1024 * 1024), 1)
            report_progress('complete', f'Successfully downloaded {repo_id}', 100, 
                files_downloaded=len(downloaded_files), total_size_mb=size_mb)
            
        except Exception as download_error:
            if ${model.requires_auth ? 'True' : 'False'} and ("gated" in str(download_error).lower() or "access" in str(download_error).lower()):
                report_progress('error', "${model.auth_error_message || 'Repository requires authentication. Please check your Hugging Face token.'}", 0)
            else:
                report_progress('error', f'Download failed: {download_error}', 0)
            traceback.print_exc()
            sys.exit(1)

    if __name__ == "__main__":
        download_model()
        
except Exception as e:
    report_progress('error', f'Script error: {e}', 0)
    traceback.print_exc()
    sys.exit(1)
`;

          await fs.writeFile(scriptPath, scriptContent);
          logger.info(`Created fallback download script at: ${scriptPath}`);
          args = [scriptPath]; // fallback script only needs the script path
        }

        // Run the download script with timeout
        await new Promise((resolve, reject) => {
          // Set a timeout for the download (60 minutes for large models)
          const downloadTimeout = setTimeout(() => {
            logger.error(`Model download for ${model.name} timed out after 60 minutes`);
            reject(new Error(`Model download timed out after 60 minutes`));
          }, 60 * 60 * 1000);
          
          const cleanup = () => {
            clearTimeout(downloadTimeout);
          };
          const env = { ...process.env };
          const scriptsDir = path.dirname(pythonExe);
          delete env.PYTHONHOME;
          delete env.PYTHONPATH;
          env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;
          env.HF_HUB_ENABLE_HF_TRANSFER = '1';  // Enable high-speed file transfer
          
          // Add pipeline-specific environment variables
          const pipeline = this.pipelines[pipelineName];
          if (pipeline?.environment) {
            Object.assign(env, pipeline.environment);
          }

          // Use the args that were already constructed above with proper token handling
          // The args variable is already set from the earlier construction
          
          const installer = spawn(pythonExe, args, {
            env,
            windowsHide: true
          });
          
          let stdout = '';
          let stderr = '';
          
          installer.stdout.on('data', (data) => {
            const text = data.toString();
            stdout += text;
            
            // Try to parse JSON progress updates
            const lines = text.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
              try {
                const progressData = JSON.parse(line.trim());
                
                if (progressData.type && progressData.model === model.name) {
                  logger.info(`Model download progress (${model.name}): ${progressData.type} - ${progressData.message} (${progressData.progress}%)`);
                  
                  let status = 'Running';
                  let progress = progressData.progress || 0;
                  let message = progressData.message || `Downloading ${model.name}...`;
                  
                  // Map progress types to status
                  if (progressData.type === 'complete') {
                    status = 'Running'; // Keep as Running until we verify
                    progress = 100;
                    message = `${model.name} downloaded successfully`;
                  } else if (progressData.type === 'error') {
                    status = 'Error';
                    progress = 100;
                    message = progressData.message || `Failed to download ${model.name}`;
                  } else if (progressData.type === 'file_progress') {
                    // Use overall progress if available, otherwise file progress
                    progress = progressData.overall_progress || progressData.progress || 0;
                    
                    // Show download speed and size info
                    if (progressData.downloaded_mb && progressData.total_mb) {
                      message = `Downloading ${model.name}... ${progressData.downloaded_mb}MB / ${progressData.total_mb}MB`;
                    } else {
                      message = `Downloading ${model.name}... ${Math.round(progress)}%`;
                    }
                  } else if (['init', 'checking', 'scanning', 'downloading', 'verifying'].includes(progressData.type)) {
                    message = progressData.message || `Downloading ${model.name}...`;
                  }
                  
                  this._sendProgress(pipelineName, 'models', progressName, {
                    status,
                    message,
                    progress: Math.round(progress)
                  });
                }
              } catch (parseError) {
                // Not JSON, treat as regular log output
                if (line.trim()) {
                  logger.info(`Model download stdout (${model.name}): ${line.trim()}`);
                  
                  // Fallback progress detection for non-JSON output
                  const lowerLine = line.toLowerCase();
                  if (lowerLine.includes('downloading') || lowerLine.includes('fetching')) {
                    this._sendProgress(pipelineName, 'models', progressName, {
                      status: 'Running',
                      message: `Downloading ${model.name}...`,
                      progress: 25
                    });
                  } else if (lowerLine.includes('successfully downloaded') || lowerLine.includes('download completed')) {
                    this._sendProgress(pipelineName, 'models', progressName, {
                      status: 'Running',
                      message: `Finalizing ${model.name}...`,
                      progress: 90
                    });
                  }
                }
              }
            }
          });

          installer.stderr.on('data', (data) => {
            const text = data.toString();
            stderr += text;
            logger.info(`Model download stderr (${model.name}): ${text.trim()}`);
          });

          installer.on('error', (err) => {
            logger.error(`Failed to spawn model download process for ${model.name}:`, err);
            cleanup();
            reject(new Error(`Failed to download model ${model.name}: ${err.message}`));
          });

          installer.on('close', async (code) => {
            cleanup();
            logger.info(`Model download script for ${model.name} completed with code ${code}`);
            
            if (code !== 0) {
              logger.error(`Model download failed for ${model.name}. Stdout: ${stdout}`);
              logger.error(`Model download failed for ${model.name}. Stderr: ${stderr}`);
              
              // Clean up empty directory on failure
              try {
                const isEmpty = (await fs.readdir(localPath)).length === 0;
                if (isEmpty) {
                  await fs.rmdir(localPath);
                  logger.info(`Cleaned up empty model directory: ${localPath}`);
                }
              } catch (cleanupError) {
                logger.warn(`Could not clean up model directory: ${cleanupError.message}`);
              }
              
              reject(new Error(`Model download failed with code ${code}. Check logs for details.`));
            } else {
              // Verify the download actually completed
              try {
                const files = await fs.readdir(localPath);
                if (files.length === 0) {
                  logger.error(`Model directory is empty after download: ${localPath}`);
                  reject(new Error(`Model download appeared to succeed but no files were downloaded`));
                } else {
                  logger.info(`Successfully downloaded model ${model.name}, ${files.length} files/folders created`);
                  // Always send a final Complete event for all models (fix for Trellis-Large progress bar)
                  this._sendProgress(pipelineName, 'models', progressName, {
                    status: 'Complete',
                    message: `${model.name} downloaded successfully`,
                    progress: 100
                  });
                  resolve();
                }
              } catch (verifyError) {
                logger.error(`Could not verify model download: ${verifyError.message}`);
                reject(new Error(`Could not verify model download: ${verifyError.message}`));
              }
            }
          });
        });

      } catch (error) {
        logger.error(`Failed to download model ${model.name}:`, error);
        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Error',
          message: `Failed to download ${model.name}: ${error.message}`,
          progress: 100
        });
        throw error;
      }
    }
  }

  _downloadFile(url, dest, token, onProgress) {
    return new Promise((resolve, reject) => {
      const urlObject = new URL(url);
      const options = {
        hostname: urlObject.hostname,
        port: urlObject.port,
        path: urlObject.pathname + urlObject.search,
        headers: {}
      };

      if (token && options.hostname.includes('huggingface.co')) {
        logger.info('Attaching Hugging Face token to download request.');
        options.headers['Authorization'] = `Bearer ${token}`;
      }

      const request = https.get(options, (response) => {
        if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
            // Follow redirect, passing the token along
            logger.info(`Redirected to ${response.headers.location}`);
            this._downloadFile(response.headers.location, dest, token, onProgress)
                .then(resolve)
                .catch(reject);
            return;
        }
        
        if (response.statusCode !== 200) {
          return reject(new Error(`Failed to get '${url}' (${response.statusCode})`));
        }

        const file = fsSync.createWriteStream(dest);
        const total = parseInt(response.headers['content-length'], 10);
        let downloaded = 0;

        response.on('data', (chunk) => {
          downloaded += chunk.length;
          const progress = !isNaN(total) ? (downloaded / total) * 100 : -1;
          onProgress({ progress, downloaded, total });
        });

        response.pipe(file);

        file.on('finish', () => {
          file.close(resolve);
        });
        
        file.on('error', (err) => {
             fs.unlink(dest).catch(()=>{/*ignore*/}).finally(()=>reject(err));
        });
      });

      request.on('error', (err) => {
        fs.unlink(dest).catch(()=>{/*ignore*/}).finally(()=>reject(err));
      });
    });
  }

  _sendProgress(pipelineName, component, name, data) {
    if (this.mainWindow) {
      // Add timestamp and additional metadata to progress updates
      const progressData = {
        pipeline: pipelineName,
        component,
        name,
        timestamp: new Date().toISOString(),
        ...data
      };
      
      // Log progress updates for debugging
      if (process.env.DEBUG_PROGRESS) {
        console.log(`[Progress] ${pipelineName} - ${component} - ${name}:`, progressData);
      }
      
      this.mainWindow.webContents.send('installation-progress', progressData);
    }
  }

  // --- Private Helpers ---
  _getPythonPath(pipelineName) {
    // Microsoft_TRELLIS uses the actual Trellis server's Python installation
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101', 'tools', 'python', 'python.exe');
    }

    // Each pipeline gets its own Python installation
    return path.join(this._getPipelineDir(pipelineName), 'python', 'python.exe');
  }

  _getVenvPath(pipelineName) {
    // Microsoft_TRELLIS uses the actual Trellis server's venv path
    if (pipelineName === 'trellis-stable-projectorz-101' || pipelineName.toLowerCase().includes('trellis')) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101', 'code', 'venv');
    }

    // Video Generation (Framepack) uses 'venv' instead of 'env'
    if (pipelineName === 'Video Generation' || pipelineName === 'framepack') {
      return path.join(this._getPipelineDir(pipelineName), 'venv');
    }

    // HunyuanVideo uses 'venv' instead of 'env' and goes to VideoPipelines directory
    if (pipelineName === 'HunyuanVideo') {
      return path.join(this._getPipelineDir(pipelineName), 'venv');
    }

    // ImageEdit uses 'venv' instead of 'env'
    if (pipelineName === 'ImageEdit') {
      return path.join(this._getPipelineDir(pipelineName), 'venv');
    }

    return path.join(this._getPipelineDir(pipelineName), 'env');
  }

  _getPythonExe(pipelineName) {
    const venvPath = this._getVenvPath(pipelineName);
    return path.join(venvPath, 'Scripts', 'python.exe');
  }

  async _findPythonVersion(version) {
    // Common Python installation paths on Windows
    const commonPaths = [
      `C:\\Python${version.replace('.', '')}\\python.exe`,
      `C:\\Python${version.replace('.', '')}-64\\python.exe`,
      `C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python${version.replace('.', '')}\\python.exe`,
      `C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python${version.replace('.', '')}-64\\python.exe`,
      `C:\\Program Files\\Python ${version}\\python.exe`,
      `C:\\Program Files (x86)\\Python ${version}\\python.exe`,
    ];

    // Also try py launcher
    try {
      const pyLauncherResult = await new Promise((resolve, reject) => {
        const pyProcess = spawn('py', [`-${version}`, '--version']);
        let output = '';

        pyProcess.stdout.on('data', (data) => {
          output += data.toString();
        });

        pyProcess.stderr.on('data', (data) => {
          output += data.toString();
        });

        pyProcess.on('close', (code) => {
          if (code === 0 && output.includes(`Python ${version}`)) {
            resolve(`py -${version}`);
          } else {
            reject(new Error('py launcher failed'));
          }
        });
      });

      if (pyLauncherResult) {
        return pyLauncherResult;
      }
    } catch {
      // py launcher not available or failed
    }

    // Check common paths
    for (const pythonPath of commonPaths) {
      try {
        await fs.access(pythonPath);

        // Verify version
        const versionCheck = await new Promise((resolve, reject) => {
          const versionProcess = spawn(pythonPath, ['--version']);
          let output = '';

          versionProcess.stdout.on('data', (data) => {
            output += data.toString();
          });

          versionProcess.stderr.on('data', (data) => {
            output += data.toString();
          });

          versionProcess.on('close', (code) => {
            if (code === 0) {
              resolve(output.trim());
            } else {
              reject(new Error(`Failed to check version: ${output}`));
            }
          });
        });

        const detectedVersion = versionCheck.match(/Python (\d+\.\d+)/)?.[1];
        if (detectedVersion === version) {
          return pythonPath;
        }
      } catch {
        // Path doesn't exist or version check failed, continue
      }
    }

    return null;
  }

  async _runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const proc = spawn(command, args, {
        windowsHide: true,
        ...options
      });
      let stderr = '';

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      proc.on('error', (err) => {
        reject(new Error(`Failed to run command ${command}: ${err.message}`));
      });

      proc.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Command ${command} failed with code ${code}. Stderr: ${stderr}`));
        } else {
          resolve();
        }
      });
    });
  }

  async _ensurePipelineExists(pipelineName) {
    const pipeline = this.pipelines[pipelineName];
    if (!pipeline) {
      // Check if pipeline exists in embedded configs
      const embeddedConfig = PIPELINE_CONFIGS[pipelineName];
      if (!embeddedConfig) {
        throw new Error(`Pipeline ${pipelineName} not found in embedded configurations`);
      }
      
      // Load from embedded config
      this.pipelines[pipelineName] = embeddedConfig;
      this.dependencyStatus[pipelineName] = {
        name: pipelineName,
        python: { installed: false, details: {} },
        models: { installed: false, details: {} }
      };
      
      // Ensure directory exists (skip for system package pipelines)
      const systemPackagePipelines = ['trellis-stable-projectorz-101'];
      if (!systemPackagePipelines.includes(pipelineName)) {
        await this._ensurePipelineDirectoryExists(pipelineName);
      }
      
      logger.info(`Loaded pipeline ${pipelineName} from embedded configuration`);
    }
  }

  async _initializeHelpers() {
    try {
      await fs.mkdir(HELPERS_DIR, { recursive: true });
    } catch (error) {
      logger.error('Failed to create helpers directory:', error);
    }
  }







  // New Hunyuan3D-2.1 validation method (separate)
  async _validateHunyuan21Installation() {
    const hunyuanServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'v13_hunyuan2-stableprojectorz');
    const venvPath = path.join(hunyuanServerPath, 'code', 'venv');
    const pythonExe = path.join(venvPath, 'Scripts', 'python.exe');
    const officialRepoPath = path.join(hunyuanServerPath, 'code');
    const officialApiServer = path.join(officialRepoPath, 'api_spz', 'main_api.py');
    const installationMarker = path.join(hunyuanServerPath, 'code', 'hunyuan_init_done.txt');
    const startServerBat = path.join(hunyuanServerPath, 'run.bat');

    logger.info(`hunyuan3d-2.1-spz-101 validation paths:`);
    logger.info(`  Server path: ${hunyuanServerPath}`);
    logger.info(`  Venv path: ${venvPath}`);
    logger.info(`  Python exe: ${pythonExe}`);
    logger.info(`  Official repo: ${officialRepoPath}`);
    logger.info(`  Official API server: ${officialApiServer}`);
    logger.info(`  Start server bat: ${startServerBat}`);

    try {
      // Check if Python executable exists in venv (primary indicator of successful installation)
      logger.info(`Checking if Python executable exists: ${pythonExe}`);
      await fs.access(pythonExe);
      logger.info(`hunyuan3d-2.1-spz-101 Python executable found`);

      // Check if official repository exists
      try {
        await fs.access(officialRepoPath);
        logger.info(`hunyuan3d-2.1-spz-101 official repository found`);
      } catch {
        logger.info(`hunyuan3d-2.1-spz-101 official repository not found`);
        return false;
      }

      // Check if official API server exists
      try {
        await fs.access(officialApiServer);
        logger.info(`hunyuan3d-2.1-spz-101 official API server found`);
      } catch {
        logger.info(`hunyuan3d-2.1-spz-101 official API server not found`);
        return false;
      }

      // Check if installation is complete
      try {
        await fs.access(installationMarker);
        logger.info(`hunyuan3d-2.1-spz-101 installation marker found`);
      } catch {
        logger.info(`hunyuan3d-2.1-spz-101 installation not complete`);
        return false;
      }

      // Check if start server batch file exists
      try {
        await fs.access(startServerBat);
        logger.info(`hunyuan3d-2.1-spz-101 start server batch file found`);
      } catch {
        logger.info(`hunyuan3d-2.1-spz-101 start server batch file not found`);
        return false;
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(venvPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'transformers', 'diffusers'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_'))
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Consider installation valid if ENV folder exists and at least some key packages are found
      const isValid = packagesFound >= 2; // At least 2 out of 3 key packages
      if (isValid) {
        logger.info(`hunyuan3d-2.1-spz-101 System Package validation successful (${packagesFound}/${requiredPackages.length} key packages found)`);
      } else {
        logger.warn(`hunyuan3d-2.1-spz-101 System Package validation failed (only ${packagesFound}/${requiredPackages.length} key packages found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info(`hunyuan3d-2.1-spz-101 Python executable not found - installation not complete`);
        return false; // Installation needed
      }

      logger.error(`hunyuan3d-2.1-spz-101 System Package validation failed:`, error);
      return false;
    }
  }

  async _validateTrellisInstallation() {
    const trellisServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101');
    const venvPath = path.join(trellisServerPath, 'code', 'venv');
    const pythonExe = path.join(venvPath, 'Scripts', 'python.exe');
    const envFolderPath = path.join(trellisServerPath, 'ENV');

    try {
      // Check if Python executable exists in venv (primary indicator of successful installation)
      await fs.access(pythonExe);
      logger.info('Microsoft_TRELLIS Python executable found');

      // Check if ENV folder exists (created by run-fp16.bat after successful installation)
      try {
        await fs.access(envFolderPath);
        logger.info('Microsoft_TRELLIS ENV folder found');
      } catch {
        // ENV folder is optional - venv existence is the main indicator
        logger.info('Microsoft_TRELLIS ENV folder not found, but venv exists');
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(venvPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'transformers', 'huggingface_hub'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_'))
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Consider installation valid if at least some key packages are found
      const isValid = packagesFound >= 2; // At least 2 out of 3 key packages
      if (isValid) {
        logger.info(`Microsoft_TRELLIS System Package validation successful (${packagesFound}/${requiredPackages.length} key packages found)`);
      } else {
        logger.warn(`Microsoft_TRELLIS System Package validation failed (only ${packagesFound}/${requiredPackages.length} key packages found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info('Microsoft_TRELLIS Python executable not found - installation not complete');
        return false; // Installation needed
      }

      logger.error('Microsoft_TRELLIS System Package validation failed:', error);
      return false;
    }
  }

  async _validateImageGenerationInstallation() {
    const imageGenPath = path.join(PIPELINES_DIR, 'ImageGeneration');
    const envPath = path.join(imageGenPath, 'env');
    const pythonExe = path.join(envPath, 'Scripts', 'python.exe');
    const initDoneFile = path.join(envPath, 'image_gen_init_done.txt');

    try {
      // Check if Python executable exists in env (primary indicator of successful installation)
      await fs.access(pythonExe);
      logger.info('ImageGeneration Python executable found');

      // Check if init done file exists (created after successful installation)
      try {
        await fs.access(initDoneFile);
        logger.info('ImageGeneration init done file found');
      } catch {
        logger.info('ImageGeneration init done file not found, but env exists');
        return false; // Installation not complete without init file
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(envPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'diffusers', 'transformers'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_'))
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Check for key models (SDXL Base and Refiner)
      const modelsPath = path.join(process.cwd(), 'models', 'ImageGeneration');
      const sdxlBasePath = path.join(modelsPath, 'stable-diffusion-xl-base-1.0');
      const sdxlRefinerPath = path.join(modelsPath, 'stable-diffusion-xl-refiner-1.0');

      let modelsFound = 0;
      try {
        await fs.access(path.join(sdxlBasePath, 'model_index.json'));
        modelsFound++;
        logger.info('SDXL Base model found');
      } catch (error) {
        logger.info('SDXL Base model not found');
      }

      try {
        await fs.access(path.join(sdxlRefinerPath, 'model_index.json'));
        modelsFound++;
        logger.info('SDXL Refiner model found');
      } catch (error) {
        logger.info('SDXL Refiner model not found');
      }

      // Consider installation valid if all key packages are found and at least one model exists
      const isValid = packagesFound >= 3 && modelsFound >= 1; // All 3 key packages + at least 1 model
      if (isValid) {
        logger.info(`ImageGeneration Module validation successful (${packagesFound}/${requiredPackages.length} packages, ${modelsFound}/2 models found)`);
      } else {
        logger.warn(`ImageGeneration Module validation failed (${packagesFound}/${requiredPackages.length} packages, ${modelsFound}/2 models found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info('ImageGeneration Python executable not found - installation not complete');
        return false; // Installation needed
      }

      logger.error('ImageGeneration Module validation failed:', error);
      return false;
    }
  }

  async _validateImageUpscalingInstallation() {
    const imageUpscalingPath = path.join(PIPELINES_DIR, 'ImageUpscaling');
    const envPath = path.join(imageUpscalingPath, 'env');
    const pythonExe = path.join(envPath, 'Scripts', 'python.exe');
    const initDoneFile = path.join(envPath, 'upscaling_init_done.txt');

    try {
      // Check if Python executable exists in env (primary indicator of successful installation)
      await fs.access(pythonExe);
      logger.info('ImageUpscaling Python executable found');

      // Check if init done file exists (created after successful installation)
      try {
        await fs.access(initDoneFile);
        logger.info('ImageUpscaling init done file found');
      } catch {
        logger.info('ImageUpscaling init done file not found, but env exists');
        return false; // Installation not complete without init file
      }

      // Additional validation: check if key packages are installed
      const sitePackagesPath = path.join(envPath, 'Lib', 'site-packages');
      const requiredPackages = ['torch', 'PIL', 'cv2'];

      let packagesFound = 0;
      for (const pkg of requiredPackages) {
        const packagePaths = [
          path.join(sitePackagesPath, pkg),
          path.join(sitePackagesPath, pkg.replace('-', '_')),
          path.join(sitePackagesPath, 'opencv_python') // Special case for cv2
        ];

        for (const pkgPath of packagePaths) {
          try {
            await fs.access(pkgPath);
            packagesFound++;
            break;
          } catch {}
        }
      }

      // Consider installation valid if at least 2 key packages are found
      const isValid = packagesFound >= 2; // At least 2 out of 3 key packages
      if (isValid) {
        logger.info(`ImageUpscaling Module validation successful (${packagesFound}/${requiredPackages.length} key packages found)`);
      } else {
        logger.warn(`ImageUpscaling Module validation failed (only ${packagesFound}/${requiredPackages.length} key packages found)`);
      }

      return isValid;
    } catch (error) {
      // If Python executable doesn't exist, installation is not complete
      if (error.code === 'ENOENT' && error.path && error.path.includes('python.exe')) {
        logger.info('ImageUpscaling Python executable not found - installation not complete');
        return false; // Installation needed
      }

      logger.error('ImageUpscaling Module validation failed:', error);
      return false;
    }
  }



  // --- New Microsoft_TRELLIS Module Installation ---
  async _installTrellisModule(pipelineName, component, name) {
    try {
      logger.info(`_installTrellisModule called with: ${pipelineName}, ${component}, ${name}`);
      const progressName = name || 'all';
      const trellisServerPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'trellis-stable-projectorz-101');
      const runBatPath = path.join(trellisServerPath, 'run.bat');

    // Only handle python component (system package), models are handled by run.bat
    if (component === 'models') {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: 'Models are automatically managed by Trellis Server System Package',
        progress: 100,
      });
      return;
    }

    // Check if already installed
    const isAlreadyInstalled = await this._validateTrellisInstallation();
    if (isAlreadyInstalled) {
      logger.info('Trellis Server System Package already installed');

      // Send a brief "checking" status first
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Checking Trellis installation...',
        progress: 50,
      });

      // Wait a moment then send completion and update status
      setTimeout(async () => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'Trellis Server System Package already installed',
          progress: 100,
        });
        
        // Update dependency status and notify UI
        try {
          await this.checkDependencies(pipelineName, true);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('dependency-status-changed', {
              pipeline: pipelineName,
              type: component,
              status: 'installed'
            });
          }
        } catch (error) {
          logger.warn('Error updating dependency status after Trellis check:', error);
        }
      }, 1000);

      return;
    }

    // Initialize progress
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: 'Installing Trellis Server System Package...',
      progress: 0,
    });

    // Clean up any corrupted installation files before starting
    const trellisCodePath = path.join(trellisServerPath, 'code');
    const initDoneFile = path.join(trellisCodePath, 'trellis_init_done.txt');
    const venvPath = path.join(trellisCodePath, 'venv');

    try {
      // Remove init done file if it exists (forces reinstallation)
      if (await fs.access(initDoneFile).then(() => true).catch(() => false)) {
        await fs.unlink(initDoneFile);
        logger.info('Removed existing trellis_init_done.txt to force clean installation');
      }

      // Remove venv directory if it exists (forces clean venv creation)
      if (await fs.access(venvPath).then(() => true).catch(() => false)) {
        await fs.rm(venvPath, { recursive: true, force: true });
        logger.info('Removed existing venv directory to force clean installation');
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Cleaned up previous installation, starting fresh install...',
        progress: 5,
      });
    } catch (error) {
      logger.warn('Error during cleanup (continuing anyway):', error);
    }

    // Check if run.bat exists
    try {
      await fs.access(runBatPath);
      logger.info(`Trellis run.bat found at: ${runBatPath}`);
    } catch (error) {
      logger.error(`Trellis run.bat not found at: ${runBatPath}`);
      throw new Error(`Trellis installation file not found: ${runBatPath}`);
    }

    logger.info('Starting Trellis run.bat execution...');
    return new Promise((resolve, reject) => {
      const proc = spawn('cmd.exe', ['/c', runBatPath], {
        cwd: trellisServerPath,
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let progress = 0;
      let stderr = '';

      proc.stdout.on('data', (data) => {
        const text = data.toString();

        // Update progress based on output patterns for system package installation
        if (text.includes('Creating virtual environment') || text.includes('venv')) progress = Math.max(progress, 15);
        if (text.includes('Installing dependencies') || text.includes('pip install')) progress = Math.max(progress, 35);
        if (text.includes('Installing PyTorch') || text.includes('torch')) progress = Math.max(progress, 55);
        if (text.includes('Installing requirements') || text.includes('requirements.txt')) progress = Math.max(progress, 75);
        if (text.includes('Downloading models') || text.includes('huggingface')) progress = Math.max(progress, 85);
        if (text.includes('Installation completed') || text.includes('Server starting')) progress = Math.max(progress, 95);
        if (text.includes('Server is running') || text.includes('Gradio app running') || text.includes('Running on')) progress = 100;

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
        // Don't send stderr as error status during installation - it's often just warnings
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: data.toString().trim(),
          progress,
        });
      });

      proc.on('close', (code) => {
        // Check if environment folder exists to verify successful installation
        const venvPath = path.join(trellisServerPath, 'code', 'venv');
        const envExists = require('fs').existsSync(venvPath);

        if (code === 0 || progress >= 90 || envExists) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Complete',
            message: 'Trellis Server System Package installed successfully',
            progress: 100,
          });
          
          // Update dependency status and notify UI
          setTimeout(async () => {
            try {
              await this.checkDependencies(pipelineName, true);
              if (this.mainWindow) {
                this.mainWindow.webContents.send('dependency-status-changed', {
                  pipeline: pipelineName,
                  type: component,
                  status: 'installed'
                });
              }
            } catch (error) {
              logger.warn('Error updating dependency status after Trellis installation:', error);
            }
          }, 1000);
          
          resolve();
        } else {
          const errorMsg = `Trellis Server System Package installation failed with code ${code}. Check if dependencies are properly installed.`;
          logger.error(errorMsg);
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Error',
            message: errorMsg,
            progress: 0,
          });
          reject(new Error(errorMsg));
        }
      });

      proc.on('error', (error) => {
        const errorMsg = `Failed to start Trellis Module installation: ${error.message}`;
        logger.error(errorMsg);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        reject(new Error(errorMsg));
      });
    });
    } catch (error) {
      logger.error('Error in _installTrellisModule:', error);
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Error',
        message: `Installation failed: ${error.message}`,
        progress: 0,
      });
      throw error;
    }
  }

  // --- New Hunyuan3D-2 Module Installation ---
  async _installHunyuanModule(pipelineName, component, name) {
    const progressName = name || 'all';
    const serverFolderName = pipelineName === 'v13_hunyuan2-stableprojectorz' ? 'v13_hunyuan2-stableprojectorz' : 'hunyuan2-spz-101';
    const hunyuanServerPath = pipelineName === 'v13_hunyuan2-stableprojectorz'
      ? path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', serverFolderName)
      : path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', serverFolderName, 'run-projectorz_(faster)');
    // Only Hunyuan3D-2.1 is supported now - use single view for image-to-3D
    const runBatPath = path.join(hunyuanServerPath, 'run-browser_(slower)', 'run-gradio-full-singleview.bat');

    // Only handle python component (system package), models are handled by run-stableprojectorz-full-multiview.bat
    if (component === 'models') {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: `Models are automatically managed by ${pipelineName} Server System Package`,
        progress: 100,
      });
      return;
    }

    // Check if already installed (only for Hunyuan3D-2.1)
    if (pipelineName !== 'v13_hunyuan2-stableprojectorz') {
      throw new Error(`Unsupported pipeline: ${pipelineName}`);
    }
    const isAlreadyInstalled = await this._validateHunyuan21Installation();
    if (isAlreadyInstalled) {
      logger.info(`${pipelineName} Server System Package already installed`);

      // Send a brief "checking" status first
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: `Checking ${pipelineName} installation...`,
        progress: 50,
      });

      // Wait a moment then send completion and update status
      setTimeout(async () => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: `${pipelineName} Server System Package already installed`,
          progress: 100,
        });
        
        // Update dependency status and notify UI
        try {
          await this.checkDependencies(pipelineName, true);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('dependency-status-changed', {
              pipeline: pipelineName,
              type: component,
              status: 'installed'
            });
          }
        } catch (error) {
          logger.warn('Error updating dependency status after Hunyuan check:', error);
        }
      }, 1000);

      return;
    }

    // Initialize progress
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: 'Installing Hunyuan3D Server System Package...',
      progress: 0,
    });

    // Clean up any corrupted installation files before starting (Hunyuan3D-2.1 only)
    const hunyuanInstallPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'v13_hunyuan2-stableprojectorz');
    const initDoneFile = path.join(hunyuanInstallPath, 'code', 'hunyuan_init_done.txt');
    const venvPath = path.join(hunyuanInstallPath, 'code', 'venv');

    try {
      // Remove init done file if it exists (forces reinstallation)
      if (await fs.access(initDoneFile).then(() => true).catch(() => false)) {
        await fs.unlink(initDoneFile);
        logger.info('Removed existing hunyuan_init_done.txt to force clean installation');
      }

      // Remove venv directory if it exists (forces clean venv creation)
      if (await fs.access(venvPath).then(() => true).catch(() => false)) {
        await fs.rm(venvPath, { recursive: true, force: true });
        logger.info('Removed existing venv directory to force clean installation');
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Cleaned up previous installation, starting fresh install...',
        progress: 5,
      });
    } catch (error) {
      logger.warn('Error during cleanup (continuing anyway):', error);
    }

    // Check if post-install script exists (only for Hunyuan3D-2, not 2.1)
    if (runBatPath) {
      try {
        await fs.access(runBatPath);
      } catch (error) {
        logger.error(`${pipelineName} batch file not found at: ${runBatPath}`);
        throw new Error(`${pipelineName} installation file not found: ${runBatPath}`);
      }
    }

    // If no post-install script, just complete the installation
    if (!runBatPath) {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: `${pipelineName} installation completed successfully`,
        progress: 100,
      });
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      let proc;

      // Handle Python scripts vs batch files
      if (runBatPath.endsWith('.py')) {
        // For Python scripts, use the virtual environment Python
        const pythonExe = pipelineName === 'v13_hunyuan2-stableprojectorz'
          ? path.join(hunyuanInstallPath, 'code', 'venv', 'Scripts', 'python.exe')
          : path.join(hunyuanInstallPath, 'code', 'venv', 'Scripts', 'python.exe');

        proc = spawn(pythonExe, [runBatPath], {
          cwd: hunyuanServerPath,
          windowsHide: true,
          stdio: ['ignore', 'pipe', 'pipe']
        });
      } else {
        // For batch files, use cmd.exe
        proc = spawn('cmd.exe', ['/c', runBatPath], {
          cwd: hunyuanServerPath,
          windowsHide: true,
          stdio: ['ignore', 'pipe', 'pipe']
        });
      }

      let progress = 0;
      let stderr = '';

      proc.stdout.on('data', (data) => {
        const text = data.toString();

        // Update progress based on output patterns for Hunyuan3D installation
        if (text.includes('Creating virtual environment') || text.includes('venv')) progress = Math.max(progress, 10);
        if (text.includes('Installing dependencies') || text.includes('pip install')) progress = Math.max(progress, 25);
        if (text.includes('Installing PyTorch') || text.includes('torch')) progress = Math.max(progress, 40);
        if (text.includes('Installing requirements') || text.includes('requirements.txt')) progress = Math.max(progress, 55);
        if (text.includes('Downloading models') || text.includes('huggingface') || text.includes('Fetching')) progress = Math.max(progress, 70);
        if (text.includes('Loading pipeline components') || text.includes('Loading model')) progress = Math.max(progress, 85);
        if (text.includes('Server starting') || text.includes('Starting the server')) progress = Math.max(progress, 90);

        // Detect successful server startup - this means installation is complete
        if (text.includes('Uvicorn running on') || text.includes('Application startup complete') ||
            text.includes('Running on local URL') || text.includes('After launched, open a browser')) {
          progress = 100;

          // Server is ready - kill the process since we only needed to verify installation
          setTimeout(() => {
            if (proc && !proc.killed) {
              logger.info('Hunyuan3D server startup detected - terminating installation process');
              proc.kill('SIGTERM');
            }
          }, 2000); // Give it 2 seconds to fully start before killing
        }

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;

        // Check for server startup in stderr (Hunyuan3D outputs server info to stderr)
        if (text.includes('Uvicorn running on') || text.includes('Application startup complete') ||
            text.includes('Running on local URL') || text.includes('After launched, open a browser')) {
          progress = 100;

          // Server is ready - kill the process since we only needed to verify installation
          setTimeout(() => {
            if (proc && !proc.killed) {
              logger.info('Hunyuan3D server startup detected in stderr - terminating installation process');
              proc.kill('SIGTERM');
            }
          }, 2000); // Give it 2 seconds to fully start before killing
        }

        // Update progress for stderr patterns too
        if (text.includes('Loading pipeline components') || text.includes('Loading model')) progress = Math.max(progress, 85);
        if (text.includes('Downloading models') || text.includes('Fetching')) progress = Math.max(progress, 70);

        // Don't send stderr as error status during installation - it's often just warnings
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.on('close', (code) => {
        // Check if environment folder exists to verify successful installation
        const venvPath = path.join(path.dirname(hunyuanServerPath), 'venv');
        const envExists = require('fs').existsSync(venvPath);

        if (code === 0 || progress >= 90 || envExists) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Complete',
            message: 'Hunyuan3D-2 Server System Package installed successfully',
            progress: 100,
          });
          
          // Update dependency status and notify UI
          setTimeout(async () => {
            try {
              await this.checkDependencies(pipelineName, true);
              if (this.mainWindow) {
                this.mainWindow.webContents.send('dependency-status-changed', {
                  pipeline: pipelineName,
                  type: component,
                  status: 'installed'
                });
              }
            } catch (error) {
              logger.warn('Error updating dependency status after Hunyuan installation:', error);
            }
          }, 1000);
          
          resolve();
        } else {
          const errorMsg = `Hunyuan3D-2 Server System Package installation failed with code ${code}. Check if dependencies are properly installed.`;
          logger.error(errorMsg);
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Error',
            message: errorMsg,
            progress: 0,
          });
          reject(new Error(errorMsg));
        }
      });

      proc.on('error', (error) => {
        const errorMsg = `Failed to start Hunyuan3D-2 Module installation: ${error.message}`;
        logger.error(errorMsg);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        reject(new Error(errorMsg));
      });
    });
  }


  // --- Get Git Executable ---
  _getGitExecutable() {
    try {
      // Test if system git is available
      require('child_process').execSync('git --version', { stdio: 'ignore' });
      return 'git';
    } catch (error) {
      // System git not available, use portable git from 3D pipelines
      const portableGitPath = path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', 'v13_hunyuan2-stableprojectorz', 'tools', 'git', 'cmd', 'git.exe');
      if (fsSync.existsSync(portableGitPath)) {
        logger.info(`Using portable Git from: ${portableGitPath}`);
        return portableGitPath;
      } else {
        throw new Error(`Git not found in system PATH and portable Git not available at ${portableGitPath}`);
      }
    }
  }

  // --- Get Pipeline Directory ---
  _getPipelineDir(pipelineName) {
    // 3D generation pipelines are in a subdirectory
    const threeDPipelines = ['trellis-stable-projectorz-101', 'v13_hunyuan2-stableprojectorz'];
    if (threeDPipelines.includes(pipelineName)) {
      return path.join(PIPELINES_DIR, '3DPipelines', 'gen3d', pipelineName);
    }

    // Video generation pipelines are in a subdirectory
    if (pipelineName === 'Video Generation' || pipelineName === 'framepack') {
      return path.join(PIPELINES_DIR, 'VideoPipelines', 'framepack_cu126_torch26');
    }

    // HunyuanVideo pipeline goes to VideoPipelines root directory
    if (pipelineName === 'HunyuanVideo') {
      return path.join(PIPELINES_DIR, 'VideoPipelines');
    }

    // ImageEdit pipeline goes to ImageEdit directory
    if (pipelineName === 'ImageEdit') {
      return path.join(PIPELINES_DIR, 'ImageEdit');
    }

    // Regular pipelines are in the root pipelines directory
    return path.join(PIPELINES_DIR, pipelineName);
  }

  // --- Post-Install Script Runner ---
  async _runPostInstallScript(pipelineName, postInstallConfig) {
    const pipelineDir = this._getPipelineDir(pipelineName);
    const scriptPath = path.join(pipelineDir, postInstallConfig.script);
    const pythonExe = this._getPythonExe(pipelineName);

    logger.info(`Running post-install script: ${scriptPath}`);
    logger.info(`Using Python: ${pythonExe}`);

    try {
      // Check if script exists
      await fs.access(scriptPath);
    } catch (error) {
      throw new Error(`Post-install script not found: ${scriptPath}`);
    }

    return new Promise((resolve, reject) => {
      let childProcess;

      // Handle different script types
      if (postInstallConfig.type === 'batch' || scriptPath.endsWith('.bat')) {
        // Run batch file directly
        const env = process.env;
        childProcess = spawn('cmd', ['/c', scriptPath], {
          cwd: pipelineDir,
          env: env
        });
      } else {
        // Run Python script
        const env = process.env;
        childProcess = spawn(pythonExe, [scriptPath], {
          cwd: pipelineDir,
          env: {
            ...env,
            PYTHONPATH: pipelineDir,
            PYTHONHOME: ''
          }
        });
      }

      let stdout = '';
      let stderr = '';

      childProcess.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        // Log each line separately for better readability
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.info(`[Post-Install] ${line.trim()}`);
          }
        });
      });

      childProcess.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        // Log each line separately for better readability
        output.split('\n').forEach(line => {
          if (line.trim()) {
            logger.error(`[Post-Install Error] ${line.trim()}`);
          }
        });
      });

      childProcess.on('close', (code) => {
        if (code === 0) {
          logger.info('Post-install script completed successfully');
          resolve({ success: true, stdout, stderr });
        } else {
          reject(new Error(`Post-install script failed with code ${code}: ${stderr}`));
        }
      });

      childProcess.on('error', (error) => {
        reject(new Error(`Failed to start post-install script: ${error.message}`));
      });
    });
  }

  // --- ImageGeneration Model Download ---
  async _downloadImageGenerationModels(pipelineName, progressName) {
    const imageGenPath = path.join(PIPELINES_DIR, 'ImageGeneration');
    const pythonExe = path.join(imageGenPath, 'env', 'Scripts', 'python.exe');
    const downloadScript = path.join(imageGenPath, 'download_models.py');

    // Determine which model to download based on progressName
    let modelToDownload = 'all';
    let downloadMessage = 'Downloading SDXL models...';

    if (progressName === 'stable-diffusion-v1-5') {
      modelToDownload = 'sd1.5';
      downloadMessage = 'Downloading Stable Diffusion 1.5...';
    } else if (progressName === 'stable-diffusion-xl-base-1.0') {
      modelToDownload = 'sdxl-base';
      downloadMessage = 'Downloading SDXL Base...';
    } else if (progressName === 'stable-diffusion-xl-refiner-1.0') {
      modelToDownload = 'sdxl-refiner';
      downloadMessage = 'Downloading SDXL Refiner...';
    }

    this._sendProgress(pipelineName, 'models', progressName, {
      status: 'Installing',
      message: downloadMessage,
      progress: 10,
    });

    try {
      // Check if Python environment exists
      await fs.access(pythonExe);
    } catch (error) {
      this._sendProgress(pipelineName, 'models', progressName, {
        status: 'Error',
        message: 'ImageGeneration environment not found. Please install the module first.',
        progress: 0,
      });
      throw new Error('ImageGeneration environment not found');
    }

    return new Promise((resolve, reject) => {
      const proc = spawn(pythonExe, [downloadScript, '--model', modelToDownload], {
        cwd: imageGenPath,
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let progress = 10;

      proc.stdout.on('data', (data) => {
        const text = data.toString();
        console.log(`ImageGeneration Models: ${text.trim()}`);

        // Update progress based on output
        if (text.includes('Downloading')) {
          progress = Math.min(progress + 20, 80);
          this._sendProgress(pipelineName, 'models', progressName, {
            status: 'Installing',
            message: text.trim(),
            progress: progress,
          });
        }
      });

      proc.stderr.on('data', (data) => {
        const text = data.toString();
        console.error(`ImageGeneration Models Error: ${text.trim()}`);
      });

      proc.on('close', async (code) => {
        if (code === 0) {
          this._sendProgress(pipelineName, 'models', progressName, {
            status: 'Complete',
            message: 'SDXL models downloaded successfully',
            progress: 100,
          });

          // Refresh dependency status after successful model download
          try {
            await this.refreshPipelineTemplates();
            logger.info('Dependency status refreshed after model download');
          } catch (error) {
            logger.warn('Failed to refresh dependency status after model download:', error);
          }

          resolve();
        } else {
          this._sendProgress(pipelineName, 'models', progressName, {
            status: 'Error',
            message: 'Model download failed',
            progress: 0,
          });
          reject(new Error(`Model download failed with code ${code}`));
        }
      });

      proc.on('error', (error) => {
        this._sendProgress(pipelineName, 'models', progressName, {
          status: 'Error',
          message: `Model download error: ${error.message}`,
          progress: 0,
        });
        reject(error);
      });
    });
  }

  // --- 3D Module Zip-Based Installation ---
  async _install3DModuleFromZip(pipelineName, component, name) {
    const progressName = name || 'all';

    logger.info(`[_install3DModuleFromZip] Installing ${pipelineName} from zip package`);

    // Define zip file paths and target directories
    const moduleConfigs = {
      'trellis-stable-projectorz-101': {
        zipPath: path.join(__dirname, '../../src/module_source/3DGen/trellis-stable-projectorz-101.zip'),
        targetDir: path.join(__dirname, '../../pipelines/3DPipelines'),
        displayName: 'Trellis 3D Generation'
      },
      'v13_hunyuan2-stableprojectorz': {
        zipPath: path.join(__dirname, '../../src/module_source/3DGen/v13_hunyuan2-stableprojectorz.zip'),
        targetDir: path.join(__dirname, '../../pipelines/3DPipelines'),
        displayName: 'Hunyuan3D Generation'
      }
    };

    const config = moduleConfigs[pipelineName];
    if (!config) {
      throw new Error(`Unknown 3D module: ${pipelineName}`);
    }

    // Check if zip file exists
    if (!fsSync.existsSync(config.zipPath)) {
      throw new Error(`Zip package not found: ${config.zipPath}`);
    }

    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: `Starting ${config.displayName} installation from pre-packaged zip...`,
      progress: 0,
    });

    try {
      // Ensure target directory exists
      await fs.mkdir(config.targetDir, { recursive: true });

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: `Preparing ${config.displayName} installation directory...`,
        progress: 10,
      });

      // Skip removing existing module directory to preserve contents
      const moduleDir = path.join(config.targetDir, pipelineName);
      if (fsSync.existsSync(moduleDir)) {
        logger.info(`[_install3DModuleFromZip] Found existing directory: ${moduleDir}, skipping removal to preserve contents`);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: `Found existing ${config.displayName} installation, preserving contents...`,
          progress: 15,
        });
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: `Extracting ${config.displayName} zip package...`,
        progress: 20,
      });

      // Extract zip file with progress tracking
      await this._extractZipFileWithProgress(config.zipPath, config.targetDir, pipelineName, component, progressName, config.displayName);

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: `Verifying ${config.displayName} installation...`,
        progress: 90,
      });

      // Verify installation by checking key files
      const verificationResult = await this._verify3DModuleInstallation(pipelineName);
      if (!verificationResult.success) {
        throw new Error(`Installation verification failed: ${verificationResult.error}`);
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Complete',
        message: `${config.displayName} installed successfully`,
        progress: 100,
      });

      logger.info(`[_install3DModuleFromZip] ${pipelineName} installation completed successfully`);

      // Update dependency status
      setTimeout(async () => {
        try {
          await this.checkDependencies(pipelineName, true);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('dependency-status-changed', {
              pipeline: pipelineName,
              type: component,
              status: 'installed'
            });
          }
        } catch (error) {
          logger.warn(`Error updating dependency status for ${pipelineName}:`, error);
        }
      }, 1000);

      return Promise.resolve();

    } catch (error) {
      logger.error(`[_install3DModuleFromZip] Installation failed for ${pipelineName}:`, error);
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Error',
        message: `Installation failed: ${error.message}`,
        progress: 0,
      });
      throw error;
    }
  }

  // --- Enhanced Zip Extraction with Progress Tracking ---
  async _extractZipFileWithProgress(zipPath, targetDir, pipelineName, component, progressName, displayName) {
    return new Promise((resolve, reject) => {
      const { spawn } = require('child_process');

      logger.info(`[_extractZipFileWithProgress] Extracting ${zipPath} to ${targetDir}`);

      // Send initial extraction message
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: `Extracting ${displayName} package... (this may take a few minutes)`,
        progress: 25,
      });

      // PowerShell script: extract entries one-by-one and emit periodic percentage updates
      const psScript = `
        $ErrorActionPreference = 'Stop'
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zipPath = '${zipPath.replace(/'/g, "''")}'
        $dest = '${targetDir.replace(/'/g, "''")}'
        if (-Not (Test-Path -LiteralPath $dest)) { New-Item -ItemType Directory -Force -Path $dest | Out-Null }
        $zip = [System.IO.Compression.ZipFile]::OpenRead($zipPath)
        try {
          # Compute total bytes for accurate progress
          $totalBytes = 0
          foreach ($e in $zip.Entries) { if (-Not $e.FullName.EndsWith('/')) { $totalBytes += $e.Length } }
          if ($totalBytes -le 0) { Write-Host 'PROGRESS:100'; return }

          $processedBytes = 0
          $lastPct = -1
          foreach ($entry in $zip.Entries) {
            # Skip directory entries
            if ($entry.FullName.EndsWith('/')) { continue }
            $outPath = Join-Path $dest $entry.FullName
            $outDir = Split-Path -Parent $outPath
            if (-Not (Test-Path -LiteralPath $outDir)) { New-Item -ItemType Directory -Force -Path $outDir | Out-Null }
            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($entry, $outPath, $true)
            $processedBytes += [Math]::Max(0, [int64]$entry.Length)
            $pct = [Math]::Floor(($processedBytes * 100.0) / $totalBytes)
            if ($pct -ne $lastPct -and $pct -ge 0 -and $pct -le 100) {
              $lastPct = $pct
              # Emit only percentage to reduce log noise
              Write-Host ('PROGRESS:{0}' -f $pct)
            }
          }
          Write-Host 'PROGRESS:100'
        } finally {
          $zip.Dispose()
        }
      `;

      const proc = spawn('powershell.exe', ['-NoProfile', '-ExecutionPolicy', 'Bypass', '-Command', psScript], {
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let stderr = '';

      proc.stdout.on('data', (data) => {
        const output = data.toString();
        const lines = output.trim().split('\n');
        lines.forEach((lineRaw) => {
          const line = lineRaw.trim();
          if (!line) return;
          // Only react to progress lines to avoid massive logs
          if (line.startsWith('PROGRESS:')) {
            const pctStr = line.split(':')[1]?.trim();
            const pct = Math.max(0, Math.min(100, parseInt(pctStr, 10) || 0));
            // Map 0-100% extraction to 25-95% UI progress
            const uiProgress = Math.round(25 + (pct * 0.70));
            this._sendProgress(pipelineName, component, progressName, {
              status: 'Running',
              message: `Extracting ${displayName}... ${pct}%`,
              progress: Math.min(uiProgress, 95),
            });
          }
        });
      });

      proc.stderr.on('data', (data) => {
        // Accumulate errors but do not spam logs with every file
        const msg = data.toString();
        stderr += msg;
      });

      proc.on('close', (code) => {
        if (code === 0) {
          logger.info(`[_extractZipFileWithProgress] ${displayName} extraction completed successfully`);
          // Send completion progress (verification will take it to 100)
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Running',
            message: `${displayName} extraction completed successfully!`,
            progress: 95,
          });
          resolve();
        } else {
          logger.error(`[_extractZipFileWithProgress] ${displayName} extraction failed with code ${code}`);
          logger.error(`[_extractZipFileWithProgress] Stderr: ${stderr}`);
          reject(new Error(`${displayName} extraction failed: ${stderr || 'Unknown error'}`));
        }
      });

      proc.on('error', (error) => {
        logger.error(`[_extractZipFileWithProgress] Process error:`, error);
        reject(new Error(`Failed to start ${displayName} extraction: ${error.message}`));
      });
    });
  }

  // --- 3D Module Installation Verification ---
  async _verify3DModuleInstallation(pipelineName) {
    try {
      const moduleConfigs = {
        'trellis-stable-projectorz-101': {
          requiredPaths: [
            'pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101',
            'pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/code',
            'pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/code/venv',
            'pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run.bat'
          ]
        },
        'v13_hunyuan2-stableprojectorz': {
          requiredPaths: [
            'pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz',
            'pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz/code',
            'pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz/code/venv',
            'pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz/run.bat'
          ]
        }
      };

      const config = moduleConfigs[pipelineName];
      if (!config) {
        return { success: false, error: `Unknown module: ${pipelineName}` };
      }

      const workspaceRoot = path.resolve(__dirname, '../..');

      for (const requiredPath of config.requiredPaths) {
        const fullPath = path.join(workspaceRoot, requiredPath);
        if (!fsSync.existsSync(fullPath)) {
          return { success: false, error: `Required path missing: ${requiredPath}` };
        }
      }

      logger.info(`[_verify3DModuleInstallation] ${pipelineName} verification passed`);
      return { success: true };

    } catch (error) {
      logger.error(`[_verify3DModuleInstallation] Verification error:`, error);
      return { success: false, error: error.message };
    }
  }

  // --- FLUX Model Auto-Download Check ---
  async _checkAndDownloadFluxModel(pipelineName) {
    if (pipelineName !== 'ImageGeneration') return;

    try {
      // Get the Hugging Face token from settings
      const hfToken = this.store.get('huggingface-token');
      if (!hfToken || hfToken.trim() === '') {
        logger.info('No HuggingFace token available - skipping FLUX auto-download');
        return;
      }

      // Check if FLUX model is already installed
      const fluxPath = path.join(MODELS_DIR, 'ImageGeneration', 'fluxDev');
      if (fs.existsSync(fluxPath)) {
        const files = await fs.readdir(fluxPath);
        if (files.length > 0) {
          logger.info('FLUX model already installed - skipping auto-download');
          return;
        }
      }

      logger.info('HuggingFace token available and FLUX not installed - starting auto-download');

      // Find the FLUX model configuration
      const config = this.pipelines[pipelineName];
      const fluxModel = config.dependencies?.models?.find(m => m.name === 'flux-dev');

      if (!fluxModel) {
        logger.warn('FLUX model configuration not found');
        return;
      }

      // Get Python executable
      const pythonExe = this._getPythonExe(pipelineName);
      if (!fs.existsSync(pythonExe)) {
        logger.warn('Python executable not found - cannot download FLUX');
        return;
      }

      // Send progress notification
      this._sendProgress(pipelineName, 'models', 'flux-dev', {
        status: 'Running',
        message: 'Auto-downloading FLUX model with HuggingFace token...',
        progress: 0,
      });

      // Download FLUX model using the existing method
      const localPath = path.join(MODELS_DIR, fluxModel.local_path);
      await this._installFluxModel(fluxModel, localPath, pythonExe, pipelineName, 'flux-dev', hfToken);

      this._sendProgress(pipelineName, 'models', 'flux-dev', {
        status: 'Complete',
        message: 'FLUX model downloaded successfully',
        progress: 100,
      });

      logger.info('FLUX model auto-download completed successfully');

    } catch (error) {
      logger.error('FLUX auto-download failed:', error);
      this._sendProgress(pipelineName, 'models', 'flux-dev', {
        status: 'Error',
        message: `FLUX auto-download failed: ${error.message}`,
        progress: 0,
      });
    }
  }

  // --- New ImageGeneration Module Installation ---
  async _installImageGenerationModule(pipelineName, component = 'python', name = 'all') {
    logger.info(`[dependency_imagegeneration] Installing ImageGeneration module from bundled package`);
    
    const logStream = this._startDependencyLogging(pipelineName, 'bundled installation');
    const imageGenPipelineDir = path.join(PIPELINES_DIR, 'ImageGeneration');
    const sourceZip = path.join(WORKSPACE_ROOT, 'src', 'module_source', 'ImageGeneration', 'dependencies', 'ImageGeneration.zip');
    const extractionDir = PIPELINES_DIR;

    // Notify UI of initial status
    if (this.mainWindow) {
      this.mainWindow.webContents.send('dependency-status-changed', {
        pipeline: pipelineName,
        type: 'python',
        status: 'installing',
        message: 'Starting ImageGeneration module installation...'
      });
    }
    
    try {
      this._logDependency(logStream, `Starting portable installation of ImageGeneration module`);
      this._logDependency(logStream, `Source: ${sourceZip}`);
      this._logDependency(logStream, `Extraction Target: ${extractionDir}`);
      
      // Step 1: Delete existing directory if it exists
      this._logDependency(logStream, `Checking for existing directory: ${imageGenPipelineDir}`);
      if (fsSync.existsSync(imageGenPipelineDir)) {
        this._logDependency(logStream, `Removing existing directory: ${imageGenPipelineDir}`);
        await fs.rm(imageGenPipelineDir, { recursive: true, force: true });
        this._logDependency(logStream, 'Existing directory removed successfully');
      } else {
        this._logDependency(logStream, 'No existing directory found, proceeding with fresh installation');
      }

      // Step 2: Ensure the extraction directory exists
      await fs.mkdir(extractionDir, { recursive: true });
      
      // Step 3: Verify the source zip exists
      this._logDependency(logStream, `Verifying source zip: ${sourceZip}`);
      if (!fsSync.existsSync(sourceZip)) {
        throw new Error(`Source zip file not found at: ${sourceZip}`);
      }
      
      // Step 4: Extract the zip file using Node's built-in modules
      this._logDependency(logStream, `Starting extraction of ${sourceZip} to ${extractionDir}`);
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Installing',
        message: 'Extracting ImageGeneration module...',
        progress: 10
      });
      
      try {
        const fs = require('fs');
        const fsPromises = require('fs').promises;
        const path = require('path');
        const { spawn } = require('child_process');
        
        // Ensure the target directory exists
        const targetDir = path.join(extractionDir, 'ImageGeneration');
        await fsPromises.mkdir(targetDir, { recursive: true });
        
        // Path to the Python extraction script
        const pythonScript = path.join(__dirname, 'python_helpers', 'extract_zip.py');
        
        // Ensure the target directory is the VideoPipelines directory
        const actualTargetDir = path.join(extractionDir, 'VideoPipelines');
        await fsPromises.mkdir(actualTargetDir, { recursive: true });
        
        // Use Python for reliable zip extraction - using the exact command that worked in testing
        await new Promise((resolve, reject) => {
          const targetDir = path.join(PIPELINES_DIR, 'VideoPipelines');
          this._logDependency(logStream, `Starting Python-based extraction from ${sourceZip} to ${targetDir}`);
          
          // Use the exact command that worked in testing
          const pythonProcess = spawn('python', [
            pythonScript,
            sourceZip,
            targetDir
          ], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
          });
          let errorOutput = '';
          
          pythonProcess.stdout.on('data', (data) => {
            try {
              const output = data.toString().trim();
              if (!output) return;
              
              const progressData = JSON.parse(output);
              
              if (progressData.status === 'error') {
                throw new Error(progressData.message);
              }
              
              this._logDependency(logStream, `[Python] ${progressData.message}`);
              
              if (progressData.status === 'extracting' || progressData.status === 'completed') {
                this._sendProgress(pipelineName, 'python', 'all', {
                  status: 'Extracting',
                  message: progressData.message,
                  progress: progressData.progress
                });
              }
            } catch (e) {
              this._logDependency(logStream, `Error parsing Python output: ${e.message}`, 'error');
            }
          });
          
          pythonProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
            this._logDependency(logStream, `[Python Error] ${data.toString().trim()}`, 'error');
          });
          
          pythonProcess.on('close', (code) => {
            if (code === 0) {
              this._logDependency(logStream, 'Python extraction completed successfully');
              resolve();
            } else {
              const errorMsg = `Python extraction failed with code ${code}: ${errorOutput}`;
              this._logDependency(logStream, errorMsg, 'error');
              reject(new Error(errorMsg));
            }
          });
          
          pythonProcess.on('error', (error) => {
            const errorMsg = `Failed to start Python process: ${error.message}`;
            this._logDependency(logStream, errorMsg, 'error');
            reject(new Error(errorMsg));
          });
        });
      } catch (error) {
        this._logDependency(logStream, `Failed to extract zip file: ${error.message}`, 'error');
        this._logDependency(logStream, `Stack: ${error.stack}`, 'error');
        throw new Error(`Failed to extract ImageGeneration module: ${error.message}`);
      }
      
      // Step 5: Verify the installation
      this._logDependency(logStream, 'Verifying installation...');
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Verifying',
        message: 'Verifying installation...',
        progress: 95 // Extraction is done
      });
      
      const requiredItems = [
        { path: 'env', type: 'directory' },
        { path: 'python', type: 'directory' },
        { path: 'image_pipeline.py', type: 'file' }
      ];
      
      const missingItems = [];
      
      for (const item of requiredItems) {
        const fullPath = path.join(imageGenPipelineDir, item.path);
        try {
          const stats = await fs.stat(fullPath);
          if ((item.type === 'directory' && !stats.isDirectory()) || 
              (item.type === 'file' && !stats.isFile())) {
            missingItems.push(`${item.path} (wrong type)`);
          }
        } catch (error) {
          missingItems.push(item.path);
        }
      }
      
      if (missingItems.length > 0) {
        const errorMsg = `Installation verification failed. Missing or invalid items: ${missingItems.join(', ')}`;
        this._logDependency(logStream, errorMsg, 'error');
        throw new Error(errorMsg);
      }
      
      // Step 6: Update dependency status
      this._logDependency(logStream, 'Installation verification successful');
      this.dependencyStatus[pipelineName] = {
        ...this.dependencyStatus[pipelineName],
        python: { installed: true, details: { 'ImageGeneration Module': { satisfied: true, installed: 'Bundled', required: 'Bundled' } } },
        models: { installed: true }, // Assuming models are bundled
        bundled: { installed: true }
      };
      
      // Update UI with success status
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Complete',
        message: 'ImageGeneration module installed successfully',
        progress: 100
      });
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send('dependency-status-changed', {
          pipeline: pipelineName,
          type: 'python',
          status: 'installed',
          details: this.dependencyStatus[pipelineName].python.details,
          message: 'ImageGeneration module installed successfully'
        });
      }
      
      this._logDependency(logStream, 'ImageGeneration module installation completed successfully');
      this._endDependencyLogging(logStream, pipelineName, 'bundled installation', true);
      return { success: true, message: 'ImageGeneration module installed successfully' };
    } catch (error) {
      this._logDependency(logStream, `Error during installation: ${error.message}`, 'error');
      
      // Update UI with error status
      if (this.mainWindow) {
        this.mainWindow.webContents.send('dependency-status-changed', {
          pipeline: pipelineName,
          type: 'python',
          status: 'error',
          details: { 'ImageGeneration Module': { satisfied: false, installed: 'Failed', required: 'Bundled' } },
          message: `Installation failed: ${error.message}`
        });
      }
      
      this._endDependencyLogging(logStream, pipelineName, 'bundled installation', false);
      throw error;
    }
  }

  // --- Zip Extraction Helper ---
  async _extractZipWithProgress(zipPath, targetPath, onProgress) {
    const { spawn } = require('child_process');
    const path = require('path');
    const logger = require('./logger');
    const fs = require('fs').promises;
    const pythonScript = path.join(__dirname, 'python_helpers', 'extract_large_zip.py');

    logger.info(`Starting ZIP extraction using Python script from ${zipPath} to ${targetPath}`);

    // Ensure target directory exists
    await fs.mkdir(targetPath, { recursive: true });

    return new Promise((resolve, reject) => {
      let totalFiles = 0;
      let processedFiles = 0;
      let totalBytes = 0;
      let processedBytes = 0;
      let lastProgress = 0;
      let lastLogTime = Date.now();

      const logProgress = (force = false) => {
        const now = Date.now();
        if (force || now - lastLogTime > 5000) { // Log at most every 5 seconds
          const progress = Math.min((processedBytes / Math.max(1, totalBytes)) * 100, 100);
          logger.info(`Extraction progress: ${progress.toFixed(2)}% (${processedFiles}/${totalFiles} files, ${(processedBytes / (1024 * 1024)).toFixed(2)}MB/${(totalBytes / (1024 * 1024)).toFixed(2)}MB)`);
          lastLogTime = now;
          lastProgress = progress;
        }
      };

      const pythonProcess = spawn('python', [pythonScript, zipPath, targetPath]);
      
      pythonProcess.stdout.on('data', (data) => {
        try {
          const output = data.toString().trim();
          // Process each line of output (in case multiple JSON objects were written at once)
          output.split('\n').forEach(line => {
            if (!line.trim()) return;
            
            try {
              const message = JSON.parse(line);
              
              switch (message.type) {
                case 'progress':
                  if (message.total_files !== undefined) totalFiles = message.total_files;
                  if (message.total_bytes !== undefined) totalBytes = message.total_bytes;
                  if (message.processed_files !== undefined) processedFiles = message.processed_files;
                  if (message.processed_bytes !== undefined) processedBytes = message.processed_bytes;
                  
                  if (onProgress && message.progress !== undefined) {
                    onProgress(message.progress);
                  }
                  
                  logProgress();
                  break;
                  
                case 'complete':
                  logger.info(`Extraction completed: ${processedFiles} files extracted (${(processedBytes / (1024 * 1024)).toFixed(2)}MB)`);
                  resolve();
                  break;
              }
            } catch (e) {
              logger.warn(`Failed to parse Python output: ${e.message}`, { line });
            }
          });
        } catch (e) {
          logger.error('Error processing Python output:', e);
        }
      });

      pythonProcess.stderr.on('data', (data) => {
        try {
          const errorOutput = data.toString().trim();
          if (errorOutput) {
            // Try to parse as JSON error message
            try {
              const error = JSON.parse(errorOutput);
              logger.error(`Extraction error: ${error.message || 'Unknown error'}`);
              if (error.error) {
                logger.error(error.error);
              }
            } catch {
              // If not JSON, log as plain text
              logger.error(`Python error: ${errorOutput}`);
            }
          }
        } catch (e) {
          logger.error('Error processing Python error output:', e);
        }
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          logger.info('Python extraction process completed successfully');
          if (!processedFiles) {
            // If we didn't get a completion message but process exited successfully
            resolve();
          }
        } else {
          const error = new Error(`Python extraction process failed with code ${code}`);
          logger.error(error.message);
          reject(error);
        }
      });

      pythonProcess.on('error', (err) => {
        logger.error('Failed to start Python extraction process:', err);
        reject(err);
      });

      // Set a timeout for the entire extraction process (60 minutes)
      const timeout = setTimeout(() => {
        logger.error('Extraction timed out after 60 minutes');
        pythonProcess.kill('SIGKILL');
        reject(new Error('Extraction timed out after 60 minutes'));
      }, 60 * 60 * 1000);

      // Cleanup on process exit
      const cleanup = () => {
        clearTimeout(timeout);
        if (!pythonProcess.killed) {
          pythonProcess.kill('SIGKILL');
        }
      };

      process.on('exit', cleanup);
      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);
    });
  }

  // --- New Bundled Zip Module Installation ---
  async _installBundledZipModule(pipelineName, component, name) {
    logger.info(`[dependency_bundled] Installing ${pipelineName} module from bundled package`);
    const logStream = this._startDependencyLogging(pipelineName, `module installation`);
    const progressName = name || 'all';
    const fs = require('fs');
    const path = require('path');

    try {
      const pipeline = this.pipelines[pipelineName];
      if (!pipeline || !pipeline.dependencies || !pipeline.dependencies.bundle) {
        throw new Error(`Pipeline ${pipelineName} not found or is not a bundled package`);
      }

      const { sourcePath, targetPath } = pipeline.dependencies.bundle;
      
      if (!sourcePath || !targetPath) {
        throw new Error(`Invalid bundle configuration for ${pipelineName}`);
      }

      // Ensure source zip exists
      if (!fs.existsSync(sourcePath)) {
        throw new Error(`Bundle source not found: ${sourcePath}`);
      }

      // Create target directory if it doesn't exist
      await fs.promises.mkdir(targetPath, { recursive: true });

      // Extract the zip file
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Installing',
        message: 'Extracting bundled package...',
        progress: 10
      });

      await this._extractZipWithProgress(sourcePath, targetPath, (progress) => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Installing',
          message: 'Extracting bundled package...',
          progress: 10 + Math.floor(progress * 0.8)
        });
      });

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Completed',
        message: 'Module installation completed successfully',
        progress: 100
      });

      return {
        status: 'success',
        message: 'Module installed successfully'
      };

    } catch (error) {
      logger.error(`[dependency_bundled] Error installing ${pipelineName} module:`, error);
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Error',
        message: `Installation failed: ${error.message}`,
        progress: 0
      });
      throw error;
    }
  }

  // --- New ImageEdit Module Installation ---
  async _installImageEditModule(pipelineName, component, name) {
    const progressName = name || 'all';
    const imageEditPath = path.join(PIPELINES_DIR, 'ImageEdit');

    // Initialize dependency status if it doesn't exist
    if (!this.dependencyStatus[pipelineName]) {
      this.dependencyStatus[pipelineName] = {
        python: { installed: false },
        models: { installed: false }
      };
    }

    // Handle "all" component - install both Python dependencies and models
    if (component === 'python' && name === 'all') {
      logger.info(`[_installImageEditModule] Installing all components (Python + Models)`);

      try {
        // First install Python dependencies
        logger.info(`[_installImageEditModule] Starting Python dependencies installation...`);
        await this._installImageEditModule(pipelineName, 'python', 'python');
        logger.info(`[_installImageEditModule] Python dependencies completed, starting models installation...`);

        // Then install models
        await this._installImageEditModule(pipelineName, 'models', 'all');
        logger.info(`[_installImageEditModule] Models installation completed`);

        logger.info(`[_installImageEditModule] All components installation completed successfully`);
        return Promise.resolve();
      } catch (error) {
        logger.error(`[_installImageEditModule] Error during all components installation:`, error);
        throw error;
      }
    }

    // Handle Python dependencies installation
    if (component === 'python' && name === 'python') {
      logger.info(`[_installImageEditModule] Installing Python dependencies`);

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Installing ImageEdit Python dependencies...',
        progress: 0,
      });

      try {
        // Ensure pipeline directory exists
        await fs.mkdir(imageEditPath, { recursive: true });

        // Create virtual environment
        await this._ensureVenvExists(pipelineName);

        // Get Python executable
        const pythonExe = this._getPythonExe(pipelineName);

        // Install Python dependencies
        const config = this.pipelines[pipelineName];
        const pythonDeps = config.dependencies?.python || [];

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: 'Installing Python packages...',
          progress: 20,
        });

        // Install Python dependencies
        for (let i = 0; i < pythonDeps.length; i++) {
          const dep = pythonDeps[i];
          logger.info(`Installing Python dependency: ${dep}`);

          await new Promise((resolve, reject) => {
            const proc = spawn(pythonExe, ['-m', 'pip', 'install', dep], {
              cwd: imageEditPath,
              windowsHide: true,
              stdio: ['ignore', 'pipe', 'pipe']
            });

            proc.stdout.on('data', d => logger.info(`[ImageEdit][pip] ${d}`));
            proc.stderr.on('data', d => logger.info(`[ImageEdit][pip] ${d}`));
            proc.on('close', code => {
              if (code === 0) resolve();
              else reject(new Error(`pip install ${dep} failed with code ${code}`));
            });
          });

          const progress = 20 + (i + 1) / pythonDeps.length * 40;
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Running',
            message: `Installed ${dep}`,
            progress: progress,
          });
        }

        // Install special packages (diffusers from git)
        const specialPackages = config.dependencies?.special_packages || [];

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: 'Installing special packages...',
          progress: 60,
        });

        for (let i = 0; i < specialPackages.length; i++) {
          const pkg = specialPackages[i];
          logger.info(`Installing special package: ${pkg.name} from ${pkg.url}`);

          if (pkg.type === 'pip_git') {
            await new Promise((resolve, reject) => {
              const proc = spawn(pythonExe, ['-m', 'pip', 'install', pkg.url], {
                cwd: imageEditPath,
                windowsHide: true,
                stdio: ['ignore', 'pipe', 'pipe']
              });

              proc.stdout.on('data', d => logger.info(`[ImageEdit][pip-git] ${d}`));
              proc.stderr.on('data', d => logger.info(`[ImageEdit][pip-git] ${d}`));
              proc.on('close', code => {
                if (code === 0) resolve();
                else reject(new Error(`pip install ${pkg.url} failed with code ${code}`));
              });
            });
          }

          const progress = 60 + (i + 1) / specialPackages.length * 20;
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Running',
            message: `Installed ${pkg.name}`,
            progress: progress,
          });
        }

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'ImageEdit Python dependencies installed successfully',
          progress: 100,
        });

        logger.info(`[_installImageEditModule] Python dependencies installation completed`);
        return Promise.resolve();

      } catch (error) {
        logger.error(`[_installImageEditModule] Python installation failed:`, error);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: `Failed to install Python dependencies: ${error.message}`,
          progress: 0,
        });
        throw error;
      }
    }

    // Handle Models installation
    if (component === 'models') {
      logger.info(`[_installImageEditModule] Installing models`);

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Installing ImageEdit models...',
        progress: 0,
      });

      try {
        const config = this.pipelines[pipelineName];
        const modelDeps = config.dependencies?.models || [];

        for (let i = 0; i < modelDeps.length; i++) {
          const model = modelDeps[i];
          logger.info(`Installing model: ${model.name} from ${model.repo_id}`);

          this._sendProgress(pipelineName, component, progressName, {
            status: 'Running',
            message: `Downloading ${model.name}... (${model.size || 'Large model'})`,
            progress: (i / modelDeps.length) * 100,
          });

          // Download model using huggingface_hub
          await this._downloadHuggingFaceModel(model, path.join(MODELS_DIR, model.local_path));

          this._sendProgress(pipelineName, component, progressName, {
            status: 'Running',
            message: `Downloaded ${model.name}`,
            progress: ((i + 1) / modelDeps.length) * 100,
          });
        }

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'ImageEdit models installed successfully',
          progress: 100,
        });

        logger.info(`[_installImageEditModule] Models installation completed`);
        return Promise.resolve();

      } catch (error) {
        logger.error(`[_installImageEditModule] Models installation failed:`, error);
        logger.error(`[_installImageEditModule] Error details:`, error.stack);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: `Failed to install models: ${error.message}`,
          progress: 0,
        });
        throw error;
      }
    }

    logger.warn(`[_installImageEditModule] Unknown component: ${component}`);
    return Promise.resolve();
  }

  // --- New ImageUpscaling Module Installation ---
  async _installImageUpscalingModule(pipelineName, component, name) {
    const progressName = name || 'all';
    const imageUpscalingPath = path.join(PIPELINES_DIR, 'ImageUpscaling');
    const installBatPath = path.join(WORKSPACE_ROOT, 'src', 'upscaleinstaller', 'install.bat');
    
    // Initialize dependency status if it doesn't exist
    if (!this.dependencyStatus[pipelineName]) {
      this.dependencyStatus[pipelineName] = {
        python: { installed: false },
        models: { installed: false }
      };
    }

    // Handle both python and models components with install.bat
    // Models are downloaded by the install.bat script

    // Check if already installed
    const isAlreadyInstalled = await this._validateImageUpscalingInstallation();
    if (isAlreadyInstalled) {
      logger.info('ImageUpscaling Module already installed');

      // Send a brief "checking" status first
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Checking ImageUpscaling installation...',
        progress: 50,
      });

      // Wait a moment then send completion and update status
      setTimeout(async () => {
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Complete',
          message: 'ImageUpscaling Module already installed',
          progress: 100,
        });

        // Update dependency status and notify UI
        try {
          await this.checkDependencies(pipelineName, true);
          if (this.mainWindow) {
            this.mainWindow.webContents.send('dependency-status-changed', {
              pipeline: pipelineName,
              type: component,
              status: 'installed'
            });
          }
        } catch (error) {
          logger.warn('Error updating dependency status after ImageUpscaling check:', error);
        }
      }, 1000);

      return;
    }

    // Initialize progress
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Running',
      message: 'Installing ImageUpscaling Module...',
      progress: 0,
    });

    // Ensure Python is installed for this pipeline first
    try {
      await this._ensurePythonInstalled(pipelineName);
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Python ready, preparing installation...',
        progress: 10,
      });
    } catch (error) {
      logger.error(`Failed to ensure Python for ImageUpscaling: ${error.message}`);
      throw new Error(`Python installation failed: ${error.message}`);
    }

    // Clean up any corrupted installation files before starting
    const envPath = path.join(imageUpscalingPath, 'env');
    const initDoneFile = path.join(envPath, 'upscaling_init_done.txt');

    try {
      // Remove init done file if it exists (forces reinstallation)
      if (await fs.access(initDoneFile).then(() => true).catch(() => false)) {
        await fs.unlink(initDoneFile);
        logger.info('Removed existing upscaling_init_done.txt to force clean installation');
      }

      // Remove env directory if it exists (forces clean venv creation)
      if (await fs.access(envPath).then(() => true).catch(() => false)) {
        await fs.rm(envPath, { recursive: true, force: true });
        logger.info('Removed existing env directory to force clean installation');
      }

      this._sendProgress(pipelineName, component, progressName, {
        status: 'Running',
        message: 'Cleaned up previous installation, starting fresh install...',
        progress: 15,
      });
    } catch (error) {
      logger.warn('Error during cleanup (continuing anyway):', error);
    }

    // Check if install.bat exists
    try {
      await fs.access(installBatPath);
    } catch (error) {
      logger.error(`ImageUpscaling install.bat not found at: ${installBatPath}`);
      throw new Error(`ImageUpscaling installation file not found: ${installBatPath}`);
    }

    return new Promise((resolve, reject) => {
      // Determine if this is a models-only installation
      const installArgs = ['/c', installBatPath, imageUpscalingPath];
      if (component === 'models') {
        installArgs.push('models-only');
        logger.info('Running ImageUpscaling installer in models-only mode');
      }

      const proc = spawn('cmd.exe', installArgs, {
        cwd: path.dirname(installBatPath),
        windowsHide: true,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let progress = 0;
      let stderr = '';

      proc.stdout.on('data', (data) => {
        const text = data.toString();
        logger.info(`ImageUpscaling install output: ${text.trim()}`);

        // Update progress based on output patterns
        if (component === 'models') {
          // Models-only installation progress
          if (text.includes('Skipping Python setup') || text.includes('models-only mode')) progress = Math.max(progress, 10);
          if (text.includes('Downloading upscaling models') || text.includes('Starting download of')) progress = Math.max(progress, 20);
          if (text.includes('Processing swinir-real-sr-x4') || text.includes('[1/5]')) progress = Math.max(progress, 30);
          if (text.includes('Processing realesrgan-x4plus') || text.includes('[2/5]')) progress = Math.max(progress, 50);
          if (text.includes('Processing realesrgan-x4plus-anime') || text.includes('[3/5]')) progress = Math.max(progress, 60);
          if (text.includes('Processing swinir-m-x4') || text.includes('[4/5]')) progress = Math.max(progress, 75);
          if (text.includes('Processing 4xlsdir') || text.includes('[5/5]')) progress = Math.max(progress, 85);
          if (text.includes('Model download completed') || text.includes('All required models downloaded')) progress = Math.max(progress, 95);
        } else {
          // Full installation progress
          if (text.includes('Creating virtual environment') || text.includes('venv')) progress = Math.max(progress, 15);
          if (text.includes('Upgrading pip') || text.includes('pip install --upgrade')) progress = Math.max(progress, 25);
          if (text.includes('Installing PyTorch') || text.includes('torch')) progress = Math.max(progress, 45);
          if (text.includes('Installing core dependencies') || text.includes('pillow')) progress = Math.max(progress, 60);
          if (text.includes('Installing additional upscaling dependencies')) progress = Math.max(progress, 75);
          if (text.includes('Downloading upscaling models') || text.includes('Starting download of')) progress = Math.max(progress, 85);
        }
        if (text.includes('Verifying installation') || text.includes('PyTorch version')) progress = Math.max(progress, 95);
        if (text.includes('Installation Complete') || text.includes('upscaling_init_done.txt')) progress = 100;

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: text.trim(),
          progress,
        });
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
        const errorText = data.toString().trim();
        logger.warn(`ImageUpscaling install stderr: ${errorText}`);
        // Don't send stderr as error status during installation - it's often just warnings
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Running',
          message: errorText,
          progress,
        });
      });

      proc.on('close', (code) => {
        // Check if environment folder exists to verify successful installation
        const envPath = path.join(imageUpscalingPath, 'env');
        const envExists = require('fs').existsSync(envPath);
        const initDoneExists = require('fs').existsSync(path.join(envPath, 'upscaling_init_done.txt'));

        if (code === 0 || progress >= 90 || (envExists && initDoneExists)) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Complete',
            message: 'ImageUpscaling Module installed successfully',
            progress: 100,
          });

          // Update dependency status and notify UI
          setTimeout(async () => {
            try {
              await this.checkDependencies(pipelineName, true);
              if (this.mainWindow) {
                this.mainWindow.webContents.send('dependency-status-changed', {
                  pipeline: pipelineName,
                  type: component,
                  status: 'installed'
                });
              }
            } catch (error) {
              logger.warn('Error updating dependency status after ImageUpscaling installation:', error);
            }
          }, 1000);

          resolve();
        } else {
          const errorMsg = `ImageUpscaling Module installation failed with code ${code}. Check if dependencies are properly installed.`;
          logger.error(errorMsg);
          if (stderr) {
            logger.error(`ImageUpscaling stderr output: ${stderr}`);
          }
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Error',
            message: errorMsg,
            progress: 0,
          });
          reject(new Error(errorMsg));
        }
      });

      proc.on('error', (error) => {
        const errorMsg = `Failed to start ImageUpscaling Module installation: ${error.message}`;
        logger.error(errorMsg);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        reject(new Error(errorMsg));
      });
    });
  }

  // Helper method for running commands with proper error handling
  async _runCommand(command, args, options = {}) {
    return new Promise((resolve) => {
      const proc = spawn(command, args, {
        ...options,
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      proc.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      proc.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, stdout, stderr });
        } else {
          resolve({ success: false, error: stderr || `Process exited with code ${code}`, stdout, stderr });
        }
      });

      proc.on('error', (error) => {
        resolve({ success: false, error: error.message, stdout, stderr });
      });
    });
  }

  // --- Video Generation Module Installation ---
  async _finalizeVideoGenerationInstallation(installDir, logStream) {
    this._logDependency(logStream, 'Starting finalization of Video Generation module');
    
    // Add any post-extraction steps here
    // For example, setting up environment variables, creating symlinks, etc.
    this._logDependency(logStream, 'Running post-extraction setup...');
    
    // Add a small delay to simulate finalization work
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    this._logDependency(logStream, 'Finalization completed successfully');
    return { success: true };
  }

  async _installVideoGenerationModule(pipelineName, component = 'python', name = 'all') {
    const logStream = this._startDependencyLogging(pipelineName, 'bundled installation');

    // Define all paths up front for consistency
    const sourceZip = path.join(WORKSPACE_ROOT, 'src', 'module_source', 'VideoGen', 'framepack_cu126_torch26.zip');
    const videoGenPipelineDir = path.join(PIPELINES_DIR, 'VideoPipelines');
    const framepackDir = path.join(videoGenPipelineDir, 'framepack_cu126_torch26');
    
    try {
      // Notify UI of initial status
      if (this.mainWindow) {
        this.mainWindow.webContents.send('dependency-status-changed', {
          pipeline: pipelineName,
          type: 'python',
          status: 'installing',
          message: 'Starting Video Generation module installation...'
        });
      }
      
      this._logDependency(logStream, `Starting portable installation of Video Generation module`);
      this._logDependency(logStream, `Source: ${sourceZip}`);
      this._logDependency(logStream, `Extraction Target: ${videoGenPipelineDir}`);
      
      // Step 1: Clean up any existing installation
      this._logDependency(logStream, `Checking for existing installation...`);
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Cleaning',
        message: 'Preparing for installation...',
        progress: 5
      });

      try {
        // First check if the target directory exists
        if (fsSync.existsSync(framepackDir)) {
          this._logDependency(logStream, `Removing existing directory: ${framepackDir}`);
          
          // Try powershell remove-item for maximum compatibility
          const cleanupProcess = spawn('powershell', [
            '-Command',
            `Remove-Item -Path '${framepackDir}' -Recurse -Force -ErrorAction Stop`
          ]);

          await new Promise((resolve, reject) => {
            cleanupProcess.on('close', (code) => {
              if (code === 0) {
                this._logDependency(logStream, 'PowerShell cleanup successful');
                resolve();
              } else {
                reject(new Error(`PowerShell cleanup failed with code ${code}`));
              }
            });
            
            cleanupProcess.on('error', (err) => {
              reject(new Error(`PowerShell cleanup error: ${err.message}`));
            });
          });
        } else {
          this._logDependency(logStream, 'No existing framepack directory found');
        }

        // Also check for any additional framepack related directories
        const videoPipelinesContent = await fs.readdir(videoGenPipelineDir).catch(() => []);
        for (const item of videoPipelinesContent) {
          if (item.toLowerCase().includes('framepack')) {
            const pathToRemove = path.join(videoGenPipelineDir, item);
            this._logDependency(logStream, `Found additional framepack directory: ${pathToRemove}, removing...`);
            await fs.rm(pathToRemove, { recursive: true, force: true }).catch(e => 
              this._logDependency(logStream, `Failed to remove ${pathToRemove}: ${e.message}`, 'warn')
            );
          }
        }

        this._logDependency(logStream, 'Cleanup completed successfully');
      } catch (error) {
        this._logDependency(logStream, `Cleanup error: ${error.message}. Will attempt to continue...`, 'warn');
      }
      
      // Step 2: Ensure parent directory exists and is empty
      await fs.mkdir(videoGenPipelineDir, { recursive: true });
      this._logDependency(logStream, 'Parent directory prepared');
      
      // Step 3: Verify the source zip exists
      this._logDependency(logStream, `Verifying source zip: ${sourceZip}`);
      if (!fsSync.existsSync(sourceZip)) {
        throw new Error(`Source zip file not found at: ${sourceZip}`);
      }
      

      // Moving on to extraction
      this._logDependency(logStream, `Using Python script for reliable extraction`);
      
      // Step 4: Extract the zip file using Python for reliability
      this._logDependency(logStream, `Starting extraction of ${sourceZip} to ${videoGenPipelineDir}`);
      
      // Initialize main progress bar
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Installing',
        message: 'Preparing Video Generation module installation...',
        progress: 10
      });

      // Also update the module-specific extraction progress
      this._sendProgress(pipelineName, 'VideoGeneration', 'extraction', {
        status: 'Preparing',
        message: 'Starting extraction...',
        progress: 0
      });
      
      try {
        const pythonScript = path.join(__dirname, 'python_helpers', 'extract_zip.py');
        this._logDependency(logStream, `Using Python script: ${pythonScript}`);

        // Use the Python script for reliable zip extraction
        const result = await new Promise((resolve, reject) => {
          this._logDependency(logStream, `Starting Python-based extraction from ${sourceZip} to ${videoGenPipelineDir}`);
          
          const pythonProcess = spawn('python', [
            pythonScript,
            sourceZip,
            videoGenPipelineDir
          ]);

          let stdout = '', stderr = '';
          
          pythonProcess.stdout.on('data', (data) => {
            const dataStr = data.toString();
            stdout += dataStr;
            
            // Try to parse each line as JSON
            const lines = dataStr.split('\n').filter(line => line.trim() !== '');
            for (const line of lines) {
              try {
                const progress = JSON.parse(line);
                if (progress.status === 'extracting' || progress.status === 'completed') {
                  const percent = Math.floor(progress.progress);
                  const message = progress.status === 'completed' 
                    ? 'Extraction completed' 
                    : `Extracting files... (${percent}%)`;
                  
                  // Calculate progress values for both main and module-specific progress bars
                  const mainProgress = 10 + (progress.progress * 0.6); // Scale to 10-70% of total progress

                  // Update the main progress bar using _sendProgress for consistency
                  this._sendProgress(pipelineName, 'python', 'all', {
                    status: 'Installing',
                    message: `Extracting files... (${percent}%)`,
                    progress: mainProgress
                  });

                  // Update the extraction-specific progress
                  this._sendProgress(pipelineName, 'VideoGeneration', 'extraction', {
                    status: 'Extracting',
                    message: `Extracting files... (${percent}%)`,
                    progress: progress.progress
                  });
                  
                  // Log every 10% progress
                  if (percent % 10 === 0 && progress.progress % 10 < 1) {
                    this._logDependency(logStream, `Extraction progress: ${percent}% - ${progress.message || ''}`.trim());
                  }
                } else if (progress.status === 'error') {
                  throw new Error(progress.message);
                }
              } catch (e) {
                // If not JSON or invalid, log as regular output
                this._logDependency(logStream, line.trim());
              }
            }
          });

          pythonProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            this._logDependency(logStream, data.toString().trim(), 'warn');
          });

          pythonProcess.on('close', (code) => {
            if (code === 0) {
              this._logDependency(logStream, 'Python extraction completed successfully');

              // Update main progress to 70% (completion of extraction phase)
              this._sendProgress(pipelineName, 'python', 'all', {
                status: 'Verifying',
                message: 'Verifying installation...',
                progress: 70
              });

              // Mark extraction as complete in the sub-progress
              this._sendProgress(pipelineName, 'VideoGeneration', 'extraction', {
                status: 'Completed',
                message: 'Extraction completed',
                progress: 100
              });

              resolve({ success: true });
            } else {
              const errorMsg = `Python extraction failed with code ${code}: ${stderr || 'No error details'}`;
              this._logDependency(logStream, errorMsg, 'error');
              // Send error to UI
              this._sendProgress(pipelineName, 'python', 'all', {
                status: 'Error',
                message: errorMsg,
                progress: 0
              });
              reject(new Error(errorMsg));
            }
          });

          pythonProcess.on('error', (error) => {
            const errorMsg = `Failed to start Python extraction: ${error.message}`;
            this._logDependency(logStream, errorMsg, 'error');
            // Send error to UI
            this._sendProgress(pipelineName, 'python', 'all', {
              status: 'Error',
              message: errorMsg,
              progress: 0
            });
            reject(new Error(errorMsg));
          });
        });

        // Verify extraction
        const files = await fs.readdir(videoGenPipelineDir);
        this._logDependency(logStream, `Extracted contents: ${files.length} files found`);

        if (files.length === 0) {
          throw new Error('Extraction completed but no files were found in the target directory');
        }

        this._logDependency(logStream, 'Extraction completed successfully');

        // Update progress to 80% (verification complete)
        this._sendProgress(pipelineName, 'python', 'all', {
          status: 'Finalizing',
          message: 'Finalizing installation...',
          progress: 80
        });

        // Run finalization steps
        this._logDependency(logStream, 'Running post-installation finalization...');
        await this._finalizeVideoGenerationInstallation(videoGenPipelineDir, logStream);

        // Mark as complete
        this._sendProgress(pipelineName, 'python', 'all', {
          status: 'Complete',
          message: 'Video Generation module installed successfully',
          progress: 100
        });

        this._logDependency(logStream, 'Video Generation module installation completed successfully');

      } catch (error) {
        this._logDependency(logStream, `Failed to extract zip file: ${error.message}`, 'error');
        this._logDependency(logStream, `Stack: ${error.stack}`, 'error');

        // Update main progress on error
        this._sendProgress(pipelineName, 'python', 'all', {
          status: 'Error',
          message: `Installation failed: ${error.message}`,
          progress: 0
        });

        // Update extraction progress on error
        this._sendProgress(pipelineName, 'VideoGeneration', 'extraction', {
          status: 'Error',
          message: `Extraction failed: ${error.message}`,
          progress: 0
        });

        throw new Error(`Failed to extract Video Generation module: ${error.message}`);
      }

      // Step 5: Verify the installation
      this._logDependency(logStream, 'Verifying installation...');
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Verifying',
        message: 'Verifying installation...',
        progress: 85
      });
      
      // Add a small delay to ensure the UI updates
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Required paths to check relative to VideoPipelines directory
      const requiredItems = [
        { path: path.join('framepack_cu126_torch26'), type: 'directory' },
        { path: path.join('framepack_cu126_torch26', 'system'), type: 'directory' },
        { path: path.join('framepack_cu126_torch26', 'run.bat'), type: 'file' },
        { path: path.join('framepack_cu126_torch26', 'update.bat'), type: 'file' },
        { path: path.join('framepack_cu126_torch26', 'environment.bat'), type: 'file' }
      ];
      
      // Calculate progress increment for each item
      const progressIncrement = 20 / requiredItems.length; // 20% of total progress (from 80% to 100%)

      const missingItems = [];
      let currentProgress = 80;

      for (let i = 0; i < requiredItems.length; i++) {
        const item = requiredItems[i];
        const fullPath = path.join(videoGenPipelineDir, item.path);
        this._logDependency(logStream, `Checking ${item.type}: ${fullPath}`);
        
        // Update progress
        currentProgress = 80 + (i * progressIncrement);
        const percent = Math.min(99, Math.round(currentProgress));
        this._sendProgress(pipelineName, 'python', 'all', {
          status: 'Verifying',
          message: `Verifying ${item.path}... (${percent}%)`,
          progress: currentProgress,
          percent: percent
        });
        
        // Add a small delay to ensure the UI updates
        await new Promise(resolve => setTimeout(resolve, 50));
        
        try {
          const stats = await fs.stat(fullPath);
          const isCorrectType = item.type === 'directory' ? stats.isDirectory() : stats.isFile();
          if (!isCorrectType) {
            missingItems.push(`${item.path} (wrong type)`);
            this._logDependency(logStream, `Invalid type for ${item.path}`, 'warn');
          } else {
            this._logDependency(logStream, `Found ${item.type}: ${item.path}`);
          }
        } catch (error) {
          missingItems.push(item.path);
          this._logDependency(logStream, `Missing ${item.type}: ${item.path}`, 'warn');
        }
      }
      
      if (missingItems.length > 0) {
        const errorMsg = `Installation verification failed. Missing or invalid items: ${missingItems.join(', ')}`;
        this._logDependency(logStream, errorMsg, 'error');
        throw new Error(errorMsg);
      }
      
      // Step 6: Update dependency status
      this._logDependency(logStream, 'Installation verification successful');
      
      // Update the dependency status to mark as installed
      if (!this.dependencyStatus[pipelineName]) {
        this.dependencyStatus[pipelineName] = {};
      }
      
      this.dependencyStatus[pipelineName].python = { 
        installed: true, 
        details: { 
          'framepack': { 
            satisfied: true, 
            installed: 'Installed', 
            required: 'Required' 
          } 
        },
        status: 'installed',
        message: 'Video Generation module installed successfully'
      };
      
      this.dependencyStatus[pipelineName].models = { 
        installed: true,
        status: 'installed',
        message: 'Models verified'
      };
      
      this.dependencyStatus[pipelineName].bundled = { 
        installed: true,
        status: 'installed',
        message: 'Bundled components installed'
      };

      // Persist the updated status to the store
      if (this.store) {
        this.store.set('dependencyStatus', this.dependencyStatus);
      }
      
      // Update UI with success status
      this._sendProgress(pipelineName, 'python', 'all', {
        status: 'Complete',
        message: 'Video Generation module installed successfully',
        progress: 100
      });
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send('dependency-status-changed', {
          pipeline: pipelineName,
          type: 'python',
          status: 'installed',
          details: this.dependencyStatus[pipelineName].python.details,
          message: 'Video Generation module installed successfully'
        });

        // Force a refresh of the dependency status in the UI
        this.mainWindow.webContents.send('refresh-dependencies');
      }
      
      this._logDependency(logStream, 'Video Generation module installation completed successfully');
      this._endDependencyLogging(logStream, pipelineName, 'bundled installation', true);
      return { success: true, message: 'Video Generation module installed successfully' };
    } catch (error) {
      this._logDependency(logStream, `Error during installation: ${error.message}`, 'error');
      
      // Update UI with error status
      if (this.mainWindow) {
        this.mainWindow.webContents.send('dependency-status-changed', {
          pipeline: pipelineName,
          type: 'python',
          status: 'error',
          details: { 'Video Generation Module': { satisfied: false, installed: 'Failed', required: 'Bundled' } },
          message: `Installation failed: ${error.message}`
        });
      }
      
      this._endDependencyLogging(logStream, pipelineName, 'bundled installation', false);
      throw error;
    }
  }

  // --- FramePack Module Installation ---
  async _installFramePackModule(pipelineName, component, name) {
    const progressName = name || 'all';
    
    // Initialize dependency status if it doesn't exist
    if (!this.dependencyStatus[pipelineName]) {
      this.dependencyStatus[pipelineName] = {
        python: { installed: false },
        models: { installed: false }
      };
    }

    logger.info(`[_installFramePackModule] Called with: pipeline=${pipelineName}, component=${component}, name=${name}`);

    // Ensure pipeline exists and is loaded
    await this._ensurePipelineExists(pipelineName);
    logger.info(`[_installFramePackModule] Pipeline exists check completed`);

    // Handle "all" component - install both Python dependencies and models
    if (component === 'python' && name === 'all') {
      logger.info(`[_installFramePackModule] Installing all components (Python + Models)`);

      try {
        // First install Python dependencies
        logger.info(`[_installFramePackModule] Starting Python dependencies installation...`);
        await this._installFramePackModule(pipelineName, 'python', 'python');
        logger.info(`[_installFramePackModule] Python dependencies completed, starting models installation...`);

        // Then install models
        await this._installFramePackModule(pipelineName, 'models', 'all');
        logger.info(`[_installFramePackModule] Models installation completed`);

        logger.info(`[_installFramePackModule] All components installation completed successfully`);
        return Promise.resolve();
      } catch (error) {
        logger.error(`[_installFramePackModule] Error during all components installation:`, error);
        throw error;
      }
    }

    // Handle python component - install dependencies and clone FramePack repository
    if (component === 'python') {
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Installing',
        message: 'Installing FramePack dependencies...',
        progress: 0,
      });

      try {
        // Install Python dependencies using the direct method (avoid recursion)
        const pythonDeps = this.pipelines[pipelineName].dependencies.python || [];
        logger.info(`[_installFramePackModule] Installing ${pythonDeps.length} Python dependencies`);
        await this._installPythonDependenciesDirect(pipelineName, pythonDeps, true);

        // Install special packages (FramePack repository)
        const specialPackages = this.pipelines[pipelineName].dependencies.special_packages;
        logger.info(`[_installFramePackModule] Found ${specialPackages?.length || 0} special packages`);
        if (specialPackages && specialPackages.length > 0) {
          this._sendProgress(pipelineName, component, progressName, {
            status: 'Installing',
            message: 'Installing special packages...',
            progress: 70,
          });

          logger.info(`[_installFramePackModule] Installing special packages...`);
          await this._installSpecialPackages(pipelineName, specialPackages);
          logger.info(`[_installFramePackModule] Special packages installation completed`);
        }

        logger.info(`[_installFramePackModule] Python component installation completed successfully`);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Completed',
          message: 'FramePack dependencies installed successfully',
          progress: 100,
        });

        return Promise.resolve();
      } catch (error) {
        const errorMsg = `FramePack installation failed: ${error.message}`;
        logger.error(`[_installFramePackModule] ${errorMsg}`, error);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        throw error;
      }
    }

    // Handle models component - download FramePack models
    if (component === 'models') {
      logger.info(`[_installFramePackModule] Handling models component for ${pipelineName}`);
      this._sendProgress(pipelineName, component, progressName, {
        status: 'Installing',
        message: 'Downloading FramePack models...',
        progress: 0,
      });

      try {
        logger.info(`[_installFramePackModule] Accessing models from this.pipelines[${pipelineName}]`);
        const pipeline = this.pipelines[pipelineName];
        if (!pipeline) {
          logger.error(`[_installFramePackModule] Pipeline ${pipelineName} not found in this.pipelines`);
          throw new Error(`Pipeline ${pipelineName} not found`);
        }
        const models = pipeline.dependencies.models;
        logger.info(`[_installFramePackModule] Found ${models?.length || 0} models to install`);
        const pythonExe = this._getPythonExe(pipelineName);
        const hfToken = this.getHuggingFaceToken();

        for (let i = 0; i < models.length; i++) {
          const model = models[i];
          const progress = Math.round((i / models.length) * 100);

          this._sendProgress(pipelineName, component, progressName, {
            status: 'Installing',
            message: `Downloading ${model.name}...`,
            progress: progress,
          });

          const localPath = path.join(MODELS_DIR, model.local_path);

          // Use the standard model download approach
          logger.info(`Starting download of model ${model.name} for ${pipelineName}`);
          logger.info(`Model ${model.name} will be downloaded to: ${localPath}`);

          // Create directory if it doesn't exist
          await fs.mkdir(path.dirname(localPath), { recursive: true });
          await fs.mkdir(localPath, { recursive: true });

          // Clean up any existing script first
          let scriptPath = path.join(HELPERS_DIR, `download_${model.name.replace(/[^a-zA-Z0-9]/g, '_')}.py`);
          try {
            await fs.unlink(scriptPath);
          } catch (error) {
            // Ignore if file doesn't exist
          }

          // Escape the token for Python string
          const escapedToken = hfToken ? hfToken.replace(/\\/g, '\\\\').replace(/"/g, '\\"') : '';

          const scriptContent = `
import os
import sys
import traceback
import json
import time

try:
    from huggingface_hub import snapshot_download, HfApi

    def report_progress(progress_type, message, progress=0, **kwargs):
        data = {
            'type': progress_type,
            'message': message,
            'progress': progress,
            'model': '${model.name}',
            'timestamp': time.time(),
            **kwargs
        }
        print(json.dumps(data), flush=True)

    def download_model():
        repo_id = "${model.repo_id}"
        local_dir = "${localPath.replace(/\\/g, '/')}"
        token = "${escapedToken}"

        # Only use token if it's not empty
        if not token or token.strip() == "":
            token = None
            report_progress('warning', 'No Hugging Face token provided - using anonymous access', 3)
        else:
            report_progress('info', f'Using Hugging Face token (length: {len(token)})', 3)

        report_progress('init', f'Initializing download for {repo_id}', 0)

        # Create directory if it doesn't exist
        os.makedirs(local_dir, exist_ok=True)

        # Check repository access
        try:
            report_progress('checking', 'Checking repository access...', 5)
            api = HfApi(token=token)
            repo_info = api.repo_info(repo_id=repo_id, token=token)
            report_progress('info', f'Repository found: {repo_id}', 10)
        except Exception as e:
            error_msg = str(e).lower()
            if ("gated" in error_msg or "access" in error_msg) and ${model.requires_auth ? 'True' : 'False'}:
                report_progress('error', "${model.auth_error_message || 'Repository requires authentication. Please check your Hugging Face token.'}", 0)
                sys.exit(1)
            else:
                report_progress('warning', f'Could not check repository info: {e}', 10)

        # Download the model
        try:
            report_progress('downloading', f'Starting download of {repo_id}...', 15)

            # Download with progress tracking
            snapshot_download(
                repo_id=repo_id,
                local_dir=local_dir,
                token=token,
                resume_download=True,
                local_dir_use_symlinks=False
            )

            report_progress('success', f'Successfully downloaded {repo_id}', 100)

        except Exception as e:
            error_msg = str(e)
            report_progress('error', f'Download failed: {error_msg}', 0)
            sys.exit(1)

    if __name__ == "__main__":
        download_model()

except ImportError as e:
    print(json.dumps({
        'type': 'error',
        'message': f'Missing required package: {e}. Please install huggingface_hub.',
        'progress': 0,
        'model': '${model.name}'
    }), flush=True)
    sys.exit(1)
except Exception as e:
    print(json.dumps({
        'type': 'error',
        'message': f'Unexpected error: {e}',
        'progress': 0,
        'model': '${model.name}'
    }), flush=True)
    sys.exit(1)
`;

          await fs.writeFile(scriptPath, scriptContent);
          logger.info(`Created download script: ${scriptPath}`);

          // Execute the download script
          await new Promise((resolve, reject) => {
            const env = { ...process.env };
            const scriptsDir = path.dirname(pythonExe);
            delete env.PYTHONHOME;
            delete env.PYTHONPATH;
            env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

            const downloadProcess = spawn(pythonExe, [scriptPath], { env });
            let downloadProgress = 0;

            downloadProcess.stdout.on('data', (data) => {
              const lines = data.toString().split('\n').filter(line => line.trim());
              for (const line of lines) {
                try {
                  const progressData = JSON.parse(line);
                  if (progressData.type === 'downloading' || progressData.type === 'success') {
                    downloadProgress = Math.max(downloadProgress, progressData.progress || 0);
                    this._sendProgress(pipelineName, component, progressName, {
                      status: 'Installing',
                      message: progressData.message || `Downloading ${model.name}...`,
                      progress: Math.round((i / models.length) * 100 + (downloadProgress / models.length)),
                    });
                  }
                } catch (e) {
                  // Ignore non-JSON output
                }
              }
            });

            downloadProcess.stderr.on('data', (data) => {
              logger.warn(`Model download stderr: ${data.toString()}`);
            });

            downloadProcess.on('close', (code) => {
              if (code === 0) {
                logger.info(`Successfully downloaded model ${model.name}`);
                resolve();
              } else {
                const errorMsg = `Model download failed with exit code ${code}`;
                logger.error(errorMsg);
                reject(new Error(errorMsg));
              }
            });

            downloadProcess.on('error', (error) => {
              logger.error(`Model download process error: ${error.message}`);
              reject(error);
            });
          });
        }

        this._sendProgress(pipelineName, component, progressName, {
          status: 'Completed',
          message: 'FramePack models downloaded successfully',
          progress: 100,
        });

        // Update dependency status and notify UI (with delay to ensure files are written)
        setTimeout(async () => {
          try {
            logger.info(`[_installFramePackModule] Updating dependency status for ${pipelineName}`);
            await this.checkDependencies(pipelineName, true);
            logger.info(`[_installFramePackModule] Dependency status updated successfully for ${pipelineName}`);
            if (this.mainWindow) {
              logger.info(`[_installFramePackModule] Sending dependency-status-changed event for ${pipelineName}`);
              this.mainWindow.webContents.send('dependency-status-changed', {
                pipeline: pipelineName,
                type: component,
                status: 'installed'
              });
            }
          } catch (statusError) {
            logger.warn(`Failed to update dependency status for ${pipelineName}:`, statusError);
          }
        }, 2000); // 2 second delay to ensure files are fully written

        return Promise.resolve();
      } catch (error) {
        const errorMsg = `FramePack model download failed: ${error.message}`;
        logger.error(errorMsg);
        logger.error(`[_installFramePackModule] Error details:`, error.stack);
        this._sendProgress(pipelineName, component, progressName, {
          status: 'Error',
          message: errorMsg,
          progress: 0,
        });
        throw error;
      }
    }

    // For other components, return success
    logger.info(`[_installFramePackModule] Handling other component: ${component}`);
    this._sendProgress(pipelineName, component, progressName, {
      status: 'Completed',
      message: `${component} installation completed`,
      progress: 100,
    });
    return Promise.resolve();
  }
}

module.exports = DependencyManager;