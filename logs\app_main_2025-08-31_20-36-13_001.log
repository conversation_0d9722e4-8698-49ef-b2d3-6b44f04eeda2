========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:36:13.099Z
File: app_main_2025-08-31_20-36-13_001.log
========================================

[2025-09-01T01:36:13.347Z] [INFO] AIStudio application started successfully
[2025-09-01T01:36:13.347Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:36:13.377Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:36:14.413Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:36:32.394Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-36-32_001.log
[2025-09-01T01:36:32.395Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:37:11.929Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T01:37:11.930Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T01:37:11.931Z] [INFO] [34m[Video Server][39m Batch file exists: true
[2025-09-01T01:37:11.934Z] [INFO] [34m[Video Server][39m Working directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-01T01:37:11.935Z] [INFO] [34m[Video Server][39m Command: cmd.exe /c run.bat
[2025-09-01T01:37:41.978Z] [ERROR] Framepack video generation failed: Error: Server startup timed out after 30 seconds
