========================================
AIStudio Real-time Log: main
Started: 2025-08-31T00:22:58.420Z
File: app_main_2025-08-30_19-22-58_001.log
========================================

[2025-08-31T00:22:58.644Z] [INFO] AIStudio application started successfully
[2025-08-31T00:22:58.644Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T00:22:58.681Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T00:22:59.614Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T00:23:16.146Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_19-23-16_001.log
[2025-08-31T00:23:16.146Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
