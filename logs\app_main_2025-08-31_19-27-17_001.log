========================================
AIStudio Real-time Log: main
Started: 2025-09-01T00:27:17.711Z
File: app_main_2025-08-31_19-27-17_001.log
========================================

[2025-09-01T00:27:17.947Z] [INFO] AIStudio application started successfully
[2025-09-01T00:27:17.948Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T00:27:17.980Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T00:27:18.988Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T00:28:00.900Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_19-28-00_001.log
[2025-09-01T00:28:00.900Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T00:30:13.046Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_python installation_2025-08-31_19-30-13_001.log
[2025-09-01T00:30:13.047Z] [INFO] [dependency_core] DependencyManager: Starting python installation for Core
[2025-09-01T00:30:13.047Z] [INFO] [dependency_core] DependencyManager: Installing dependencies for Core (python:all)
[2025-09-01T00:30:13.048Z] [INFO] [dependency_core] DependencyManager: Component type: string, Component value: 'python'
[2025-09-01T00:30:13.048Z] [INFO] [dependency_core] DependencyManager: Name type: string, Name value: 'all'
[2025-09-01T00:30:13.049Z] [INFO] [dependency_core] DependencyManager: About to check routing for Core with component python
[2025-09-01T00:30:13.051Z] [INFO] [dependency_core] DependencyManager: Installing Python dependencies for Core...
[2025-09-01T00:30:13.057Z] [INFO] [dependency_core] DependencyManager: Failed to install dependencies: Failed to create/verify virtual environment: this._getVenvPath is not a function
[2025-09-01T00:30:13.058Z] [INFO] [dependency_core] DependencyManager: Failed python installation for Core
[2025-09-01T00:30:14.074Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
