========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T01:58:46.030Z
File: dependency_python installation_2025-08-30_20-58-46_001.log
========================================

[2025-08-31T01:58:46.030Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:58:46.032Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:58:46.032Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:58:46.033Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:58:46.034Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
