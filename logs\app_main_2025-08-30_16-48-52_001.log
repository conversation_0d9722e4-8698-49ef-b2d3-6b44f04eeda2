========================================
AIStudio Real-time Log: main
Started: 2025-08-30T21:48:52.532Z
File: app_main_2025-08-30_16-48-52_001.log
========================================

[2025-08-30T21:48:52.798Z] [INFO] AIStudio application started successfully
[2025-08-30T21:48:52.798Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T21:48:52.834Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T21:48:53.730Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T21:49:06.165Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_16-49-06_001.log
[2025-08-30T21:49:06.165Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
