========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T17:14:17.436Z
File: dependency_python installation_2025-08-31_12-14-17_001.log
========================================

[2025-08-31T17:14:17.436Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:14:17.437Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:14:17.438Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:14:17.438Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:14:17.439Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
