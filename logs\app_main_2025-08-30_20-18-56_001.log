========================================
AIStudio Real-time Log: main
Started: 2025-08-31T01:18:56.632Z
File: app_main_2025-08-30_20-18-56_001.log
========================================

[2025-08-31T01:18:56.889Z] [INFO] AIStudio application started successfully
[2025-08-31T01:18:56.889Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T01:18:56.924Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T01:18:57.883Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T01:19:11.050Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_20-19-11_001.log
[2025-08-31T01:19:11.051Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T01:21:48.745Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_20-21-48_001.log
[2025-08-31T01:21:48.745Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:21:48.746Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:21:48.746Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:21:48.747Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:21:48.747Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T01:21:48.749Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_20-21-48_001.log
[2025-08-31T01:21:48.749Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
