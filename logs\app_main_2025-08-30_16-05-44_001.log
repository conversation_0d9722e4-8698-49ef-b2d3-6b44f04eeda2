========================================
AIStudio Real-time Log: main
Started: 2025-08-30T21:05:44.085Z
File: app_main_2025-08-30_16-05-44_001.log
========================================

[2025-08-30T21:05:44.290Z] [INFO] AIStudio application started successfully
[2025-08-30T21:05:44.290Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T21:05:44.321Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T21:05:45.280Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T21:05:57.480Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_16-05-57_001.log
[2025-08-30T21:05:57.481Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T21:05:59.890Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T21:05:59.891Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T21:06:00.901Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
