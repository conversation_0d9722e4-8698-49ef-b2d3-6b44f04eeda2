========================================
AIStudio Real-time Log: main
Started: 2025-09-03T15:50:26.668Z
File: app_main_2025-09-03_10-50-26_001.log
========================================

[2025-09-03T15:50:26.824Z] [ERROR] App threw an error during load
[2025-09-03T15:50:26.825Z] [ERROR] Error: Attempted to register a second handler for 'resolve-video-path'
    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:101132)
    at Object.<anonymous> (N:\AIStudio\src\main\index.js:1706:9)
    at Module._compile (node:internal/modules/cjs/loader:1484:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
    at Module.load (node:internal/modules/cjs/loader:1295:32)
    at Module._load (node:internal/modules/cjs/loader:1111:12)
    at c._load (node:electron/js2c/node_init:2:16955)
    at cjsLoader (node:internal/modules/esm/translators:350:17)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:286:7)
    at ModuleJob.run (node:internal/modules/esm/module_job:234:25)
[2025-09-03T15:50:26.826Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T15:50:26.826Z] [ERROR] [main] ERROR: Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T15:50:26.826Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T15:50:26.958Z] [INFO] AIStudio application started successfully
[2025-09-03T15:50:26.958Z] [INFO] [main] AIStudio application started successfully
[2025-09-03T15:50:26.996Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-03T15:50:28.011Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-03T15:51:42.388Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-03_10-51-42_001.log
[2025-09-03T15:51:42.388Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-03T17:28:57.533Z] [ERROR] Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
[2025-09-03T17:30:41.396Z] [ERROR] Error occurred in handler for 'install-dependencies': Error: No handler registered for 'install-dependencies'
