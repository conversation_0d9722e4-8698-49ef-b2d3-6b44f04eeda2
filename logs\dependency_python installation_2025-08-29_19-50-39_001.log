========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-08-30T00:50:39.100Z
File: dependency_python installation_2025-08-29_19-50-39_001.log
========================================

[2025-08-30T00:50:39.101Z] [INFO] DependencyManager: Starting python installation for ImageGeneration
[2025-08-30T00:50:39.102Z] [INFO] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-08-30T00:50:39.102Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-30T00:50:39.103Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-30T00:50:39.103Z] [INFO] DependencyManager: About to check routing for ImageGeneration with component python
