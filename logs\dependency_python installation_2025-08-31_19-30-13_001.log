========================================
AIStudio Real-time Log: dependency_core
Started: 2025-09-01T00:30:13.046Z
File: dependency_python installation_2025-08-31_19-30-13_001.log
========================================

[2025-09-01T00:30:13.047Z] [INFO] DependencyManager: Starting python installation for Core
[2025-09-01T00:30:13.047Z] [INFO] DependencyManager: Installing dependencies for Core (python:all)
[2025-09-01T00:30:13.048Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-09-01T00:30:13.048Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-09-01T00:30:13.049Z] [INFO] DependencyManager: About to check routing for Core with component python
[2025-09-01T00:30:13.051Z] [INFO] DependencyManager: Installing Python dependencies for Core...
[2025-09-01T00:30:13.057Z] [ERROR] DependencyManager: Failed to install dependencies: Failed to create/verify virtual environment: this._getVenvPath is not a function
[2025-09-01T00:30:13.058Z] [ERROR] DependencyManager: Failed python installation for Core

========================================
Log ended: 2025-09-01T00:30:14.074Z
========================================
