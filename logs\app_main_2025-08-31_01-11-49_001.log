========================================
AIStudio Real-time Log: main
Started: 2025-08-31T06:11:49.960Z
File: app_main_2025-08-31_01-11-49_001.log
========================================

[2025-08-31T06:11:50.217Z] [INFO] AIStudio application started successfully
[2025-08-31T06:11:50.217Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T06:11:50.250Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T06:11:51.213Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T06:12:08.402Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_01-12-08_001.log
[2025-08-31T06:12:08.402Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T06:12:36.289Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-12-36_001.log
[2025-08-31T06:12:36.289Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:12:36.290Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:12:36.291Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:12:36.291Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:12:36.292Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:12:36.293Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-12-36_001.log
[2025-08-31T06:12:36.293Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T06:13:40.481Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-13-40_001.log
[2025-08-31T06:13:40.481Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:13:40.482Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:13:40.482Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:13:40.482Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:13:40.483Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:13:40.484Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-13-40_001.log
[2025-08-31T06:13:40.484Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T06:13:41.614Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-13-41_001.log
[2025-08-31T06:13:41.614Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:13:41.615Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:13:41.615Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:13:41.615Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:13:41.616Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:13:41.617Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-13-41_001.log
[2025-08-31T06:13:41.617Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T06:13:42.197Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-13-42_001.log
[2025-08-31T06:13:42.197Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:13:42.198Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:13:42.198Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:13:42.199Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:13:42.199Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:13:42.200Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-13-42_001.log
[2025-08-31T06:13:42.200Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T06:13:43.052Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_01-13-43_001.log
[2025-08-31T06:13:43.053Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T06:13:43.053Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T06:13:43.054Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T06:13:43.054Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T06:13:43.054Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T06:13:43.055Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_01-13-43_001.log
[2025-08-31T06:13:43.055Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
