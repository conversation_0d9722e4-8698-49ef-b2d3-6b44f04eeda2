========================================
AIStudio Real-time Log: main
Started: 2025-09-01T02:52:42.873Z
File: app_main_2025-08-31_21-52-42_001.log
========================================

[2025-09-01T02:52:43.121Z] [INFO] AIStudio application started successfully
[2025-09-01T02:52:43.121Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T02:52:43.146Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T02:52:44.092Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T02:53:01.723Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_21-53-01_001.log
[2025-09-01T02:53:01.723Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T02:54:03.272Z] [INFO] [RealtimeLogger] Started logging Starting server... to: Starting server..._2025-08-31_21-54-03_001.log
[2025-09-01T02:54:03.272Z] [INFO] [Starting server...] video-server
[2025-09-01T02:54:03.274Z] [INFO] [RealtimeLogger] Started logging RUN_BAT: to: RUN_BAT:_2025-08-31_21-54-03_001.log
[2025-09-01T02:54:03.274Z] [INFO] [RUN_BAT:] video-server
[2025-09-01T02:54:03.275Z] [INFO] [RealtimeLogger] Started logging Batch file exists: to: Batch file exists:_2025-08-31_21-54-03_001.log
[2025-09-01T02:54:03.276Z] [INFO] [Batch file exists:] video-server
[2025-09-01T02:54:03.277Z] [INFO] [RealtimeLogger] Started logging Working directory: to: Working directory:_2025-08-31_21-54-03_001.log
[2025-09-01T02:54:03.278Z] [INFO] [Working directory:] video-server
[2025-09-01T02:54:03.279Z] [INFO] [RealtimeLogger] Started logging Command: cmd.exe /c to: Command: cmd.exe /c_2025-08-31_21-54-03_001.log
[2025-09-01T02:54:03.279Z] [INFO] [Command: cmd.exe /c] video-server
[2025-09-01T02:54:03.397Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-54-03_001.log'
[2025-09-01T02:54:03.397Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-54-03_001.log'
[2025-09-01T02:54:03.397Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_21-54-03_001.log'
[2025-09-01T02:54:08.330Z] [INFO] [RealtimeLogger] Started logging Status check error: this.isServerRunning is not a function to: Status check error: this.isServerRunning is not a function_2025-08-31_21-54-08_001.log
[2025-09-01T02:54:08.330Z] [INFO] [Status check error: this.isServerRunning is not a function] video-server
[2025-09-01T02:54:17.949Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed! to: [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!_2025-08-31_21-54-17_001.log
[2025-09-01T02:54:17.949Z] [INFO] [[STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!] video-server
[2025-09-01T02:54:17.999Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True) to: [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)_2025-08-31_21-54-17_001.log
[2025-09-01T02:54:18.000Z] [INFO] [[STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)] video-server
[2025-09-01T02:54:18.711Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False to: [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False_2025-08-31_21-54-18_001.log
[2025-09-01T02:54:18.711Z] [INFO] [[STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False] video-server
[2025-09-01T02:54:19.544Z] [INFO] [RealtimeLogger] Started logging [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet` to: [STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`_2025-08-31_21-54-19_001.log
[2025-09-01T02:54:19.545Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:54:26.455Z] [INFO] [RealtimeLogger] Started logging [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message) to: [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)_2025-08-31_21-54-26_001.log
[2025-09-01T02:54:26.456Z] [INFO] [[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)] video-server
[2025-09-01T02:54:26.457Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-54-26_001.log'
[2025-09-01T02:54:26.457Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-54-26_001.log'
[2025-09-01T02:54:26.457Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--hunyuanvideo-community--HunyuanVideo. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-54-26_001.log'
[2025-09-01T02:54:29.236Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:54:42.402Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:54:47.967Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:55:35.839Z] [INFO] [RealtimeLogger] Started logging [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message) to: [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)_2025-08-31_21-55-35_001.log
[2025-09-01T02:55:35.839Z] [INFO] [[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)] video-server
[2025-09-01T02:55:35.841Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-55-35_001.log'
[2025-09-01T02:55:35.841Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-55-35_001.log'
[2025-09-01T02:55:35.841Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--flux_redux_bfl. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-55-35_001.log'
[2025-09-01T02:55:36.294Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:56:17.678Z] [INFO] [RealtimeLogger] Started logging [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message) to: [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)_2025-08-31_21-56-17_001.log
[2025-09-01T02:56:17.679Z] [INFO] [[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)] video-server
[2025-09-01T02:56:17.681Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-56-17_001.log'
[2025-09-01T02:56:17.681Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-56-17_001.log'
[2025-09-01T02:56:17.681Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_21-56-17_001.log'
[2025-09-01T02:56:18.438Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:56:18.439Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
[2025-09-01T02:56:18.444Z] [INFO] [[STDERR] Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`] video-server
