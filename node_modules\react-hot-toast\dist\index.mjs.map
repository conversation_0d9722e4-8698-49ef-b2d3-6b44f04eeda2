{"version": 3, "sources": ["../src/core/types.ts", "../src/core/utils.ts", "../src/core/store.ts", "../src/core/toast.ts", "../src/core/use-toaster.ts", "../src/components/toast-bar.tsx", "../src/components/toast-icon.tsx", "../src/components/error.tsx", "../src/components/loader.tsx", "../src/components/checkmark.tsx", "../src/components/toaster.tsx", "../src/index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  toasterId?: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'toasterId'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  toasterId?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nexport const TOAST_EXPIRE_DISMISS_DELAY = 1000;\nexport const TOAST_LIMIT = 20;\nexport const DEFAULT_TOASTER_ID = 'default';\n\ninterface ToasterSettings {\n  toastLimit: number;\n}\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\nexport type Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface ToasterState {\n  toasts: Toast[];\n  settings: ToasterSettings;\n  pausedAt: number | undefined;\n}\n\ninterface State {\n  [toasterId: string]: ToasterState;\n}\n\nexport const reducer = (state: ToasterState, action: Action): ToasterState => {\n  const { toastLimit } = state.settings;\n\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, toastLimit),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<\n  [toasterId: string, reducer: (state: ToasterState) => void]\n> = [];\n\nconst defaultToasterState: ToasterState = {\n  toasts: [],\n  pausedAt: undefined,\n  settings: {\n    toastLimit: TOAST_LIMIT,\n  },\n};\nlet memoryState: State = {};\n\nexport const dispatch = (action: Action, toasterId = DEFAULT_TOASTER_ID) => {\n  memoryState[toasterId] = reducer(\n    memoryState[toasterId] || defaultToasterState,\n    action\n  );\n  listeners.forEach(([id, listener]) => {\n    if (id === toasterId) {\n      listener(memoryState[toasterId]);\n    }\n  });\n};\n\nexport const dispatchAll = (action: Action) =>\n  Object.keys(memoryState).forEach((toasterId) => dispatch(action, toasterId));\n\nexport const getToasterIdFromToastId = (toastId: string) =>\n  Object.keys(memoryState).find((toasterId) =>\n    memoryState[toasterId].toasts.some((t) => t.id === toastId)\n  );\n\nexport const createDispatch =\n  (toasterId = DEFAULT_TOASTER_ID) =>\n  (action: Action) => {\n    dispatch(action, toasterId);\n  };\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (\n  toastOptions: DefaultToastOptions = {},\n  toasterId: string = DEFAULT_TOASTER_ID\n): ToasterState => {\n  const [state, setState] = useState<ToasterState>(\n    memoryState[toasterId] || defaultToasterState\n  );\n  const initial = useRef(memoryState[toasterId]);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState[toasterId]) {\n      setState(memoryState[toasterId]);\n    }\n    listeners.push([toasterId, setState]);\n    return () => {\n      const index = listeners.findIndex(([id]) => id === toasterId);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [toasterId]);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport {\n  createDispatch,\n  Action,\n  ActionType,\n  dispatchAll,\n  getToasterIdFromToastId,\n} from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n\n    const dispatch = createDispatch(\n      toast.toasterId || getToasterIdFromToastId(toast.id)\n    );\n\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\n/**\n * Dismisses the toast with the given id. If no id is given, dismisses all toasts.\n * The toast will transition out and then be removed from the DOM.\n * Applies to all toasters, except when a `toasterId` is given.\n */\ntoast.dismiss = (toastId?: string, toasterId?: string) => {\n  const action: Action = {\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  };\n\n  if (toasterId) {\n    createDispatch(toasterId)(action);\n  } else {\n    dispatchAll(action);\n  }\n};\n\n/**\n * Dismisses all toasts.\n */\ntoast.dismissAll = (toasterId?: string) => toast.dismiss(undefined, toasterId);\n\n/**\n * Removes the toast with the given id.\n * The toast will be removed from the DOM without any transition.\n */\ntoast.remove = (toastId?: string, toasterId?: string) => {\n  const action: Action = {\n    type: ActionType.REMOVE_TOAST,\n    toastId,\n  };\n  if (toasterId) {\n    createDispatch(toasterId)(action);\n  } else {\n    dispatchAll(action);\n  }\n};\n\n/**\n * Removes all toasts.\n */\ntoast.removeAll = (toasterId?: string) => toast.remove(undefined, toasterId);\n\n/**\n * Create a loading toast that will automatically updates with the promise.\n */\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback, useRef } from 'react';\nimport { createDispatch, ActionType, useStore, dispatch } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nexport const REMOVE_DELAY = 1000;\n\nexport const useToaster = (\n  toastOptions?: DefaultToastOptions,\n  toasterId: string = 'default'\n) => {\n  const { toasts, pausedAt } = useStore(toastOptions, toasterId);\n  const toastTimeouts = useRef(\n    new Map<Toast['id'], ReturnType<typeof setTimeout>>()\n  ).current;\n\n  const addToRemoveQueue = useCallback(\n    (toastId: string, removeDelay = REMOVE_DELAY) => {\n      if (toastTimeouts.has(toastId)) {\n        return;\n      }\n\n      const timeout = setTimeout(() => {\n        toastTimeouts.delete(toastId);\n        dispatch({\n          type: ActionType.REMOVE_TOAST,\n          toastId: toastId,\n        });\n      }, removeDelay);\n\n      toastTimeouts.set(toastId, timeout);\n    },\n    []\n  );\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id, toasterId), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt, toasterId]);\n\n  const dispatch = useCallback(createDispatch(toasterId), [toasterId]);\n\n  const startPause = useCallback(() => {\n    dispatch({\n      type: ActionType.START_PAUSE,\n      time: Date.now(),\n    });\n  }, [dispatch]);\n\n  const updateHeight = useCallback(\n    (toastId: string, height: number) => {\n      dispatch({\n        type: ActionType.UPDATE_TOAST,\n        toast: { id: toastId, height },\n      });\n    },\n    [dispatch]\n  );\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt, dispatch]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  // Keep track of dismissed toasts and remove them after the delay\n  useEffect(() => {\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts, addToRemoveQueue]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  toasterId,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions, toasterId);\n\n  return (\n    <div\n      data-rht-toaster={toasterId || ''}\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "mappings": ";AAuBA,IAAMA,EACJC,GAEA,OAAOA,GAAkB,WAEdC,EAAe,CAC1BD,EACAE,IACYH,EAAWC,CAAa,EAAIA,EAAcE,CAAG,EAAIF,EC/BxD,IAAMG,GAAS,IAAM,CAC1B,IAAIC,EAAQ,EACZ,MAAO,KACG,EAAEA,GAAO,SAAS,CAE9B,GAAG,EAEUC,GAAwB,IAAM,CAEzC,IAAIC,EAEJ,MAAO,IAAM,CACX,GAAIA,IAAuB,QAAa,OAAO,OAAW,IAAa,CACrE,IAAMC,EAAa,WAAW,kCAAkC,EAChED,EAAqB,CAACC,GAAcA,EAAW,QAEjD,OAAOD,CACT,CACF,GAAG,EClBH,OAAS,aAAAE,GAAW,YAAAC,GAAU,UAAAC,OAAc,QAIrC,IAAMC,GAAc,GACdC,EAAqB,UAwD3B,IAAMC,EAAU,CAACC,EAAqBC,IAAiC,CAC5E,GAAM,CAAE,WAAAC,CAAW,EAAIF,EAAM,SAE7B,OAAQC,EAAO,KAAM,CACnB,IAAK,GACH,MAAO,CACL,GAAGD,EACH,OAAQ,CAACC,EAAO,MAAO,GAAGD,EAAM,MAAM,EAAE,MAAM,EAAGE,CAAU,CAC7D,EAEF,IAAK,GACH,MAAO,CACL,GAAGF,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOF,EAAO,MAAM,GAAK,CAAE,GAAGE,EAAG,GAAGF,EAAO,KAAM,EAAIE,CACzD,CACF,EAEF,IAAK,GACH,GAAM,CAAE,MAAAC,CAAM,EAAIH,EAClB,OAAOF,EAAQC,EAAO,CACpB,KAAMA,EAAM,OAAO,KAAMG,GAAMA,EAAE,KAAOC,EAAM,EAAE,EAC5C,EACA,EACJ,MAAAA,CACF,CAAC,EAEH,IAAK,GACH,GAAM,CAAE,QAAAC,CAAQ,EAAIJ,EAEpB,MAAO,CACL,GAAGD,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOE,GAAWA,IAAY,OAC5B,CACE,GAAGF,EACH,UAAW,GACX,QAAS,EACX,EACAA,CACN,CACF,EACF,IAAK,GACH,OAAIF,EAAO,UAAY,OACd,CACL,GAAGD,EACH,OAAQ,CAAC,CACX,EAEK,CACL,GAAGA,EACH,OAAQA,EAAM,OAAO,OAAQG,GAAMA,EAAE,KAAOF,EAAO,OAAO,CAC5D,EAEF,IAAK,GACH,MAAO,CACL,GAAGD,EACH,SAAUC,EAAO,IACnB,EAEF,IAAK,GACH,IAAMK,EAAOL,EAAO,MAAQD,EAAM,UAAY,GAE9C,MAAO,CACL,GAAGA,EACH,SAAU,OACV,OAAQA,EAAM,OAAO,IAAKG,IAAO,CAC/B,GAAGA,EACH,cAAeA,EAAE,cAAgBG,CACnC,EAAE,CACJ,CACJ,CACF,EAEMC,EAEF,CAAC,EAECC,EAAoC,CACxC,OAAQ,CAAC,EACT,SAAU,OACV,SAAU,CACR,WAAYC,EACd,CACF,EACIC,EAAqB,CAAC,EAEbC,EAAW,CAACV,EAAgBW,EAAYC,IAAuB,CAC1EH,EAAYE,CAAS,EAAIb,EACvBW,EAAYE,CAAS,GAAKJ,EAC1BP,CACF,EACAM,EAAU,QAAQ,CAAC,CAACO,EAAIC,CAAQ,IAAM,CAChCD,IAAOF,GACTG,EAASL,EAAYE,CAAS,CAAC,CAEnC,CAAC,CACH,EAEaI,EAAef,GAC1B,OAAO,KAAKS,CAAW,EAAE,QAASE,GAAcD,EAASV,EAAQW,CAAS,CAAC,EAEhEK,EAA2BZ,GACtC,OAAO,KAAKK,CAAW,EAAE,KAAME,GAC7BF,EAAYE,CAAS,EAAE,OAAO,KAAMT,GAAMA,EAAE,KAAOE,CAAO,CAC5D,EAEWa,EACX,CAACN,EAAYC,IACZZ,GAAmB,CAClBU,EAASV,EAAQW,CAAS,CAC5B,EAEWO,GAET,CACF,MAAO,IACP,MAAO,IACP,QAAS,IACT,QAAS,IACT,OAAQ,GACV,EAEaC,EAAW,CACtBC,EAAoC,CAAC,EACrCT,EAAoBC,IACH,CACjB,GAAM,CAACb,EAAOsB,CAAQ,EAAIC,GACxBb,EAAYE,CAAS,GAAKJ,CAC5B,EACMgB,EAAUC,GAAOf,EAAYE,CAAS,CAAC,EAG7Cc,GAAU,KACJF,EAAQ,UAAYd,EAAYE,CAAS,GAC3CU,EAASZ,EAAYE,CAAS,CAAC,EAEjCL,EAAU,KAAK,CAACK,EAAWU,CAAQ,CAAC,EAC7B,IAAM,CACX,IAAMK,EAAQpB,EAAU,UAAU,CAAC,CAACO,CAAE,IAAMA,IAAOF,CAAS,EACxDe,EAAQ,IACVpB,EAAU,OAAOoB,EAAO,CAAC,CAE7B,GACC,CAACf,CAAS,CAAC,EAEd,IAAMgB,EAAe5B,EAAM,OAAO,IAAKG,GAAG,CA/M5C,IAAA0B,EAAAC,EAAAC,EA+MgD,OAC5C,GAAGV,EACH,GAAGA,EAAalB,EAAE,IAAI,EACtB,GAAGA,EACH,YACEA,EAAE,eACF0B,EAAAR,EAAalB,EAAE,IAAI,IAAnB,YAAA0B,EAAsB,eACtBR,GAAA,YAAAA,EAAc,aAChB,SACElB,EAAE,YACF2B,EAAAT,EAAalB,EAAE,IAAI,IAAnB,YAAA2B,EAAsB,YACtBT,GAAA,YAAAA,EAAc,WACdF,GAAgBhB,EAAE,IAAI,EACxB,MAAO,CACL,GAAGkB,EAAa,MAChB,IAAGU,EAAAV,EAAalB,EAAE,IAAI,IAAnB,YAAA4B,EAAsB,MACzB,GAAG5B,EAAE,KACP,CACF,EAAE,EAEF,MAAO,CACL,GAAGH,EACH,OAAQ4B,CACV,CACF,ECjNA,IAAMI,GAAc,CAClBC,EACAC,EAAkB,QAClBC,KACW,CACX,UAAW,KAAK,IAAI,EACpB,QAAS,GACT,UAAW,GACX,KAAAD,EACA,UAAW,CACT,KAAM,SACN,YAAa,QACf,EACA,QAAAD,EACA,cAAe,EACf,GAAGE,EACH,IAAIA,GAAA,YAAAA,EAAM,KAAMC,EAAM,CACxB,GAEMC,EACHH,GACD,CAACD,EAASK,IAAY,CACpB,IAAMC,EAAQP,GAAYC,EAASC,EAAMI,CAAO,EAMhD,OAJiBE,EACfD,EAAM,WAAaE,EAAwBF,EAAM,EAAE,CACrD,EAES,CAAE,OAA+B,MAAAA,CAAM,CAAC,EAC1CA,EAAM,EACf,EAEIA,EAAQ,CAACN,EAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,EAASE,CAAI,EAEtCI,EAAM,MAAQF,EAAc,OAAO,EACnCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,OAASF,EAAc,QAAQ,EAOrCE,EAAM,QAAU,CAACG,EAAkBC,IAAuB,CACxD,IAAMC,EAAiB,CACrB,OACA,QAAAF,CACF,EAEIC,EACFH,EAAeG,CAAS,EAAEC,CAAM,EAEhCC,EAAYD,CAAM,CAEtB,EAKAL,EAAM,WAAcI,GAAuBJ,EAAM,QAAQ,OAAWI,CAAS,EAM7EJ,EAAM,OAAS,CAACG,EAAkBC,IAAuB,CACvD,IAAMC,EAAiB,CACrB,OACA,QAAAF,CACF,EACIC,EACFH,EAAeG,CAAS,EAAEC,CAAM,EAEhCC,EAAYD,CAAM,CAEtB,EAKAL,EAAM,UAAaI,GAAuBJ,EAAM,OAAO,OAAWI,CAAS,EAK3EJ,EAAM,QAAU,CACdO,EACAC,EAKAZ,IACG,CACH,IAAMa,EAAKT,EAAM,QAAQQ,EAAK,QAAS,CAAE,GAAGZ,EAAM,GAAGA,GAAA,YAAAA,EAAM,OAAQ,CAAC,EAEpE,OAAI,OAAOW,GAAY,aACrBA,EAAUA,EAAQ,GAGpBA,EACG,KAAMG,GAAM,CACX,IAAMC,EAAiBH,EAAK,QACxBI,EAAaJ,EAAK,QAASE,CAAC,EAC5B,OAEJ,OAAIC,EACFX,EAAM,QAAQW,EAAgB,CAC5B,GAAAF,EACA,GAAGb,EACH,GAAGA,GAAA,YAAAA,EAAM,OACX,CAAC,EAEDI,EAAM,QAAQS,CAAE,EAEXC,CACT,CAAC,EACA,MAAOG,GAAM,CACZ,IAAMC,EAAeN,EAAK,MAAQI,EAAaJ,EAAK,MAAOK,CAAC,EAAI,OAE5DC,EACFd,EAAM,MAAMc,EAAc,CACxB,GAAAL,EACA,GAAGb,EACH,GAAGA,GAAA,YAAAA,EAAM,KACX,CAAC,EAEDI,EAAM,QAAQS,CAAE,CAEpB,CAAC,EAEIF,CACT,EC5JA,OAAS,aAAAQ,EAAW,eAAAC,EAAa,UAAAC,OAAc,QAKxC,IAAMC,GAAe,IAEfC,EAAa,CACxBC,EACAC,EAAoB,YACjB,CACH,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAASJ,EAAcC,CAAS,EACvDI,EAAgBC,GACpB,IAAI,GACN,EAAE,QAEIC,EAAmBC,EACvB,CAACC,EAAiBC,EAAcZ,KAAiB,CAC/C,GAAIO,EAAc,IAAII,CAAO,EAC3B,OAGF,IAAME,EAAU,WAAW,IAAM,CAC/BN,EAAc,OAAOI,CAAO,EAC5BG,EAAS,CACP,OACA,QAASH,CACX,CAAC,CACH,EAAGC,CAAW,EAEdL,EAAc,IAAII,EAASE,CAAO,CACpC,EACA,CAAC,CACH,EAEAE,EAAU,IAAM,CACd,GAAIV,EACF,OAGF,IAAMW,EAAM,KAAK,IAAI,EACfC,EAAWb,EAAO,IAAKc,GAAM,CACjC,GAAIA,EAAE,WAAa,IACjB,OAGF,IAAMC,GACHD,EAAE,UAAY,GAAKA,EAAE,eAAiBF,EAAME,EAAE,WAEjD,GAAIC,EAAe,EAAG,CAChBD,EAAE,SACJE,EAAM,QAAQF,EAAE,EAAE,EAEpB,OAEF,OAAO,WAAW,IAAME,EAAM,QAAQF,EAAE,GAAIf,CAAS,EAAGgB,CAAY,CACtE,CAAC,EAED,MAAO,IAAM,CACXF,EAAS,QAASJ,GAAYA,GAAW,aAAaA,CAAO,CAAC,CAChE,CACF,EAAG,CAACT,EAAQC,EAAUF,CAAS,CAAC,EAEhC,IAAMW,EAAWJ,EAAYW,EAAelB,CAAS,EAAG,CAACA,CAAS,CAAC,EAE7DmB,EAAaZ,EAAY,IAAM,CACnCI,EAAS,CACP,OACA,KAAM,KAAK,IAAI,CACjB,CAAC,CACH,EAAG,CAACA,CAAQ,CAAC,EAEPS,EAAeb,EACnB,CAACC,EAAiBa,IAAmB,CACnCV,EAAS,CACP,OACA,MAAO,CAAE,GAAIH,EAAS,OAAAa,CAAO,CAC/B,CAAC,CACH,EACA,CAACV,CAAQ,CACX,EAEMW,EAAWf,EAAY,IAAM,CAC7BL,GACFS,EAAS,CAAE,OAA4B,KAAM,KAAK,IAAI,CAAE,CAAC,CAE7D,EAAG,CAACT,EAAUS,CAAQ,CAAC,EAEjBY,EAAkBhB,EACtB,CACEU,EACAO,IAKG,CACH,GAAM,CAAE,aAAAC,EAAe,GAAO,OAAAC,EAAS,EAAG,gBAAAC,CAAgB,EAAIH,GAAQ,CAAC,EAEjEI,EAAiB3B,EAAO,OAC3Bc,IACEA,EAAE,UAAYY,MACZV,EAAM,UAAYU,IAAoBZ,EAAE,MAC/C,EACMc,EAAaD,EAAe,UAAWb,GAAMA,EAAE,KAAOE,EAAM,EAAE,EAC9Da,EAAeF,EAAe,OAClC,CAACX,EAAOc,IAAMA,EAAIF,GAAcZ,EAAM,OACxC,EAAE,OAOF,OALeW,EACZ,OAAQb,GAAMA,EAAE,OAAO,EACvB,MAAM,GAAIU,EAAe,CAACK,EAAe,CAAC,EAAI,CAAC,EAAGA,CAAY,CAAE,EAChE,OAAO,CAACE,EAAKjB,IAAMiB,GAAOjB,EAAE,QAAU,GAAKW,EAAQ,CAAC,CAGzD,EACA,CAACzB,CAAM,CACT,EAGA,OAAAW,EAAU,IAAM,CACdX,EAAO,QAASgB,GAAU,CACxB,GAAIA,EAAM,UACRX,EAAiBW,EAAM,GAAIA,EAAM,WAAW,MACvC,CAEL,IAAMP,EAAUN,EAAc,IAAIa,EAAM,EAAE,EACtCP,IACF,aAAaA,CAAO,EACpBN,EAAc,OAAOa,EAAM,EAAE,GAGnC,CAAC,CACH,EAAG,CAAChB,EAAQK,CAAgB,CAAC,EAEtB,CACL,OAAAL,EACA,SAAU,CACR,aAAAmB,EACA,WAAAD,EACA,SAAAG,EACA,gBAAAC,CACF,CACF,CACF,EChJA,UAAYU,MAAW,QACvB,OAAS,UAAAC,EAAQ,aAAAC,MAAiB,SCDlC,UAAYC,MAAW,QACvB,OAAS,UAAAC,EAAQ,aAAAC,OAAiB,SCDlC,OAAS,UAAAC,GAAQ,aAAAC,MAAiB,SAElC,IAAMC,GAAkBD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUlBE,GAAqBF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUrBG,GAAsBH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAefI,EAAYL,GAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKpBM,GAAMA,EAAE,SAAW;AAAA;AAAA;AAAA;AAAA,eAIrBJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAOEC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKEG,GAAMA,EAAE,WAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAQvBF;AAAA;AAAA;AAAA;EClEjB,OAAS,UAAAG,GAAQ,aAAAC,OAAiB,SAElC,IAAMC,GAASD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcFE,EAAaH,GAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMnBI,GAAMA,EAAE,WAAa;AAAA,wBACfA,GAAMA,EAAE,SAAW;AAAA,eAC7BF;ECxBf,OAAS,UAAAG,GAAQ,aAAAC,MAAiB,SAElC,IAAMC,GAAkBD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUlBE,GAAqBF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAqBdG,EAAgBJ,GAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKxBK,GAAMA,EAAE,SAAW;AAAA;AAAA;AAAA;AAAA,eAIrBH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAMEC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAMIE,GAAMA,EAAE,WAAa;AAAA;AAAA;AAAA;AAAA;AAAA;EH9C1C,IAAMC,GAAgBC,EAAO,KAAK;AAAA;AAAA,EAI5BC,GAAmBD,EAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS/BE,GAAQC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUDC,GAAsBJ,EAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,eAKhCE;AAAA;AAAA,EAUFG,EAER,CAAC,CAAE,MAAAC,CAAM,IAAM,CAClB,GAAM,CAAE,KAAAC,EAAM,KAAAC,EAAM,UAAAC,CAAU,EAAIH,EAClC,OAAIC,IAAS,OACP,OAAOA,GAAS,SACX,gBAACH,GAAA,KAAqBG,CAAK,EAE3BA,EAIPC,IAAS,QACJ,KAIP,gBAACP,GAAA,KACC,gBAACS,EAAA,CAAY,GAAGD,EAAW,EAC1BD,IAAS,WACR,gBAACT,GAAA,KACES,IAAS,QACR,gBAACG,EAAA,CAAW,GAAGF,EAAW,EAE1B,gBAACG,EAAA,CAAe,GAAGH,EAAW,CAElC,CAEJ,CAEJ,EDrEA,IAAMI,GAAkBC,GAAmB;AAAA,+BACZA,EAAS;AAAA;AAAA,EAIlCC,GAAiBD,GAAmB;AAAA;AAAA,iCAETA,EAAS;AAAA,EAGpCE,GAAkB,kCAClBC,GAAmB,kCAEnBC,GAAeC,EAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc3BC,GAAUD,EAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBtBE,GAAoB,CACxBC,EACAC,IACwB,CAExB,IAAMT,EADMQ,EAAS,SAAS,KAAK,EACd,EAAI,GAEnB,CAACE,EAAOC,CAAI,EAAIC,EAAqB,EACvC,CAACV,GAAiBC,EAAgB,EAClC,CAACJ,GAAeC,CAAM,EAAGC,GAAcD,CAAM,CAAC,EAElD,MAAO,CACL,UAAWS,EACP,GAAGI,EAAUH,CAAK,gDAClB,GAAGG,EAAUF,CAAI,6CACvB,CACF,EAEaG,EAA0C,OACrD,CAAC,CAAE,MAAAC,EAAO,SAAAP,EAAU,MAAAQ,EAAO,SAAAC,CAAS,IAAM,CACxC,IAAMC,EAAsCH,EAAM,OAC9CR,GACEQ,EAAM,UAAYP,GAAY,aAC9BO,EAAM,OACR,EACA,CAAE,QAAS,CAAE,EAEXI,EAAO,gBAACC,EAAA,CAAU,MAAOL,EAAO,EAChCM,EACJ,gBAACf,GAAA,CAAS,GAAGS,EAAM,WAChBO,EAAaP,EAAM,QAASA,CAAK,CACpC,EAGF,OACE,gBAACX,GAAA,CACC,UAAWW,EAAM,UACjB,MAAO,CACL,GAAGG,EACH,GAAGF,EACH,GAAGD,EAAM,KACX,GAEC,OAAOE,GAAa,WACnBA,EAAS,CACP,KAAAE,EACA,QAAAE,CACF,CAAC,EAED,gCACGF,EACAE,CACH,CAEJ,CAEJ,CACF,EK9GA,OAAS,OAAAE,GAAK,SAAAC,OAAa,SAC3B,UAAYC,MAAW,QAWvBC,GAAY,eAAa,EAEzB,IAAMC,GAAe,CAAC,CACpB,GAAAC,EACA,UAAAC,EACA,MAAAC,EACA,eAAAC,EACA,SAAAC,CACF,IAAyB,CACvB,IAAMC,EAAY,cACfC,GAA2B,CAC1B,GAAIA,EAAI,CACN,IAAMC,EAAe,IAAM,CACzB,IAAMC,EAASF,EAAG,sBAAsB,EAAE,OAC1CH,EAAeH,EAAIQ,CAAM,CAC3B,EACAD,EAAa,EACb,IAAI,iBAAiBA,CAAY,EAAE,QAAQD,EAAI,CAC7C,QAAS,GACT,UAAW,GACX,cAAe,EACjB,CAAC,EAEL,EACA,CAACN,EAAIG,CAAc,CACrB,EAEA,OACE,gBAAC,OAAI,IAAKE,EAAK,UAAWJ,EAAW,MAAOC,GACzCE,CACH,CAEJ,EAEMK,GAAmB,CACvBC,EACAC,IACwB,CACxB,IAAMC,EAAMF,EAAS,SAAS,KAAK,EAC7BG,EAAqCD,EAAM,CAAE,IAAK,CAAE,EAAI,CAAE,OAAQ,CAAE,EACpEE,EAAuCJ,EAAS,SAAS,QAAQ,EACnE,CACE,eAAgB,QAClB,EACAA,EAAS,SAAS,OAAO,EACzB,CACE,eAAgB,UAClB,EACA,CAAC,EACL,MAAO,CACL,KAAM,EACN,MAAO,EACP,QAAS,OACT,SAAU,WACV,WAAYK,EAAqB,EAC7B,OACA,yCACJ,UAAW,cAAcJ,GAAUC,EAAM,EAAI,SAC7C,GAAGC,EACH,GAAGC,CACL,CACF,EAEME,GAAcC;AAAA;AAAA;AAAA;AAAA;AAAA,EAOdC,EAAiB,GAEVC,GAAkC,CAAC,CAC9C,aAAAC,EACA,SAAAV,EAAW,aACX,aAAAW,EACA,OAAAC,EACA,SAAAlB,EACA,UAAAmB,EACA,eAAAC,EACA,mBAAAC,CACF,IAAM,CACJ,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAAWP,EAAcE,CAAS,EAE/D,OACE,gBAAC,OACC,mBAAkBA,GAAa,GAC/B,MAAO,CACL,SAAU,QACV,OAAQ,KACR,IAAKL,EACL,KAAMA,EACN,MAAOA,EACP,OAAQA,EACR,cAAe,OACf,GAAGM,CACL,EACA,UAAWC,EACX,aAAcE,EAAS,WACvB,aAAcA,EAAS,UAEtBD,EAAO,IAAKG,GAAM,CACjB,IAAMC,EAAgBD,EAAE,UAAYnB,EAC9BC,EAASgB,EAAS,gBAAgBE,EAAG,CACzC,aAAAT,EACA,OAAAE,EACA,gBAAiBZ,CACnB,CAAC,EACKqB,EAAgBtB,GAAiBqB,EAAenB,CAAM,EAE5D,OACE,gBAACZ,GAAA,CACC,GAAI8B,EAAE,GACN,IAAKA,EAAE,GACP,eAAgBF,EAAS,aACzB,UAAWE,EAAE,QAAUb,GAAc,GACrC,MAAOe,GAENF,EAAE,OAAS,SACVG,EAAaH,EAAE,QAASA,CAAC,EACvBzB,EACFA,EAASyB,CAAC,EAEV,gBAACI,EAAA,CAAS,MAAOJ,EAAG,SAAUC,EAAe,CAEjD,CAEJ,CAAC,CACH,CAEJ,EClIA,IAAOI,GAAQC", "names": ["isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "useEffect", "useState", "useRef", "TOAST_LIMIT", "DEFAULT_TOASTER_ID", "reducer", "state", "action", "toastLimit", "t", "toast", "toastId", "diff", "listeners", "defaultToasterState", "TOAST_LIMIT", "memoryState", "dispatch", "toasterId", "DEFAULT_TOASTER_ID", "id", "listener", "dispatchAll", "getToasterIdFromToastId", "createDispatch", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "createDispatch", "getToasterIdFromToastId", "toastId", "toasterId", "action", "dispatchAll", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "useEffect", "useCallback", "useRef", "REMOVE_DELAY", "useToaster", "toastOptions", "toasterId", "toasts", "pausedAt", "useStore", "toastTimeouts", "useRef", "addToRemoveQueue", "useCallback", "toastId", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "dispatch", "useEffect", "now", "timeouts", "t", "durationLeft", "toast", "createDispatch", "startPause", "updateHeight", "height", "endPause", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "styled", "keyframes", "React", "styled", "keyframes", "styled", "keyframes", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "styled", "keyframes", "rotate", "LoaderIcon", "p", "styled", "keyframes", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "styled", "IndicatorWrapper", "enter", "keyframes", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "styled", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "keyframes", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "css", "setup", "React", "setup", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "toasterId", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"]}