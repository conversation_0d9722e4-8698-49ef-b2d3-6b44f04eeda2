========================================
AIStudio Real-time Log: main
Started: 2025-09-03T02:59:49.030Z
File: app_main_2025-09-02_21-59-49_001.log
========================================

[2025-09-03T02:59:49.186Z] [ERROR] App threw an error during load
[2025-09-03T02:59:49.188Z] [ERROR] Error: Attempted to register a second handler for 'resolve-video-path'
    at IpcMainImpl.handle (node:electron/js2c/browser_init:2:101132)
    at Object.<anonymous> (N:\AIStudio\src\main\index.js:1706:9)
    at Module._compile (node:internal/modules/cjs/loader:1484:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1564:10)
    at Module.load (node:internal/modules/cjs/loader:1295:32)
    at Module._load (node:internal/modules/cjs/loader:1111:12)
    at c._load (node:electron/js2c/node_init:2:16955)
    at cjsLoader (node:internal/modules/esm/translators:350:17)
    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:286:7)
    at ModuleJob.run (node:internal/modules/esm/module_job:234:25)
[2025-09-03T02:59:49.188Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T02:59:49.188Z] [ERROR] [main] ERROR: Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T02:59:49.189Z] [ERROR] Uncaught Exception: Error: Attempted to register a second handler for 'resolve-video-path'
[2025-09-03T02:59:49.274Z] [INFO] AIStudio application started successfully
[2025-09-03T02:59:49.274Z] [INFO] [main] AIStudio application started successfully
[2025-09-03T02:59:49.308Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-03T02:59:50.297Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-03T03:00:11.812Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-02_22-00-11_001.log
[2025-09-03T03:00:11.813Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-03T03:00:38.195Z] [INFO] [RealtimeLogger] Started logging videogeneration to: process_videogeneration_2025-09-02_22-00-38_001.log
[2025-09-03T03:00:40.771Z] [INFO] [RealtimeLogger] Closed log stream: videogeneration
