========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:26:14.983Z
File: app_main_2025-08-31_20-26-14_001.log
========================================

[2025-09-01T01:26:15.270Z] [INFO] AIStudio application started successfully
[2025-09-01T01:26:15.271Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:26:15.302Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:26:16.262Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:26:34.040Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-26-34_001.log
[2025-09-01T01:26:34.040Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:28:49.739Z] [ERROR] Framepack video generation failed: Error: spawn C:\WINDOWS\system32\cmd.exe ENOENT
