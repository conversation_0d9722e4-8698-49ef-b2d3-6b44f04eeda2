========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:48:08.211Z
File: app_main_2025-08-29_23-48-08_001.log
========================================

[2025-08-30T04:48:08.477Z] [INFO] AIStudio application started successfully
[2025-08-30T04:48:08.478Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:48:08.514Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:48:09.447Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T04:48:27.292Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-48-27_001.log
[2025-08-30T04:48:27.292Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:48:29.682Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:48:29.683Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:48:30.690Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
