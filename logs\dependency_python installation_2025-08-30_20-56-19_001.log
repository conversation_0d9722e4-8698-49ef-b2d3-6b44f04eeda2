========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-08-31T01:56:19.157Z
File: dependency_python installation_2025-08-30_20-56-19_001.log
========================================

[2025-08-31T01:56:19.158Z] [INFO] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:56:19.158Z] [INFO] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:56:19.159Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:56:19.159Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:56:19.160Z] [INFO] DependencyManager: About to check routing for Video Generation with component python
