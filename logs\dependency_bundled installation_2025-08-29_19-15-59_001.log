========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-08-30T00:15:59.547Z
File: dependency_bundled installation_2025-08-29_19-15-59_001.log
========================================

[2025-08-30T00:15:59.547Z] [INFO] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:15:59.548Z] [INFO] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:15:59.548Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:15:59.549Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:15:59.549Z] [INFO] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:15:59.550Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:15:59.559Z] [INFO] DependencyManager: Existing directory removed successfully
[2025-08-30T00:15:59.560Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:15:59.561Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
[2025-08-30T00:15:59.733Z] [ERROR] DependencyManager: Failed to extract zip file: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:15:59.733Z] [ERROR] DependencyManager: Error during installation: Failed to extract ImageGeneration module: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:15:59.734Z] [ERROR] DependencyManager: Failed bundled installation for ImageGeneration

========================================
Log ended: 2025-08-30T00:16:00.766Z
========================================
