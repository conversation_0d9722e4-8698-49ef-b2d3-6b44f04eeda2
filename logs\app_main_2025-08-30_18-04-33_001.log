========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:04:33.258Z
File: app_main_2025-08-30_18-04-33_001.log
========================================

[2025-08-30T23:04:33.477Z] [INFO] AIStudio application started successfully
[2025-08-30T23:04:33.478Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T23:04:33.514Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T23:04:34.385Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T23:04:51.353Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_18-04-51_001.log
[2025-08-30T23:04:51.353Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
