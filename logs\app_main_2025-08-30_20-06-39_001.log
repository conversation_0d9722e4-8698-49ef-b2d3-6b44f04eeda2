========================================
AIStudio Real-time Log: main
Started: 2025-08-31T01:06:39.929Z
File: app_main_2025-08-30_20-06-39_001.log
========================================

[2025-08-31T01:06:40.231Z] [INFO] AIStudio application started successfully
[2025-08-31T01:06:40.231Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T01:06:40.258Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T01:06:41.232Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T01:06:54.317Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_20-06-54_001.log
[2025-08-31T01:06:54.317Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
