import React, { useState, useEffect } from 'react';
import { Image as ImageIcon, Download, Trash2, Play, FolderOpen, ChevronRight, Plus, X, ChevronDown } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import VideoGenerationProgressBar from './VideoGenerationProgressBar';

interface VideoGenerationProps {
  isDarkMode: boolean;
  onSaveAsProject: (videoData: string) => void;
  onProjectSaved?: () => void;
}

interface VideoGenPreset {
  name: string;
  settings: {
    steps: number;
    guidanceScale: number;
    width: number;
    height: number;
    frames: number;
    fps: number;
  };
}

interface VideoCollection {
  name: string;
  videos: VideoItem[];
}

interface VideoItem {
  filename: string;
  url: string;
}

const VIDEO_PRESETS: VideoGenPreset[] = [
  {
    name: "Fast",
    settings: { steps: 20, guidanceScale: 2.5, width: 640, height: 608, frames: 24, fps: 30 }
  },
  {
    name: "Balanced",
    settings: { steps: 25, guidanceScale: 3.0, width: 640, height: 608, frames: 30, fps: 30 }
  },
  {
    name: "High Quality",
    settings: { steps: 30, guidanceScale: 3.5, width: 640, height: 608, frames: 36, fps: 30 }
  }
];

const ACTIVE_VIDEO_COLLECTION_KEY = 'activeVideoCollection';

const VideoGeneration: React.FC<VideoGenerationProps> = ({ isDarkMode }) => {
  const [selectedPreset, setSelectedPreset] = useState(VIDEO_PRESETS[0]);

  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [inputImageUrl, setInputImageUrl] = useState<string | null>(null);
  const [inputImageFile, setInputImageFile] = useState<File | null>(null);
  const [generatedVideoUrl, setGeneratedVideoUrl] = useState<string | null>(null);
  const [generationProgress, setGenerationProgress] = useState<string>('');

  // Progress bar state (reuse image progress component)
  const [currentStage, setCurrentStage] = useState<string>('initializing');
  const [progress, setProgress] = useState<number>(0);
  const [progressText, setProgressText] = useState<string>('');
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [totalSteps, setTotalSteps] = useState<number>(0);
  const [stageProgress, setStageProgress] = useState<number>(0);

  // Video settings
  const [duration, setDuration] = useState(5); // Default 5 seconds (proven working value)
  const [fps, setFps] = useState(30); // Default 30 FPS (Framepack default)

  // Advanced settings
  const [seed, setSeed] = useState(31337); // Use proven working seed from successful generation
  const [negativePrompt, setNegativePrompt] = useState('');

  // Real-time preview states
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [intermediateVideoPath, setIntermediateVideoPath] = useState<string | null>(null);
  const [showLatentPreview, setShowLatentPreview] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Video Gallery State
  const [collections, setCollections] = useState<VideoCollection[]>([]);
  const [activeCollection, setActiveCollection] = useState<string>('Default');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    videoIndex: number;
    video: VideoItem;
  } | null>(null);

  // Load collections from file system on mount
  useEffect(() => {
    const loadCollections = async () => {
      try {
        const storedActive = localStorage.getItem(ACTIVE_VIDEO_COLLECTION_KEY);
        const loadedCollections = await (window as any).electronAPI.getVideoCollections();

        if (loadedCollections && loadedCollections.length > 0) {
          setCollections(loadedCollections);
          setActiveCollection(storedActive && loadedCollections.find(c => c.name === storedActive) ? storedActive : loadedCollections[0].name);
        } else {
          const defaultCollections = [{ name: 'Default', videos: [] }];
          setCollections(defaultCollections);
          setActiveCollection('Default');
        }
      } catch (error) {
        console.error('Error loading video collections:', error);
        const defaultCollections = [{ name: 'Default', videos: [] }];
        setCollections(defaultCollections);
        setActiveCollection('Default');
      }
    };

    loadCollections();
  }, []);

  // Persist collections to file system and active collection to localStorage
  useEffect(() => {
    const saveCollections = async () => {
      try {
        await (window as any).electronAPI.saveVideoCollections(collections);
        console.log('🎬 Video collections saved to file system');
      } catch (e) {
        console.error('🎬 Error saving video collections to file system:', e);
      }
    };

    if (collections.length > 0) {
      saveCollections();
    }
  }, [collections]);

  useEffect(() => {
    if (activeCollection) {
      localStorage.setItem(ACTIVE_VIDEO_COLLECTION_KEY, activeCollection);
    }
  }, [activeCollection]);

  const { getRootProps, getInputProps } = useDropzone({
    accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.bmp', '.webp'] },
    onDrop: (files) => {
      if (files[0]) {
        setInputImageFile(files[0]);
        setInputImageUrl(URL.createObjectURL(files[0]));
        setGeneratedVideoUrl(null); // Clear previous generation
      }
    },
    maxFiles: 1
  });

  // Add video to active collection
  const addVideoToActiveCollection = async (videoPath: string) => {
    try {
      console.log('🎬 Adding video to collection:', activeCollection);

      // Save video to gallery via IPC
      const result = await (window as any).electronAPI.addVideoToGallery({
        videoPath: videoPath,
        collectionName: activeCollection
      });

      if (result.success) {
        // Update local state with the new video
        setCollections(prev => {
          const updated = prev.map(c => c.name === activeCollection ? {
            ...c,
            videos: [{ filename: result.filename, url: result.url }, ...c.videos]
          } : c);
          console.log('🎬 Video collections updated successfully');
          return updated;
        });
      } else {
        throw new Error(result.error || 'Failed to add video to gallery');
      }
    } catch (error) {
      console.error('🎬 Error adding video to collection:', error);
      alert(`Failed to add video to gallery: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Create a new collection
  const handleCreateCollection = () => {
    setShowCreateModal(true);
    setNewCollectionName('');
  };

  const confirmCreateCollection = () => {
    const name = newCollectionName.trim();
    if (!name) return;
    if (collections.some(c => c.name === name)) {
      alert('A collection with that name already exists.');
      return;
    }
    setCollections(prev => [...prev, { name, videos: [] }]);
    setActiveCollection(name);
    setShowCreateModal(false);
    setNewCollectionName('');
  };

  const cancelCreateCollection = () => {
    setShowCreateModal(false);
    setNewCollectionName('');
  };

  const handleGenerate = async () => {
    if (!prompt.trim() || !inputImageFile) {
      alert('Please provide both an image and a prompt to generate a video.');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress('Preparing video generation...');
    setGeneratedVideoUrl(null);
    // Initialize progress
    setCurrentStage('initializing');
    setProgress(5);
    setStageProgress(10);
    setProgressText('Setting up video generation parameters...');

    // Calculate total frames based on duration and FPS
    const totalFrames = duration * fps;
    setCurrentStep(0);
    setTotalSteps(totalFrames);

    try {
      // Save the uploaded image file without background removal
      const buffer = await inputImageFile.arrayBuffer();
      const savedImageResult = await (window as any).electronAPI.uploadFileNoBgRemoval({
        buffer: new Uint8Array(buffer),
        filename: inputImageFile.name
      });

      if (!savedImageResult || !savedImageResult.success) {
        throw new Error('Failed to save input image');
      }

      // Prepare generation settings using user inputs and preset
      const settings = {
        // Core generation parameters
        steps: selectedPreset.settings.steps,
        cfg: selectedPreset.settings.guidanceScale,
        duration: duration, // Use user's duration setting

        // Advanced Framepack parameters (based on successful generation)
        latent_window_size: 16, // Standard window size that worked
        gs: 10.0, // Distilled guidance scale (matches successful generation setting)
        rs: 0.0, // Guidance rescale
        gpu_memory_preservation: 6.0, // Proven working value from successful generation
        use_teacache: true, // Re-enabled as it worked in successful generation
        mp4_crf: 16, // Proven working compression value

        // User-controlled parameters
        negative_prompt: negativePrompt,
        seed: seed,
        device: 'cuda', // Use GPU by default
        high_vram: false // Keep low VRAM mode as it worked successfully
      };

      setGenerationProgress('Processing input image...');
      setCurrentStage('preprocessing');
      setProgress(15);
      setStageProgress(30);
      setProgressText('Analyzing input image and preparing for video generation...');

      // Generate output path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const outputPath = `outputs/videos/framepack_${timestamp}.mp4`;

      // Add loading model stage
      setGenerationProgress('Loading video generation model...');
      setCurrentStage('loading_model');
      setProgress(30);
      setStageProgress(50);
      setProgressText('Loading AI video generation model and pipeline...');

      // Simulate model loading delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Call FramePack generation method
      setGenerationProgress('Starting FramePack generation...');
      setCurrentStage('initializing');
      setProgress(0);
      setStageProgress(0);
      setCurrentStep(0);
      setTotalSteps(settings.steps);
      setProgressText('Initializing video generation...');

      // Clear previous preview states
      setPreviewImage(null);
      setIntermediateVideoPath(null);
      setShowLatentPreview(true); // Auto-expand during generation

      // Set up progress listener
      const progressListener = (progressData: any) => {
        const percentage = progressData.percentage || 0;
        const message = progressData.message || 'Generating...';

        setProgress(percentage);
        setProgressText(message);
        setStageProgress(percentage);

        // Update stage based on progress percentage
        if (percentage < 10) {
          setCurrentStage('initializing');
        } else if (percentage < 20) {
          setCurrentStage('loading');
        } else if (percentage < 40) {
          setCurrentStage('preprocessing');
        } else if (percentage < 90) {
          setCurrentStage('generating');
          // Update steps during generation phase (40-90% = actual generation)
          const generationProgress = (percentage - 40) / 50; // Normalize to 0-1
          const estimatedStep = Math.floor(generationProgress * settings.steps);
          setCurrentStep(estimatedStep);
          setTotalSteps(settings.steps);
        } else {
          setCurrentStage('finalizing');
        }

        // Handle preview images and intermediate videos
        if (progressData.preview) {
          setPreviewImage(progressData.preview);
        }

        if (progressData.video_path) {
          // Convert relative path to absolute URL for video display
          const resolveVideoPath = async () => {
            try {
              const videoUrl = await (window as any).electronAPI.resolveVideoPath(progressData.video_path);
              if (videoUrl) {
                setIntermediateVideoPath(videoUrl);
              }
            } catch (error) {
              console.error('Failed to resolve video path:', error);
            }
          };
          resolveVideoPath();
        }

        // Update step information if available
        if (progressData.step && progressData.total_steps) {
          setCurrentStep(progressData.step);
          setTotalSteps(progressData.total_steps);
        }
      };

      // Add the progress listener
      window.electronAPI.ipcRenderer.on('framepack-video-progress', progressListener);

      let result;
      try {
        result = await (window as any).electronAPI.generateFramepackVideo({
          imagePath: savedImageResult.path,
          prompt: prompt,
          outputPath: outputPath,
          settings: settings
        });
      } finally {
        // Remove the progress listener
        window.electronAPI.ipcRenderer.removeListener('framepack-video-progress', progressListener);

        // Ensure we show completion
        const totalFrames = duration * fps;
        setCurrentStep(totalFrames);
        setStageProgress(100);
      }

      if (result.success) {
        setGenerationProgress('Video generation completed!');
        setCurrentStage('saving');
        setProgress(100);
        setStageProgress(100);
        setProgressText('Video generation complete!');

        // Get the video path from the Framepack result
        let videoPath = result.videoPath;

        if (videoPath) {
          // Convert relative path to absolute path using IPC
          const resolveVideoPath = async (relativePath: string) => {
            try {
              // Use the same mechanism as load-file to resolve paths
              const absolutePath = await (window as any).electronAPI.invoke('resolve-video-path', relativePath);
              return absolutePath;
            } catch (error) {
              console.error('Error resolving video path:', error);
              return null;
            }
          };

          resolveVideoPath(videoPath).then((absolutePath) => {
            if (absolutePath) {
              console.log('🎯 Original path:', videoPath);
              console.log('🎯 Resolved absolute path:', absolutePath);
              console.log('🎯 Setting video URL in state...');
              setGeneratedVideoUrl(absolutePath);

              // Additional debugging - check if file exists by trying to fetch it
              setTimeout(() => {
                console.log('🔍 Testing video URL accessibility...');
                const testVideo = document.createElement('video');
                testVideo.preload = 'metadata';
                testVideo.onloadedmetadata = () => {
                  console.log('✅ Test video loaded successfully!');
                  console.log('📏 Test video dimensions:', testVideo.videoWidth, 'x', testVideo.videoHeight);
                  console.log('⏱️ Test video duration:', testVideo.duration);
                };
                testVideo.onerror = (e) => {
                  console.error('❌ Test video failed to load:', e);
                  console.log('🔍 Trying alternative URL format...');

                  // Try without file:// prefix
                  const altUrl = absolutePath.replace('file://', '');
                  console.log('🔄 Alternative URL:', altUrl);

                  const testVideo2 = document.createElement('video');
                  testVideo2.src = altUrl;
                  testVideo2.onloadedmetadata = () => {
                    console.log('✅ Alternative URL works! Using:', altUrl);
                    setGeneratedVideoUrl(altUrl);
                  };
                  testVideo2.onerror = () => {
                    console.error('❌ Alternative URL also failed');
                  };
                };
                testVideo.src = absolutePath;
              }, 100);
            } else {
              console.error('❌ Failed to resolve video path:', videoPath);
            }
          });
        }

        // Add video to gallery
        if (videoPath) {
          await addVideoToActiveCollection(videoPath);
        }
      } else {
        throw new Error(result.error || 'Video generation failed');
      }

    } catch (error) {
      console.error('Video generation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setGenerationProgress(`Error: ${errorMessage}`);
      alert(`Video generation failed: ${errorMessage}`);
    } finally {
      setIsGenerating(false);
      setProgress(100);
      setStageProgress(100);
      setProgressText('Done');
    }
  };

  // Context menu handlers
  const handleContextMenu = (e: React.MouseEvent, videoIndex: number, video: VideoItem) => {
    e.preventDefault();

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Estimated context menu dimensions
    const menuWidth = 192; // min-w-48 = 12rem = 192px
    const menuHeight = 200; // Approximate height with all items

    // Calculate position, adjusting if it would go off-screen
    let x = e.clientX;
    let y = e.clientY;

    // Adjust horizontal position if menu would go off right edge
    if (x + menuWidth > viewportWidth) {
      x = e.clientX - menuWidth;
    }

    // Adjust vertical position if menu would go off bottom edge
    if (y + menuHeight > viewportHeight) {
      y = e.clientY - menuHeight;
    }

    setContextMenu({
      show: true,
      x,
      y,
      videoIndex,
      video
    });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  const handleDownloadVideo = async (video: VideoItem) => {
    try {
      // Extract the actual file path from the file:// URL
      const filePath = video.url.replace('file://', '');
      await (window as any).electronAPI.downloadFile(filePath);
    } catch (error) {
      console.error('Error downloading video:', error);
      alert('Failed to download video');
    }
    closeContextMenu();
  };

  // Delete a video from the gallery
  const handleDeleteVideo = async (video: VideoItem) => {
    if (window.confirm('Delete this video?')) {
      try {
        const result = await (window as any).electronAPI.deleteVideoFromGallery({
          filename: video.filename,
          collectionName: activeCollection
        });

        if (result.success) {
          // Update local state
          setCollections(prev => prev.map(c =>
            c.name === activeCollection
              ? { ...c, videos: c.videos.filter(v => v.filename !== video.filename) }
              : c
          ));
          console.log('🎬 Video deleted successfully:', video.filename);
        } else {
          throw new Error(result.error || 'Failed to delete video from gallery');
        }
      } catch (error) {
        console.error('Error deleting video:', error);
        alert(`Failed to delete video: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    closeContextMenu();
  };

  // Move video to another collection
  const handleMoveVideo = async (video: VideoItem, targetCollection: string) => {
    if (targetCollection === activeCollection) return;

    try {
      // Add to target collection
      const addResult = await (window as any).electronAPI.addVideoToGallery({
        videoPath: video.url.replace('file://', ''), // Convert back to file path
        collectionName: targetCollection
      });

      if (!addResult.success) {
        throw new Error(addResult.error || 'Failed to add video to target collection');
      }

      // Remove from source collection
      const deleteResult = await (window as any).electronAPI.deleteVideoFromGallery({
        filename: video.filename,
        collectionName: activeCollection
      });

      if (!deleteResult.success) {
        // If delete fails, try to remove the added video to maintain consistency
        await (window as any).electronAPI.deleteVideoFromGallery({
          filename: addResult.filename,
          collectionName: targetCollection
        });
        throw new Error(deleteResult.error || 'Failed to remove video from source collection');
      }

      // Update local state
      setCollections(prev => prev.map(c => {
        if (c.name === activeCollection) {
          return { ...c, videos: c.videos.filter(v => v.filename !== video.filename) };
        } else if (c.name === targetCollection) {
          return { ...c, videos: [{ filename: addResult.filename, url: addResult.url }, ...c.videos] };
        }
        return c;
      }));

      console.log('🎬 Video moved successfully from', activeCollection, 'to', targetCollection);
    } catch (error) {
      console.error('Error moving video:', error);
      alert(`Failed to move video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    closeContextMenu();
  };

  // Clear all videos in active collection
  const clearActiveCollection = async () => {
    if (window.confirm('Clear all videos in this collection?')) {
      try {
        const collection = collections.find(c => c.name === activeCollection);
        if (!collection) return;

        // Delete all videos
        for (const video of collection.videos) {
          await (window as any).electronAPI.deleteVideoFromGallery({
            filename: video.filename,
            collectionName: activeCollection
          });
        }

        // Update local state
        setCollections(prev => prev.map(c => c.name === activeCollection ? { ...c, videos: [] } : c));
        console.log('🎬 Collection cleared successfully:', activeCollection);
      } catch (error) {
        console.error('Error clearing collection:', error);
        alert('Failed to clear collection');
      }
    }
  };



  // Get videos for active collection
  const activeCollectionVideos = collections.find(c => c.name === activeCollection)?.videos || [];

  return (
    <div className="flex flex-col h-full gap-6 p-4">


      {/* Main Content Area */}
      <div className="flex-1 flex flex-col lg:flex-row gap-6">
        {/* Settings Panel */}
        <div className={`w-full lg:w-80 rounded-xl p-4 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white shadow text-gray-900'}`}>
          <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Video Generation Settings</h2>

          {/* Input Image Section */}
          <div className="mb-6">
            <h3 className={`text-md font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Input Image</h3>
            <div
              {...getRootProps()}
              className={`h-48 border-2 border-dashed rounded-lg flex items-center justify-center ${
                isDarkMode ? 'border-gray-700 hover:border-gray-600' : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              {inputImageUrl ? (
                <img src={inputImageUrl} alt="Input" className="max-h-full max-w-full object-contain rounded" />
              ) : (
                <div className={`text-center p-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  <ImageIcon className={`mx-auto mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>Drag & drop an image, or click to select</p>
                  <p className={`text-sm mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Supports JPG, PNG, WebP</p>
                </div>
              )}
            </div>
          </div>

          {/* Video Generation Settings */}
          <div className="space-y-4">
            {/* Model Selection */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                Video Generation Model
              </label>
              <div className={`w-full px-3 py-2 rounded-lg border ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}>
                FramePack - AI Image-to-Video Generation
              </div>
            </div>

            {/* Quality Preset */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                Quality Preset
              </label>
              <select
                value={selectedPreset.name}
                onChange={(e) => {
                  const preset = VIDEO_PRESETS.find(p => p.name === e.target.value);
                  if (preset) setSelectedPreset(preset);
                }}
                className={`w-full px-3 py-2 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {VIDEO_PRESETS.map(preset => (
                  <option key={preset.name} value={preset.name}>
                    {preset.name} ({preset.settings.steps} steps, CFG {preset.settings.guidanceScale})
                  </option>
                ))}
              </select>
            </div>

            {/* Duration Setting */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                Duration (seconds)
              </label>
              <input
                type="number"
                min="2"
                max="8"
                step="0.5"
                value={duration}
                onChange={(e) => setDuration(parseFloat(e.target.value))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Recommended: 2-6 seconds for best quality
              </p>
            </div>

            {/* FPS Setting */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                Frame Rate (FPS)
              </label>
              <select
                value={fps}
                onChange={(e) => setFps(parseInt(e.target.value))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value={12}>12 FPS</option>
                <option value={24}>24 FPS</option>
                <option value={30}>30 FPS</option>
              </select>
            </div>

            {/* Advanced Settings Toggle */}
            <div className="mt-4">
              <button
                type="button"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className={`flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600'
                    : 'bg-gray-50 border-gray-300 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <span>Advanced Settings</span>
                <ChevronRight className={`w-4 h-4 transition-transform ${showAdvancedSettings ? 'rotate-90' : ''}`} />
              </button>
            </div>

            {/* Advanced Settings Panel */}
            {showAdvancedSettings && (
              <div className="mt-3 space-y-3 p-3 rounded-lg border border-dashed border-gray-300 dark:border-gray-600">
                {/* Seed Setting */}
                <div>
                  <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                    Seed
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      value={seed}
                      onChange={(e) => setSeed(parseInt(e.target.value) || 42)}
                      className={`flex-1 px-3 py-2 rounded-lg border ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                    <button
                      type="button"
                      onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                      className={`px-3 py-2 rounded-lg border text-sm ${
                        isDarkMode
                          ? 'bg-gray-600 border-gray-500 text-gray-200 hover:bg-gray-500'
                          : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      Random
                    </button>
                  </div>
                </div>

                {/* Negative Prompt */}
                <div>
                  <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                    Negative Prompt
                  </label>
                  <textarea
                    value={negativePrompt}
                    onChange={(e) => setNegativePrompt(e.target.value)}
                    placeholder="What you don't want in the video..."
                    rows={2}
                    className={`w-full p-2 rounded border ${
                      isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>

                {/* Settings Info */}
                <div className={`text-xs p-2 rounded ${isDarkMode ? 'bg-gray-800 text-gray-400' : 'bg-gray-50 text-gray-600'}`}>
                  <strong>Current Settings:</strong> {selectedPreset.name} preset, {duration}s duration, {selectedPreset.settings.steps} steps, CFG {selectedPreset.settings.guidanceScale}
                </div>
              </div>
            )}

          {/* Prompt and Generate */}
          <div className="mt-4 space-y-3">
            <div>
              <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>Prompt</label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe your video..."
                rows={3}
                className={`w-full p-2 rounded border ${
                  isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
            <button
              onClick={handleGenerate}
              disabled={isGenerating || !prompt.trim() || !inputImageFile}
              className={`w-full py-2 rounded-lg font-medium ${
                isGenerating || !prompt.trim() || !inputImageFile
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isGenerating ? `Generating...` : 'Generate Video'}
            </button>
          </div>

          </div>
        </div>

        {/* Video Preview Section */}
        <div className={`flex-1 rounded-xl p-4 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white shadow text-gray-900'}`}>
          <h2 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Video Preview</h2>

          {/* Latent Preview Section - Expandable */}
          {(isGenerating || previewImage) && (
            <div className={`mb-4 rounded-lg border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'}`}>
              <button
                onClick={() => setShowLatentPreview(!showLatentPreview)}
                className={`w-full p-3 text-left flex items-center justify-between ${isDarkMode ? 'hover:bg-gray-600' : 'hover:bg-gray-100'} rounded-lg transition-colors`}
              >
                <span className={`font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                  Real-time Generation Preview
                </span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showLatentPreview ? 'rotate-180' : ''}`} />
              </button>

              {showLatentPreview && (
                <div className="p-4 border-t border-gray-300">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Latent Preview Image */}
                    {previewImage && (
                      <div>
                        <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          Current Generation Step
                        </h4>
                        <div className={`rounded-lg overflow-hidden border ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                          <img
                            src={previewImage}
                            alt="Generation preview"
                            className="w-full h-auto"
                            style={{ maxHeight: '200px', objectFit: 'contain' }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Intermediate Video */}
                    {intermediateVideoPath && (
                      <div>
                        <h4 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          Latest Video Section
                        </h4>
                        <div className={`rounded-lg overflow-hidden border ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                          <video
                            src={intermediateVideoPath}
                            controls
                            autoPlay
                            muted
                            loop
                            className="w-full h-auto"
                            style={{ maxHeight: '200px' }}
                          >
                            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              Your browser does not support the video tag.
                            </p>
                          </video>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Generated Video Section */}
          {generatedVideoUrl || (isGenerating && intermediateVideoPath) ? (
            <div className="h-full flex flex-col">
              <div className="flex-1 flex items-center justify-center p-4">
                <video
                  src={generatedVideoUrl || intermediateVideoPath || ''}
                  controls
                  preload="metadata"
                  playsInline
                  autoPlay={false}
                  className="w-full h-auto max-w-full rounded-lg shadow-lg"
                  style={{
                    minHeight: '250px',
                    maxHeight: '50vh',
                    minWidth: '400px'
                  }}
                  onError={(e) => {
                    console.error('🎬 Video playback error:', e);
                    console.log('🎬 Video URL:', generatedVideoUrl);
                    console.log('🎬 Video element:', e.target);

                    const video = e.target as HTMLVideoElement;
                    console.log('🎬 Video error details:', {
                      error: video.error,
                      networkState: video.networkState,
                      readyState: video.readyState,
                      src: video.src,
                      currentSrc: video.currentSrc
                    });

                    if (video.error) {
                      console.log('🎬 Video error code:', video.error.code);
                      console.log('🎬 Video error message:', video.error.message);

                      // Log specific error types
                      switch (video.error.code) {
                        case 1:
                          console.log('🎬 MEDIA_ERR_ABORTED: Video loading was aborted');
                          break;
                        case 2:
                          console.log('🎬 MEDIA_ERR_NETWORK: Network error occurred');
                          break;
                        case 3:
                          console.log('🎬 MEDIA_ERR_DECODE: Video decoding error (codec issue)');
                          break;
                        case 4:
                          console.log('🎬 MEDIA_ERR_SRC_NOT_SUPPORTED: Video format not supported');
                          break;
                      }
                    }

                    // Prevent infinite retry loops
                    if (!video.dataset.retryCount) {
                      video.dataset.retryCount = '0';
                    }

                    const retryCount = parseInt(video.dataset.retryCount);
                    if (retryCount < 2) {
                      video.dataset.retryCount = (retryCount + 1).toString();
                      console.log(`Video retry attempt ${retryCount + 1}/2`);

                      setTimeout(() => {
                        video.load();
                      }, 1000);
                    } else {
                      console.error('Video failed to load after 2 retries. Giving up.');
                      // Reset retry count for future attempts
                      video.dataset.retryCount = '0';
                    }
                  }}
                  onLoadStart={() => {
                    console.log('🎬 Video loading started');
                    console.log('📁 Current video src:', generatedVideoUrl);
                    console.log('📁 Video src attribute:', (document.querySelector('video') as HTMLVideoElement)?.src);

                    // Test if the file URL is accessible
                    if (generatedVideoUrl) {
                      console.log('🔍 Testing file accessibility...');
                      fetch(generatedVideoUrl)
                        .then(response => {
                          console.log('✅ File fetch successful:', response.status, response.statusText);
                          console.log('📊 Content-Type:', response.headers.get('content-type'));
                          console.log('📏 Content-Length:', response.headers.get('content-length'));
                        })
                        .catch(error => {
                          console.error('❌ File fetch failed:', error);
                          console.log('🔍 This suggests file:// protocol access issues');
                        });
                    }
                  }}
                  onCanPlay={() => {
                    console.log('✅ Video can play - SUCCESS!');
                    const video = document.querySelector('video') as HTMLVideoElement;
                    if (video) {
                      console.log('📊 Video ready state:', video.readyState);
                      console.log('🌐 Video network state:', video.networkState);
                      console.log('📏 Video dimensions:', video.videoWidth, 'x', video.videoHeight);
                      console.log('⏱️ Video duration:', video.duration);
                      console.log('📦 Video file size (approx):', video.buffered.length > 0 ? 'Has buffered data' : 'No buffered data');
                    }
                  }}
                  onCanPlayThrough={() => {
                    console.log('✅ Video can play through - READY TO PLAY!');
                  }}
                  onLoadedData={() => {
                    console.log('📥 Video data loaded');
                    const video = document.querySelector('video') as HTMLVideoElement;
                    if (video) {
                      console.log('⏱️ Video duration:', video.duration);
                      console.log('📊 Video buffered ranges:', video.buffered.length);
                      for (let i = 0; i < video.buffered.length; i++) {
                        console.log(`📊 Buffered range ${i}:`, video.buffered.start(i), '-', video.buffered.end(i));
                      }
                    }
                  }}
                  onLoadedMetadata={() => {
                    console.log('📋 Video metadata loaded');
                    const video = document.querySelector('video') as HTMLVideoElement;
                    if (video) {
                      console.log('📏 Video dimensions:', video.videoWidth, 'x', video.videoHeight);
                      console.log('⏱️ Video duration:', video.duration);
                      console.log('🎞️ Video has video tracks:', video.videoTracks?.length || 'Unknown');
                      console.log('🔊 Video has audio tracks:', video.audioTracks?.length || 'Unknown');
                    }
                  }}
                  onPlay={() => console.log('▶️ Video started playing')}
                  onPause={() => console.log('⏸️ Video paused')}
                  onEnded={() => console.log('🏁 Video ended')}
                  onTimeUpdate={() => {
                    const video = document.querySelector('video') as HTMLVideoElement;
                    if (video && video.currentTime > 0) {
                      console.log('⏰ Video time update:', video.currentTime, '/', video.duration);
                    }
                  }}
                  onWaiting={() => console.log('⏳ Video waiting for data')}
                  onStalled={() => console.log('🚫 Video stalled')}
                  onSuspend={() => console.log('⏸️ Video suspended')}
                  onAbort={() => console.log('❌ Video aborted')}
                  onEmptied={() => console.log('🗑️ Video emptied')}
                >
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Your browser does not support the video tag.
                    <a href={generatedVideoUrl} download className="text-blue-500 hover:underline ml-1">
                      Download the video instead
                    </a>
                  </p>
                </video>
              </div>
              <div className="mt-4 text-center">
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {generatedVideoUrl
                    ? "Video generated successfully! Use the controls to play, pause, and adjust volume."
                    : "Showing latest generated section - video will update in real-time as generation progresses."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className={`text-center ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                <Play className="mx-auto mb-4 w-16 h-16" />
                <p className="text-lg font-medium mb-2">No video generated yet</p>
                <p className="text-sm">Upload an image and click generate to create a video</p>
              </div>
            </div>
          )}

          {/* Progress Bar positioned above bottom edge */}
          {isGenerating && (
            <div className="w-full mt-2 mb-4">
              <VideoGenerationProgressBar
                isDarkMode={isDarkMode}
                currentStage={currentStage}
                progress={progress}
                progressText={progressText || generationProgress}
                currentStep={currentStep}
                totalSteps={totalSteps}
                stageProgress={stageProgress}
              />
            </div>
          )}
        </div>

        {/* Right Sidebar: Video Gallery */}
        <div className={`w-full lg:w-80 rounded-xl p-4 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white shadow text-gray-900'}`}>
          <div className="flex items-center justify-between mb-4">
            <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Video Gallery</h2>
          </div>

          {/* Collection Selector */}
          <div className="mb-4">
            <div className="flex gap-2">
              <select
                value={activeCollection}
                onChange={e => setActiveCollection(e.target.value)}
                className={`flex-1 px-2 py-1 rounded ${isDarkMode ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-700'}`}
              >
                {collections.map(c => (
                  <option key={c.name} value={c.name}>{c.name}</option>
                ))}
              </select>
              <button
                onClick={handleCreateCollection}
                className={`px-2 py-1 rounded text-xs font-semibold ${isDarkMode ? 'bg-blue-700 text-white' : 'bg-blue-100 text-blue-700'}`}
                title="Create new collection"
                type="button"
              >
                + New
              </button>
              <button
                onClick={clearActiveCollection}
                className={`px-2 py-1 rounded text-xs font-semibold ${isDarkMode ? 'bg-red-700 text-white' : 'bg-red-100 text-red-700'}`}
                title="Clear all videos in this collection"
                type="button"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Collection Info */}
          <div className={`text-xs mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {activeCollectionVideos.length} videos
          </div>

          {/* Video Thumbnails */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {activeCollectionVideos.length === 0 ? (
              <div className={`text-center py-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                <Play className="mx-auto mb-2 w-8 h-8" />
                <p>No videos in this collection</p>
                <p className="text-sm">Generate videos to see them here</p>
              </div>
            ) : (
              activeCollectionVideos.map((video, idx) => (
                <div
                  key={idx}
                  className={`relative group cursor-pointer rounded border transition-all duration-200 hover:shadow-lg ${
                    isDarkMode ? 'border-gray-600 hover:border-purple-400' : 'border-gray-200 hover:border-purple-500'
                  }`}
                  onClick={() => {
                    console.log('Gallery video clicked:', video.url);
                    setGeneratedVideoUrl(video.url);
                  }}
                  onContextMenu={(e) => handleContextMenu(e, idx, video)}
                >
                  <div className="aspect-video relative overflow-hidden rounded">
                    <video
                      src={video.url}
                      className="w-full h-full object-cover"
                      muted
                      preload="metadata"
                      onError={(e) => {
                        console.error('🎬 Gallery video error:', e);
                        console.log('🎬 Gallery video URL:', video.url);

                        const videoEl = e.target as HTMLVideoElement;
                        if (videoEl.error) {
                          console.log('🎬 Gallery video error code:', videoEl.error.code);
                          console.log('🎬 Gallery video error message:', videoEl.error.message);
                        }
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                      <Play className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </div>
                  </div>
                  <div className="p-2">
                    <p className={`text-xs truncate ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {video.filename}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {contextMenu && (
        <>
          <div className="fixed inset-0 z-40" onClick={closeContextMenu} />
          <div
            className={`fixed z-50 min-w-48 rounded-lg shadow-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
            style={{ left: contextMenu.x, top: contextMenu.y }}
          >
            <button
              onClick={() => handleDownloadVideo(contextMenu.video)}
              className={`w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium rounded-t-lg ${
                isDarkMode ? 'text-gray-200' : 'text-gray-800'
              }`}
            >
              <Download className="inline w-4 h-4 mr-2" />
              Save Video
            </button>

            {/* Move to Collection submenu */}
            {collections.length > 1 && (
              <div className="relative group">
                <button
                  className={`w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-800'
                  }`}
                >
                  <FolderOpen className="inline w-4 h-4 mr-2" />
                  Move to Collection
                  <ChevronRight className="inline w-4 h-4 ml-auto" />
                </button>

                {/* Submenu */}
                <div className={`absolute left-full top-0 min-w-48 rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 ${
                  isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                }`}>
                  {collections
                    .filter(c => c.name !== activeCollection)
                    .map((collection) => (
                      <button
                        key={collection.name}
                        onClick={() => handleMoveVideo(contextMenu.video, collection.name)}
                        className={`w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm ${
                          isDarkMode ? 'text-gray-200' : 'text-gray-800'
                        } first:rounded-t-lg last:rounded-b-lg`}
                      >
                        {collection.name}
                      </button>
                    ))}
                </div>
              </div>
            )}

            <button
              onClick={() => handleDeleteVideo(contextMenu.video)}
              className={`w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm font-medium rounded-b-lg text-red-600 dark:text-red-400`}
            >
              <Trash2 className="inline w-4 h-4 mr-2" />
              Delete Video
            </button>
          </div>
        </>
      )}

      {/* Create Collection Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg max-w-md w-full mx-4 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Create New Collection
            </h3>
            <input
              type="text"
              value={newCollectionName}
              onChange={(e) => setNewCollectionName(e.target.value)}
              placeholder="Collection name"
              className={`w-full p-2 border rounded mb-4 ${
                isDarkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
              }`}
              onKeyPress={(e) => e.key === 'Enter' && confirmCreateCollection()}
            />
            <div className="flex gap-2 justify-end">
              <button
                onClick={cancelCreateCollection}
                className={`px-4 py-2 rounded ${
                  isDarkMode ? 'bg-gray-600 hover:bg-gray-700 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={confirmCreateCollection}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoGeneration;
