========================================
AIStudio Real-time Log: main
Started: 2025-08-30T00:49:14.492Z
File: app_main_2025-08-29_19-49-14_001.log
========================================

[2025-08-30T00:49:14.721Z] [INFO] AIStudio application started successfully
[2025-08-30T00:49:14.721Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T00:49:14.753Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T00:49:15.676Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T00:49:29.795Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_19-49-29_001.log
[2025-08-30T00:49:29.795Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T00:49:32.168Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T00:49:32.169Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T00:49:33.177Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T00:50:39.100Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_python installation_2025-08-29_19-50-39_001.log
[2025-08-30T00:50:39.101Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting python installation for ImageGeneration
[2025-08-30T00:50:39.102Z] [INFO] [dependency_imagegeneration] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-08-30T00:50:39.102Z] [INFO] [dependency_imagegeneration] DependencyManager: Component type: string, Component value: 'python'
[2025-08-30T00:50:39.103Z] [INFO] [dependency_imagegeneration] DependencyManager: Name type: string, Name value: 'all'
[2025-08-30T00:50:39.103Z] [INFO] [dependency_imagegeneration] DependencyManager: About to check routing for ImageGeneration with component python
[2025-08-30T00:50:39.104Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_bundled installation_2025-08-29_19-50-39_001.log
[2025-08-30T00:50:39.105Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:50:39.105Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:50:39.106Z] [INFO] [dependency_imagegeneration] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:50:39.106Z] [INFO] [dependency_imagegeneration] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:50:39.106Z] [INFO] [dependency_imagegeneration] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:50:39.107Z] [INFO] [dependency_imagegeneration] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:50:39.116Z] [INFO] [dependency_imagegeneration] DependencyManager: Existing directory removed successfully
[2025-08-30T00:50:39.117Z] [INFO] [dependency_imagegeneration] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:50:39.118Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
[2025-08-30T00:50:39.119Z] [INFO] [dependency_imagegeneration] DependencyManager: Failed to extract zip file: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:50:39.121Z] [INFO] [dependency_imagegeneration] DependencyManager: Stack: TypeError [ERR_INVALID_ARG_TYPE]: The "cb" argument must be of type function. Received undefined
    at makeCallback (node:fs:185:3)
    at mkdir (node:fs:1337:14)
    at t.mkdir (node:electron/js2c/node_init:2:16026)
    at DependencyManager._installImageGenerationModule (N:\AIStudio\src\main\dependencyManager.js:5797:18)
    at async DependencyManager.installDependencies (N:\AIStudio\src\main\dependencyManager.js:677:14)
    at async N:\AIStudio\src\main\index.js:1939:5
    at async WebContents.<anonymous> (node:electron/js2c/browser_init:2:83724)
[2025-08-30T00:50:39.122Z] [INFO] [dependency_imagegeneration] DependencyManager: Error during installation: Failed to extract ImageGeneration module: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:50:39.122Z] [INFO] [dependency_imagegeneration] DependencyManager: Failed bundled installation for ImageGeneration
[2025-08-30T00:50:40.143Z] [INFO] [RealtimeLogger] Closed log stream: dependency_imagegeneration
