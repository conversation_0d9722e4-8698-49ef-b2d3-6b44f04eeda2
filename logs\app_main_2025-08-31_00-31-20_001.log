========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:31:20.870Z
File: app_main_2025-08-31_00-31-20_001.log
========================================

[2025-08-31T05:31:21.108Z] [INFO] AIStudio application started successfully
[2025-08-31T05:31:21.108Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:31:21.142Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:31:22.076Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:31:39.583Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-31-39_001.log
[2025-08-31T05:31:39.583Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:33:18.383Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-33-18_001.log
[2025-08-31T05:33:18.383Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:33:18.384Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:33:18.384Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:33:18.385Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:33:18.385Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:33:18.387Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-33-18_001.log
[2025-08-31T05:33:18.387Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
