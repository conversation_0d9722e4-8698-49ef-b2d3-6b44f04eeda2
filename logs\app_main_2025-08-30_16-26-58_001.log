========================================
AIStudio Real-time Log: main
Started: 2025-08-30T21:26:58.280Z
File: app_main_2025-08-30_16-26-58_001.log
========================================

[2025-08-30T21:26:58.501Z] [INFO] AIStudio application started successfully
[2025-08-30T21:26:58.501Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T21:26:58.535Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T21:26:59.463Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T21:27:13.573Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_16-27-13_001.log
[2025-08-30T21:27:13.574Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T21:27:23.843Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T21:27:23.844Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T21:27:24.854Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
