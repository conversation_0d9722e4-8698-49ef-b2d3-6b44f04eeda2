========================================
AIStudio Real-time Log: main
Started: 2025-09-01T04:16:51.794Z
File: app_main_2025-08-31_23-16-51_001.log
========================================

[2025-09-01T04:16:52.015Z] [INFO] AIStudio application started successfully
[2025-09-01T04:16:52.015Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T04:16:52.048Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T04:16:53.034Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-01T04:16:53.035Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                   20116 Console                    1 48,039,264 K
[2025-09-01T04:16:53.430Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-01T04:17:27.476Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_23-17-27_001.log
[2025-09-01T04:17:27.477Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T04:17:50.408Z] [INFO] [RealtimeLogger] Started logging Starting server... to: Starting server..._2025-08-31_23-17-50_001.log
[2025-09-01T04:17:50.409Z] [INFO] [Starting server...] video-server
[2025-09-01T04:17:50.410Z] [INFO] [RealtimeLogger] Started logging RUN_BAT: to: RUN_BAT:_2025-08-31_23-17-50_001.log
[2025-09-01T04:17:50.411Z] [INFO] [RUN_BAT:] video-server
[2025-09-01T04:17:50.412Z] [INFO] [RealtimeLogger] Started logging Batch file exists: to: Batch file exists:_2025-08-31_23-17-50_001.log
[2025-09-01T04:17:50.412Z] [INFO] [Batch file exists:] video-server
[2025-09-01T04:17:50.414Z] [INFO] [RealtimeLogger] Started logging Working directory: to: Working directory:_2025-08-31_23-17-50_001.log
[2025-09-01T04:17:50.414Z] [INFO] [Working directory:] video-server
[2025-09-01T04:17:50.415Z] [INFO] [RealtimeLogger] Started logging Command: cmd.exe /c to: Command: cmd.exe /c_2025-08-31_23-17-50_001.log
[2025-09-01T04:17:50.416Z] [INFO] [Command: cmd.exe /c] video-server
[2025-09-01T04:17:50.475Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_23-17-50_001.log'
[2025-09-01T04:17:50.475Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_23-17-50_001.log'
[2025-09-01T04:17:50.476Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_23-17-50_001.log'
[2025-09-01T04:18:05.559Z] [INFO] [RealtimeLogger] Started logging Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn'] to: Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']_2025-08-31_23-18-05_001.log
[2025-09-01T04:18:05.559Z] [INFO] [Currently enabled native sdp backends: ['flash', 'math', 'mem_efficient', 'cudnn']] video-server
[2025-09-01T04:18:05.565Z] [INFO] [RealtimeLogger] Started logging Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed! to: Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!_2025-08-31_23-18-05_001.log
[2025-09-01T04:18:05.565Z] [INFO] [Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!] video-server
[2025-09-01T04:18:05.614Z] [INFO] [RealtimeLogger] Started logging Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True) to: Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)_2025-08-31_23-18-05_001.log
[2025-09-01T04:18:05.614Z] [INFO] [Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)] video-server
[2025-09-01T04:18:05.782Z] [INFO] [RealtimeLogger] Started logging Free VRAM 10.9814453125 GBHigh-VRAM Mode: False to: Free VRAM 10.9814453125 GBHigh-VRAM Mode: False_2025-08-31_23-18-05_001.log
[2025-09-01T04:18:05.783Z] [INFO] [Free VRAM 10.9814453125 GBHigh-VRAM Mode: False] video-server
[2025-09-01T04:18:06.536Z] [INFO] [RealtimeLogger] Started logging Loading model: 0% to: Loading model: 0%_2025-08-31_23-18-06_001.log
[2025-09-01T04:18:06.536Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T04:18:06.539Z] [INFO] [RealtimeLogger] Started logging Loading model: 100% to: Loading model: 100%_2025-08-31_23-18-06_001.log
[2025-09-01T04:18:06.539Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T04:18:06.812Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T04:18:07.021Z] [INFO] [RealtimeLogger] Started logging Loading model: 25% to: Loading model: 25%_2025-08-31_23-18-07_001.log
[2025-09-01T04:18:07.021Z] [INFO] [Loading model: 25%] video-server
[2025-09-01T04:18:07.249Z] [INFO] [RealtimeLogger] Started logging Loading model: 50% to: Loading model: 50%_2025-08-31_23-18-07_001.log
[2025-09-01T04:18:07.249Z] [INFO] [Loading model: 50%] video-server
[2025-09-01T04:18:07.433Z] [INFO] [RealtimeLogger] Started logging Loading model: 75% to: Loading model: 75%_2025-08-31_23-18-07_001.log
[2025-09-01T04:18:07.433Z] [INFO] [Loading model: 75%] video-server
[2025-09-01T04:18:07.602Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T04:18:12.754Z] [INFO] [RealtimeLogger] Started logging Downloading: 0% to: Downloading: 0%_2025-08-31_23-18-12_001.log
[2025-09-01T04:18:12.754Z] [INFO] [Downloading: 0%] video-server
[2025-09-01T04:18:12.756Z] [INFO] [RealtimeLogger] Started logging Downloading: 100% to: Downloading: 100%_2025-08-31_23-18-12_001.log
[2025-09-01T04:18:12.756Z] [INFO] [Downloading: 100%] video-server
[2025-09-01T04:18:12.916Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T04:18:13.078Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T04:18:13.107Z] [INFO] [RealtimeLogger] Started logging transformer.high_quality_fp32_output_for_inference = True to: transformer.high_quality_fp32_output_for_inference = True_2025-08-31_23-18-13_001.log
[2025-09-01T04:18:13.107Z] [INFO] [transformer.high_quality_fp32_output_for_inference = True] video-server
[2025-09-01T04:18:13.799Z] [INFO] [RealtimeLogger] Started logging * Running on local URL: http://127.0.0.1:7860 to: * Running on local URL: http://127.0.0.1:7860_2025-08-31_23-18-13_001.log
[2025-09-01T04:18:13.799Z] [INFO] [* Running on local URL: http://127.0.0.1:7860] video-server
[2025-09-01T04:18:13.800Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\* Running on local URL: http:\127.0.0.1:7860_2025-08-31_23-18-13_001.log'
[2025-09-01T04:18:13.800Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\* Running on local URL: http:\127.0.0.1:7860_2025-08-31_23-18-13_001.log'
[2025-09-01T04:18:13.801Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\* Running on local URL: http:\127.0.0.1:7860_2025-08-31_23-18-13_001.log'
[2025-09-01T04:18:13.856Z] [INFO] [RealtimeLogger] Started logging To create a public link, set `share=True` in `launch()`. to: To create a public link, set `share=True` in `launch()`._2025-08-31_23-18-13_001.log
[2025-09-01T04:18:13.857Z] [INFO] [To create a public link, set `share=True` in `launch()`.] video-server
