========================================
AIStudio Real-time Log: main
Started: 2025-08-31T01:57:48.469Z
File: app_main_2025-08-30_20-57-48_001.log
========================================

[2025-08-31T01:57:48.756Z] [INFO] AIStudio application started successfully
[2025-08-31T01:57:48.756Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T01:57:48.792Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T01:57:49.681Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T01:58:15.496Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_20-58-15_001.log
[2025-08-31T01:58:15.496Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T01:58:46.030Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_20-58-46_001.log
[2025-08-31T01:58:46.030Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:58:46.032Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:58:46.032Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:58:46.033Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:58:46.034Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T01:58:46.035Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_20-58-46_001.log
[2025-08-31T01:58:46.036Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
