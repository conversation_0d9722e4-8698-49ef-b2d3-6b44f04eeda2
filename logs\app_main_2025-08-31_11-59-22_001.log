========================================
AIStudio Real-time Log: main
Started: 2025-08-31T16:59:22.742Z
File: app_main_2025-08-31_11-59-22_001.log
========================================

[2025-08-31T16:59:22.956Z] [INFO] AIStudio application started successfully
[2025-08-31T16:59:22.956Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T16:59:22.990Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T16:59:24.007Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T16:59:37.598Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_11-59-37_001.log
[2025-08-31T16:59:37.599Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T17:08:10.596Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_12-08-10_001.log
[2025-08-31T17:08:10.597Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:08:10.597Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:08:10.598Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:08:10.598Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:08:10.599Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T17:08:10.600Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_12-08-10_001.log
[2025-08-31T17:08:10.600Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
