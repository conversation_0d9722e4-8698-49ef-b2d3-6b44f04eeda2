========================================
AIStudio Real-time Log: main
Started: 2025-08-30T00:14:57.286Z
File: app_main_2025-08-29_19-14-57_001.log
========================================

[2025-08-30T00:14:57.526Z] [INFO] AIStudio application started successfully
[2025-08-30T00:14:57.527Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T00:14:57.567Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T00:14:58.567Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T00:15:22.263Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_19-15-22_001.log
[2025-08-30T00:15:22.264Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T00:15:24.601Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T00:15:24.602Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T00:15:25.604Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T00:15:59.542Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_python installation_2025-08-29_19-15-59_001.log
[2025-08-30T00:15:59.543Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting python installation for ImageGeneration
[2025-08-30T00:15:59.544Z] [INFO] [dependency_imagegeneration] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-08-30T00:15:59.544Z] [INFO] [dependency_imagegeneration] DependencyManager: Component type: string, Component value: 'python'
[2025-08-30T00:15:59.545Z] [INFO] [dependency_imagegeneration] DependencyManager: Name type: string, Name value: 'all'
[2025-08-30T00:15:59.545Z] [INFO] [dependency_imagegeneration] DependencyManager: About to check routing for ImageGeneration with component python
[2025-08-30T00:15:59.547Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_bundled installation_2025-08-29_19-15-59_001.log
[2025-08-30T00:15:59.547Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:15:59.548Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:15:59.548Z] [INFO] [dependency_imagegeneration] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:15:59.549Z] [INFO] [dependency_imagegeneration] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:15:59.549Z] [INFO] [dependency_imagegeneration] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:15:59.550Z] [INFO] [dependency_imagegeneration] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:15:59.559Z] [INFO] [dependency_imagegeneration] DependencyManager: Existing directory removed successfully
[2025-08-30T00:15:59.560Z] [INFO] [dependency_imagegeneration] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:15:59.561Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
[2025-08-30T00:15:59.733Z] [INFO] [dependency_imagegeneration] DependencyManager: Failed to extract zip file: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:15:59.733Z] [INFO] [dependency_imagegeneration] DependencyManager: Error during installation: Failed to extract ImageGeneration module: The "cb" argument must be of type function. Received undefined
[2025-08-30T00:15:59.734Z] [INFO] [dependency_imagegeneration] DependencyManager: Failed bundled installation for ImageGeneration
[2025-08-30T00:16:00.766Z] [INFO] [RealtimeLogger] Closed log stream: dependency_imagegeneration
