========================================
AIStudio Real-time Log: main
Started: 2025-08-30T04:32:56.427Z
File: app_main_2025-08-29_23-32-56_001.log
========================================

[2025-08-30T04:32:56.693Z] [INFO] AIStudio application started successfully
[2025-08-30T04:32:56.693Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T04:32:56.728Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T04:32:57.506Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-08-30T04:32:57.507Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                    5164 Console                    1 12,688,200 K
[2025-08-30T04:32:57.887Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-08-30T04:34:11.371Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_23-34-11_001.log
[2025-08-30T04:34:11.371Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T04:34:14.368Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T04:34:14.370Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T04:34:15.375Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
