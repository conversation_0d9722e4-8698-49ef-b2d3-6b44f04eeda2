import sys
import os
import json
import base64
import traceback
from pathlib import Path
from PIL import Image
import numpy as np

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

try:
    from video_generator import HunyuanVideoGenerator
except ImportError as e:
    print(json.dumps({"error": f"Failed to import video_generator: {str(e)}"}))
    sys.exit(1)

def progress_callback_factory():
    """Factory function to create a progress callback that outputs JSON."""
    def progress_callback(percentage, message, preview=None):
        # Ensure percentage is a number
        if isinstance(percentage, (int, float)):
            percentage = max(0, min(100, int(percentage)))
        else:
            percentage = 0

        progress_data = {
            "type": "progress",
            "percentage": percentage,
            "message": str(message),
            "timestamp": __import__('time').time()
        }

        # Add preview data if available
        if preview is not None:
            try:
                # Handle enhanced preview data (dict) or simple numpy array
                if isinstance(preview, dict):
                    # Enhanced preview data with additional info
                    if "preview" in preview and isinstance(preview["preview"], np.ndarray):
                        preview_image = preview["preview"]
                        if preview_image.dtype != np.uint8:
                            preview_image = (preview_image * 255).astype(np.uint8)

                        pil_image = Image.fromarray(preview_image)

                        # Add additional data
                        if "video_path" in preview:
                            progress_data["video_path"] = preview["video_path"]
                        if "total_frames" in preview:
                            progress_data["total_frames"] = preview["total_frames"]
                        if "video_length" in preview:
                            progress_data["video_length"] = preview["video_length"]
                        if "step" in preview:
                            progress_data["step"] = preview["step"]
                        if "total_steps" in preview:
                            progress_data["total_steps"] = preview["total_steps"]
                        if "section" in preview:
                            progress_data["section"] = preview["section"]
                        if "total_sections" in preview:
                            progress_data["total_sections"] = preview["total_sections"]
                    else:
                        pil_image = None
                elif isinstance(preview, np.ndarray):
                    # Simple numpy array preview
                    if preview.dtype != np.uint8:
                        preview = (preview * 255).astype(np.uint8)

                    pil_image = Image.fromarray(preview)
                else:
                    pil_image = None

                # Convert to base64 if we have a valid PIL image
                if pil_image is not None:
                    import io
                    buffer = io.BytesIO()
                    pil_image.save(buffer, format='PNG')
                    preview_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    progress_data["preview"] = f"data:image/png;base64,{preview_base64}"
            except Exception as e:
                # If preview conversion fails, just skip it
                progress_data["preview_error"] = str(e)
        
        # Output as JSON line
        print(json.dumps(progress_data), flush=True)

        # Also print to stderr for debugging
        import sys
        print(f"[DEBUG] Progress: {percentage}% - {message}", file=sys.stderr, flush=True)

    return progress_callback

def generate_framepack_video(image_path, prompt, output_path, pipeline_dir=None, **settings):
    """
    Generate a video using the Framepack pipeline.
    
    Args:
        image_path: Path to input image
        prompt: Text prompt for video generation
        output_path: Path where the output video should be saved
        pipeline_dir: Directory containing the pipeline (optional)
        **settings: Additional settings for video generation
    
    Returns:
        dict: Result containing success status and output path or error
    """
    try:
        # Print start message
        print(json.dumps({
            "type": "start",
            "message": "Initializing Framepack video generation...",
            "image_path": image_path,
            "prompt": prompt,
            "settings": settings
        }), flush=True)
        
        # Validate input image
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Input image not found: {image_path}")
        
        # Load input image
        try:
            input_image = np.array(Image.open(image_path).convert("RGB"))
        except Exception as e:
            raise ValueError(f"Failed to load input image: {str(e)}")
        
        # Create output directory
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Extract settings with defaults
        seed = settings.get('seed', 42)
        steps = settings.get('steps', 50)
        duration = settings.get('duration', 4.0)
        cfg = settings.get('cfg', 3.0)
        gs = settings.get('gs', 1.0)
        rs = settings.get('rs', 0.0)
        latent_window_size = settings.get('latent_window_size', 16)
        gpu_memory_preservation = settings.get('gpu_memory_preservation', 8.0)
        use_teacache = settings.get('use_teacache', True)
        mp4_crf = settings.get('mp4_crf', 18)
        negative_prompt = settings.get('negative_prompt', '')
        device = settings.get('device', 'cuda')
        high_vram = settings.get('high_vram', None)
        
        # Initialize the generator with memory management
        print(json.dumps({
            "type": "progress",
            "percentage": 5,
            "message": "Initializing video generator..."
        }), flush=True)

        # Force garbage collection and clear CUDA cache before initialization
        import gc
        gc.collect()

        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                # Check available memory
                free_memory = torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)
                print(json.dumps({
                    "type": "progress",
                    "percentage": 6,
                    "message": f"Available GPU memory: {free_memory / 1024**3:.1f} GB"
                }), flush=True)
        except Exception as e:
            print(json.dumps({
                "type": "progress",
                "percentage": 6,
                "message": f"Memory check warning: {str(e)}"
            }), flush=True)

        generator = HunyuanVideoGenerator(
            output_dir=str(output_dir),
            device=device,
            high_vram=high_vram
        )
        
        # Load models
        print(json.dumps({
            "type": "progress", 
            "percentage": 10,
            "message": "Loading AI models..."
        }), flush=True)
        
        generator.load_models()
        
        # Create progress callback
        progress_cb = progress_callback_factory()
        
        # Generate video
        print(json.dumps({
            "type": "progress",
            "percentage": 15,
            "message": "Starting video generation..."
        }), flush=True)
        
        # Wrap video generation with memory monitoring
        try:
            final_output_path = generator.generate_video(
                input_image=input_image,
                prompt=prompt,
                n_prompt=negative_prompt,
                seed=seed,
                total_second_length=duration,  # Map duration to total_second_length
                latent_window_size=latent_window_size,
                steps=steps,
                cfg=cfg,
                gs=gs,
                rs=rs,
                gpu_memory_preservation=gpu_memory_preservation,
                use_teacache=use_teacache,
                mp4_crf=mp4_crf,
                progress_callback=progress_cb
            )
        except Exception as generation_error:
            # Handle generation errors gracefully
            error_msg = str(generation_error)
            print(json.dumps({
                "type": "error",
                "error": f"Video generation failed: {error_msg}",
                "traceback": __import__('traceback').format_exc()
            }), flush=True)
            raise generation_error
        
        # Copy to desired output path if different
        if final_output_path != output_path:
            import shutil
            shutil.copy2(final_output_path, output_path)
            final_output_path = output_path
        
        # Success result
        result = {
            "type": "complete",
            "success": True,
            "output_path": final_output_path,
            "message": "Video generation completed successfully!"
        }
        
        print(json.dumps(result), flush=True)
        return result
        
    except Exception as e:
        error_result = {
            "type": "error",
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }
        print(json.dumps(error_result), flush=True)
        return error_result
    
    finally:
        # Clean up
        try:
            if 'generator' in locals():
                print(json.dumps({
                    "type": "progress",
                    "percentage": 95,
                    "message": "Cleaning up models..."
                }), flush=True)
                generator.unload_models()

            # Force garbage collection and clear CUDA cache
            import gc
            gc.collect()

            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
            except:
                pass

        except Exception as cleanup_error:
            print(json.dumps({
                "type": "progress",
                "percentage": 95,
                "message": f"Cleanup warning: {str(cleanup_error)}"
            }), flush=True)

if __name__ == "__main__":
    # Command line interface for testing
    if len(sys.argv) < 4:
        print(json.dumps({
            "type": "error",
            "error": "Usage: python framepack_integration.py <image_path> <prompt> <output_path> [settings_json]"
        }))
        sys.exit(1)
    
    image_path = sys.argv[1]
    prompt = sys.argv[2]
    output_path = sys.argv[3]
    settings = {}
    
    if len(sys.argv) > 4:
        try:
            settings = json.loads(sys.argv[4])
        except json.JSONDecodeError as e:
            print(json.dumps({
                "type": "error",
                "error": f"Invalid settings JSON: {str(e)}"
            }))
            sys.exit(1)
    
    result = generate_framepack_video(image_path, prompt, output_path, **settings)
    
    if not result.get("success", False):
        sys.exit(1)
