========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:40:44.207Z
File: app_main_2025-08-31_00-40-44_001.log
========================================

[2025-08-31T05:40:44.428Z] [INFO] AIStudio application started successfully
[2025-08-31T05:40:44.428Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:40:44.468Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:40:45.405Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:41:01.644Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-41-01_001.log
[2025-08-31T05:41:01.644Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:41:29.597Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-41-29_001.log
[2025-08-31T05:41:29.597Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:41:29.598Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:41:29.599Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:41:29.599Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:41:29.600Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:41:29.601Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-41-29_001.log
[2025-08-31T05:41:29.601Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
