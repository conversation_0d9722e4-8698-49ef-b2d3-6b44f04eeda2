========================================
AIStudio Real-time Log: main
Started: 2025-09-01T03:20:15.864Z
File: app_main_2025-08-31_22-20-15_001.log
========================================

[2025-09-01T03:20:16.129Z] [INFO] AIStudio application started successfully
[2025-09-01T03:20:16.130Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T03:20:16.157Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T03:20:17.155Z] [INFO] [34m[Trellis Server][39m Found Python processes, killing them...
[2025-09-01T03:20:17.156Z] [INFO] [34m[Trellis Server][39m Processes found: python.exe                   24020 Console                    1  2,220,944 K
[2025-09-01T03:20:17.596Z] [INFO] [34m[Trellis Server][39m Successfully killed Python processes
[2025-09-01T03:20:39.669Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_22-20-39_001.log
[2025-09-01T03:20:39.669Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T03:21:01.717Z] [INFO] [RealtimeLogger] Started logging Starting server... to: Starting server..._2025-08-31_22-21-01_001.log
[2025-09-01T03:21:01.717Z] [INFO] [Starting server...] video-server
[2025-09-01T03:21:01.719Z] [INFO] [RealtimeLogger] Started logging RUN_BAT: to: RUN_BAT:_2025-08-31_22-21-01_001.log
[2025-09-01T03:21:01.719Z] [INFO] [RUN_BAT:] video-server
[2025-09-01T03:21:01.720Z] [INFO] [RealtimeLogger] Started logging Batch file exists: to: Batch file exists:_2025-08-31_22-21-01_001.log
[2025-09-01T03:21:01.721Z] [INFO] [Batch file exists:] video-server
[2025-09-01T03:21:01.722Z] [INFO] [RealtimeLogger] Started logging Working directory: to: Working directory:_2025-08-31_22-21-01_001.log
[2025-09-01T03:21:01.722Z] [INFO] [Working directory:] video-server
[2025-09-01T03:21:01.723Z] [INFO] [RealtimeLogger] Started logging Command: cmd.exe /c to: Command: cmd.exe /c_2025-08-31_22-21-01_001.log
[2025-09-01T03:21:01.724Z] [INFO] [Command: cmd.exe /c] video-server
[2025-09-01T03:21:01.766Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_22-21-01_001.log'
[2025-09-01T03:21:01.766Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_22-21-01_001.log'
[2025-09-01T03:21:01.767Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\Command: cmd.exe \c_2025-08-31_22-21-01_001.log'
[2025-09-01T03:21:16.070Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed! to: [STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!_2025-08-31_22-21-16_001.log
[2025-09-01T03:21:16.070Z] [INFO] [[STDOUT] Xformers is not installed!Flash Attn is not installed!Sage Attn is not installed!] video-server
[2025-09-01T03:21:16.121Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True) to: [STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)_2025-08-31_22-21-16_001.log
[2025-09-01T03:21:16.121Z] [INFO] [[STDOUT] Namespace(share=False, server='127.0.0.1', port=None, inbrowser=True)] video-server
[2025-09-01T03:21:16.284Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False to: [STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False_2025-08-31_22-21-16_001.log
[2025-09-01T03:21:16.284Z] [INFO] [[STDOUT] Free VRAM 10.9814453125 GBHigh-VRAM Mode: False] video-server
[2025-09-01T03:21:17.007Z] [INFO] [RealtimeLogger] Started logging Loading model: 0% to: Loading model: 0%_2025-08-31_22-21-17_001.log
[2025-09-01T03:21:17.007Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T03:21:17.012Z] [INFO] [RealtimeLogger] Started logging Loading model: 100% to: Loading model: 100%_2025-08-31_22-21-17_001.log
[2025-09-01T03:21:17.012Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T03:21:17.311Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T03:21:17.495Z] [INFO] [RealtimeLogger] Started logging Loading model: 25% to: Loading model: 25%_2025-08-31_22-21-17_001.log
[2025-09-01T03:21:17.495Z] [INFO] [Loading model: 25%] video-server
[2025-09-01T03:21:17.685Z] [INFO] [RealtimeLogger] Started logging Loading model: 50% to: Loading model: 50%_2025-08-31_22-21-17_001.log
[2025-09-01T03:21:17.685Z] [INFO] [Loading model: 50%] video-server
[2025-09-01T03:21:17.843Z] [INFO] [RealtimeLogger] Started logging Loading model: 75% to: Loading model: 75%_2025-08-31_22-21-17_001.log
[2025-09-01T03:21:17.843Z] [INFO] [Loading model: 75%] video-server
[2025-09-01T03:21:18.171Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T03:21:23.613Z] [INFO] [RealtimeLogger] Started logging Downloading: 0% to: Downloading: 0%_2025-08-31_22-21-23_001.log
[2025-09-01T03:21:23.614Z] [INFO] [Downloading: 0%] video-server
[2025-09-01T03:22:01.917Z] [INFO] [RealtimeLogger] Started logging [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message) to: [STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)_2025-08-31_22-22-01_001.log
[2025-09-01T03:22:01.918Z] [INFO] [[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development warnings.warn(message)] video-server
[2025-09-01T03:22:01.919Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_22-22-01_001.log'
[2025-09-01T03:22:01.919Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_22-22-01_001.log'
[2025-09-01T03:22:01.919Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDERR] N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system\python\lib\site-packages\huggingface_hub\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\webui\hf_download\hub\models--lllyasviel--FramePackI2V_HY. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https:\huggingface.co\docs\huggingface_hub\how-to-cache#limitations.To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https:\docs.microsoft.com\en-us\windows\apps\get-started\enable-your-device-for-development warnings.warn(message)_2025-08-31_22-22-01_001.log'
[2025-09-01T03:23:14.868Z] [INFO] [RealtimeLogger] Started logging Downloading: 100% to: Downloading: 100%_2025-08-31_22-23-14_001.log
[2025-09-01T03:23:14.868Z] [INFO] [Downloading: 100%] video-server
[2025-09-01T03:23:15.005Z] [INFO] [Loading model: 0%] video-server
[2025-09-01T03:23:15.169Z] [INFO] [Loading model: 100%] video-server
[2025-09-01T03:23:15.197Z] [INFO] [RealtimeLogger] Started logging [STDOUT] transformer.high_quality_fp32_output_for_inference = True to: [STDOUT] transformer.high_quality_fp32_output_for_inference = True_2025-08-31_22-23-15_001.log
[2025-09-01T03:23:15.197Z] [INFO] [[STDOUT] transformer.high_quality_fp32_output_for_inference = True] video-server
[2025-09-01T03:23:18.292Z] [INFO] [RealtimeLogger] Started logging [STDOUT] * Running on local URL: http://127.0.0.1:7860 to: [STDOUT] * Running on local URL: http://127.0.0.1:7860_2025-08-31_22-23-18_001.log
[2025-09-01T03:23:18.292Z] [INFO] [[STDOUT] * Running on local URL: http://127.0.0.1:7860] video-server
[2025-09-01T03:23:18.293Z] [ERROR] Uncaught Exception: ReferenceError: startupTimeout is not defined
[2025-09-01T03:23:18.293Z] [ERROR] [main] ERROR: Uncaught Exception: ReferenceError: startupTimeout is not defined
[2025-09-01T03:23:18.293Z] [ERROR] Uncaught Exception: ReferenceError: startupTimeout is not defined
[2025-09-01T03:23:18.295Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] * Running on local URL: http:\127.0.0.1:7860_2025-08-31_22-23-18_001.log'
[2025-09-01T03:23:18.295Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] * Running on local URL: http:\127.0.0.1:7860_2025-08-31_22-23-18_001.log'
[2025-09-01T03:23:18.295Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] * Running on local URL: http:\127.0.0.1:7860_2025-08-31_22-23-18_001.log'
[2025-09-01T03:23:18.852Z] [INFO] [RealtimeLogger] Started logging [STDOUT] To create a public link, set `share=True` in `launch()`. to: [STDOUT] To create a public link, set `share=True` in `launch()`._2025-08-31_22-23-18_001.log
[2025-09-01T03:23:18.852Z] [INFO] [[STDOUT] To create a public link, set `share=True` in `launch()`.] video-server
[2025-09-01T03:26:07.259Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Unloaded DynamicSwap_LlamaModel as complete. to: [STDOUT] Unloaded DynamicSwap_LlamaModel as complete._2025-08-31_22-26-07_001.log
[2025-09-01T03:26:07.259Z] [INFO] [[STDOUT] Unloaded DynamicSwap_LlamaModel as complete.] video-server
[2025-09-01T03:26:07.260Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Unloaded CLIPTextModel as complete. to: [STDOUT] Unloaded CLIPTextModel as complete._2025-08-31_22-26-07_001.log
[2025-09-01T03:26:07.261Z] [INFO] [[STDOUT] Unloaded CLIPTextModel as complete.] video-server
[2025-09-01T03:26:07.268Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Unloaded SiglipVisionModel as complete. to: [STDOUT] Unloaded SiglipVisionModel as complete._2025-08-31_22-26-07_001.log
[2025-09-01T03:26:07.269Z] [INFO] [[STDOUT] Unloaded SiglipVisionModel as complete.] video-server
[2025-09-01T03:26:07.272Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete. to: [STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete._2025-08-31_22-26-07_001.log
[2025-09-01T03:26:07.273Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:26:07.302Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Unloaded DynamicSwap_HunyuanVideoTransformer3DModelPacked as complete. to: [STDOUT] Unloaded DynamicSwap_HunyuanVideoTransformer3DModelPacked as complete._2025-08-31_22-26-07_001.log
[2025-09-01T03:26:07.302Z] [INFO] [[STDOUT] Unloaded DynamicSwap_HunyuanVideoTransformer3DModelPacked as complete.] video-server
[2025-09-01T03:26:18.107Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Loaded CLIPTextModel to cuda:0 as complete. to: [STDOUT] Loaded CLIPTextModel to cuda:0 as complete._2025-08-31_22-26-18_001.log
[2025-09-01T03:26:18.108Z] [INFO] [[STDOUT] Loaded CLIPTextModel to cuda:0 as complete.] video-server
[2025-09-01T03:27:20.056Z] [INFO] [[STDOUT] Unloaded CLIPTextModel as complete.] video-server
[2025-09-01T03:27:20.521Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete. to: [STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete._2025-08-31_22-27-20_001.log
[2025-09-01T03:27:20.521Z] [INFO] [[STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete.] video-server
[2025-09-01T03:27:24.702Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:27:25.239Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Loaded SiglipVisionModel to cuda:0 as complete. to: [STDOUT] Loaded SiglipVisionModel to cuda:0 as complete._2025-08-31_22-27-25_001.log
[2025-09-01T03:27:25.239Z] [INFO] [[STDOUT] Loaded SiglipVisionModel to cuda:0 as complete.] video-server
[2025-09-01T03:27:26.620Z] [INFO] [RealtimeLogger] Started logging [STDOUT] latent_padding_size = 27, is_last_section = False to: [STDOUT] latent_padding_size = 27, is_last_section = False_2025-08-31_22-27-26_001.log
[2025-09-01T03:27:26.621Z] [INFO] [[STDOUT] latent_padding_size = 27, is_last_section = False] video-server
[2025-09-01T03:27:27.290Z] [INFO] [[STDOUT] Unloaded SiglipVisionModel as complete.] video-server
[2025-09-01T03:27:27.326Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB to: [STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB_2025-08-31_22-27-27_001.log
[2025-09-01T03:27:27.326Z] [INFO] [[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB] video-server
[2025-09-01T03:27:27.327Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB_2025-08-31_22-27-27_001.log'
[2025-09-01T03:27:27.327Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB_2025-08-31_22-27-27_001.log'
[2025-09-01T03:27:27.328Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB_2025-08-31_22-27-27_001.log'
[2025-09-01T03:27:33.551Z] [INFO] [RealtimeLogger] Started logging Progress: 0% to: Progress: 0%_2025-08-31_22-27-33_001.log
[2025-09-01T03:27:33.551Z] [INFO] [Progress: 0%] video-server
[2025-09-01T03:30:08.247Z] [INFO] [RealtimeLogger] Started logging Progress: 20% to: Progress: 20%_2025-08-31_22-30-08_001.log
[2025-09-01T03:30:08.248Z] [INFO] [Progress: 20%] video-server
[2025-09-01T03:30:42.248Z] [INFO] [RealtimeLogger] Started logging Progress: 40% to: Progress: 40%_2025-08-31_22-30-42_001.log
[2025-09-01T03:30:42.248Z] [INFO] [Progress: 40%] video-server
[2025-09-01T03:31:49.141Z] [INFO] [RealtimeLogger] Started logging Progress: 60% to: Progress: 60%_2025-08-31_22-31-49_001.log
[2025-09-01T03:31:49.141Z] [INFO] [Progress: 60%] video-server
[2025-09-01T03:32:55.602Z] [INFO] [RealtimeLogger] Started logging Progress: 80% to: Progress: 80%_2025-08-31_22-32-55_001.log
[2025-09-01T03:32:55.603Z] [INFO] [Progress: 80%] video-server
[2025-09-01T03:34:33.630Z] [INFO] [RealtimeLogger] Started logging Progress: 100% to: Progress: 100%_2025-08-31_22-34-33_001.log
[2025-09-01T03:34:33.631Z] [INFO] [Progress: 100%] video-server
[2025-09-01T03:34:34.190Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB to: [STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB_2025-08-31_22-34-34_001.log
[2025-09-01T03:34:34.191Z] [INFO] [[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB] video-server
[2025-09-01T03:34:34.192Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB_2025-08-31_22-34-34_001.log'
[2025-09-01T03:34:34.192Z] [ERROR] [main] ERROR: Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB_2025-08-31_22-34-34_001.log'
[2025-09-01T03:34:34.193Z] [ERROR] Uncaught Exception: Error: ENOENT: no such file or directory, open 'N:\AIStudio\logs\[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB_2025-08-31_22-34-34_001.log'
[2025-09-01T03:34:36.813Z] [INFO] [[STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete.] video-server
[2025-09-01T03:35:06.510Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:35:08.747Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 9, 60, 104]); pixel shape torch.Size([1, 3, 33, 480, 832])latent_padding_size = 18, is_last_section = False to: [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 9, 60, 104]); pixel shape torch.Size([1, 3, 33, 480, 832])latent_padding_size = 18, is_last_section = False_2025-08-31_22-35-08_001.log
[2025-09-01T03:35:08.747Z] [INFO] [[STDOUT] Decoded. Current latent shape torch.Size([1, 16, 9, 60, 104]); pixel shape torch.Size([1, 3, 33, 480, 832])latent_padding_size = 18, is_last_section = False] video-server
[2025-09-01T03:35:08.749Z] [INFO] [[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB] video-server
[2025-09-01T03:35:10.964Z] [INFO] [Progress: 0%] video-server
[2025-09-01T03:37:24.293Z] [INFO] [Progress: 20%] video-server
[2025-09-01T03:37:57.371Z] [INFO] [Progress: 40%] video-server
[2025-09-01T03:39:02.865Z] [INFO] [Progress: 60%] video-server
[2025-09-01T03:40:08.383Z] [INFO] [Progress: 80%] video-server
[2025-09-01T03:42:18.770Z] [INFO] [Progress: 100%] video-server
[2025-09-01T03:42:18.779Z] [INFO] [[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB] video-server
[2025-09-01T03:42:20.505Z] [INFO] [[STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete.] video-server
[2025-09-01T03:43:21.819Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:43:24.173Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 18, 60, 104]); pixel shape torch.Size([1, 3, 69, 480, 832])latent_padding_size = 9, is_last_section = False to: [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 18, 60, 104]); pixel shape torch.Size([1, 3, 69, 480, 832])latent_padding_size = 9, is_last_section = False_2025-08-31_22-43-24_001.log
[2025-09-01T03:43:24.174Z] [INFO] [[STDOUT] Decoded. Current latent shape torch.Size([1, 16, 18, 60, 104]); pixel shape torch.Size([1, 3, 69, 480, 832])latent_padding_size = 9, is_last_section = False] video-server
[2025-09-01T03:43:24.176Z] [INFO] [[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB] video-server
[2025-09-01T03:43:26.926Z] [INFO] [Progress: 0%] video-server
[2025-09-01T03:45:37.890Z] [INFO] [Progress: 20%] video-server
[2025-09-01T03:46:10.994Z] [INFO] [Progress: 40%] video-server
[2025-09-01T03:47:16.530Z] [INFO] [Progress: 60%] video-server
[2025-09-01T03:48:22.044Z] [INFO] [Progress: 80%] video-server
[2025-09-01T03:50:32.455Z] [INFO] [Progress: 100%] video-server
[2025-09-01T03:50:32.460Z] [INFO] [[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB] video-server
[2025-09-01T03:50:33.912Z] [INFO] [[STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete.] video-server
[2025-09-01T03:51:34.740Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:51:39.040Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 27, 60, 104]); pixel shape torch.Size([1, 3, 105, 480, 832])latent_padding_size = 0, is_last_section = True to: [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 27, 60, 104]); pixel shape torch.Size([1, 3, 105, 480, 832])latent_padding_size = 0, is_last_section = True_2025-08-31_22-51-39_001.log
[2025-09-01T03:51:39.041Z] [INFO] [[STDOUT] Decoded. Current latent shape torch.Size([1, 16, 27, 60, 104]); pixel shape torch.Size([1, 3, 105, 480, 832])latent_padding_size = 0, is_last_section = True] video-server
[2025-09-01T03:51:39.105Z] [INFO] [[STDOUT] Moving DynamicSwap_HunyuanVideoTransformer3DModelPacked to cuda:0 with preserved memory: 6 GB] video-server
[2025-09-01T03:51:41.688Z] [INFO] [Progress: 0%] video-server
[2025-09-01T03:53:52.040Z] [INFO] [Progress: 20%] video-server
[2025-09-01T03:54:25.087Z] [INFO] [Progress: 40%] video-server
[2025-09-01T03:55:30.555Z] [INFO] [Progress: 60%] video-server
[2025-09-01T03:56:36.022Z] [INFO] [Progress: 80%] video-server
[2025-09-01T03:58:46.279Z] [INFO] [Progress: 100%] video-server
[2025-09-01T03:58:46.289Z] [INFO] [[STDOUT] Offloading DynamicSwap_HunyuanVideoTransformer3DModelPacked from cuda:0 to preserve memory: 8 GB] video-server
[2025-09-01T03:58:47.682Z] [INFO] [[STDOUT] Loaded AutoencoderKLHunyuanVideo to cuda:0 as complete.] video-server
[2025-09-01T03:59:51.378Z] [INFO] [[STDOUT] Unloaded AutoencoderKLHunyuanVideo as complete.] video-server
[2025-09-01T03:59:56.858Z] [INFO] [RealtimeLogger] Started logging [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 37, 60, 104]); pixel shape torch.Size([1, 3, 145, 480, 832]) to: [STDOUT] Decoded. Current latent shape torch.Size([1, 16, 37, 60, 104]); pixel shape torch.Size([1, 3, 145, 480, 832])_2025-08-31_22-59-56_001.log
[2025-09-01T03:59:56.858Z] [INFO] [[STDOUT] Decoded. Current latent shape torch.Size([1, 16, 37, 60, 104]); pixel shape torch.Size([1, 3, 145, 480, 832])] video-server
