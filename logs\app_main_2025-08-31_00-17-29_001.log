========================================
AIStudio Real-time Log: main
Started: 2025-08-31T05:17:29.802Z
File: app_main_2025-08-31_00-17-29_001.log
========================================

[2025-08-31T05:17:30.036Z] [INFO] AIStudio application started successfully
[2025-08-31T05:17:30.036Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T05:17:30.070Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T05:17:30.981Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T05:17:47.981Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_00-17-47_001.log
[2025-08-31T05:17:47.981Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T05:18:18.574Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_00-18-18_001.log
[2025-08-31T05:18:18.575Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T05:18:18.575Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T05:18:18.576Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T05:18:18.576Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T05:18:18.577Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T05:18:18.578Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_00-18-18_001.log
[2025-08-31T05:18:18.578Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
