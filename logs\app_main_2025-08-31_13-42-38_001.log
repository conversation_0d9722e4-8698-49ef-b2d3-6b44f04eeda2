========================================
AIStudio Real-time Log: main
Started: 2025-08-31T18:42:38.611Z
File: app_main_2025-08-31_13-42-38_001.log
========================================

[2025-08-31T18:42:38.859Z] [INFO] AIStudio application started successfully
[2025-08-31T18:42:38.859Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T18:42:38.894Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T18:42:39.836Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T18:42:54.093Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_13-42-54_001.log
[2025-08-31T18:42:54.094Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T18:45:33.813Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_13-45-33_001.log
[2025-08-31T18:45:33.814Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T18:45:33.814Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T18:45:33.815Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T18:45:33.815Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T18:45:33.816Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T18:45:33.817Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_13-45-33_001.log
[2025-08-31T18:45:33.817Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
