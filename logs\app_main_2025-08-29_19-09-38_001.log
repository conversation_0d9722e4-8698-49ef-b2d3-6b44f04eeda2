========================================
AIStudio Real-time Log: main
Started: 2025-08-30T00:09:38.631Z
File: app_main_2025-08-29_19-09-38_001.log
========================================

[2025-08-30T00:09:38.856Z] [INFO] AIStudio application started successfully
[2025-08-30T00:09:38.857Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T00:09:38.889Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T00:09:39.812Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T00:09:53.920Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-29_19-09-53_001.log
[2025-08-30T00:09:53.921Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-30T00:09:56.399Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-08-30T00:09:56.400Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-08-30T00:09:57.402Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-08-30T00:10:48.455Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_python installation_2025-08-29_19-10-48_001.log
[2025-08-30T00:10:48.455Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting python installation for ImageGeneration
[2025-08-30T00:10:48.456Z] [INFO] [dependency_imagegeneration] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-08-30T00:10:48.456Z] [INFO] [dependency_imagegeneration] DependencyManager: Component type: string, Component value: 'python'
[2025-08-30T00:10:48.457Z] [INFO] [dependency_imagegeneration] DependencyManager: Name type: string, Name value: 'all'
[2025-08-30T00:10:48.457Z] [INFO] [dependency_imagegeneration] DependencyManager: About to check routing for ImageGeneration with component python
[2025-08-30T00:10:48.459Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_bundled installation_2025-08-29_19-10-48_001.log
[2025-08-30T00:10:48.459Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting bundled installation for ImageGeneration
[2025-08-30T00:10:48.459Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting portable installation of ImageGeneration module
[2025-08-30T00:10:48.460Z] [INFO] [dependency_imagegeneration] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:10:48.460Z] [INFO] [dependency_imagegeneration] DependencyManager: Extraction Target: N:\AIStudio\pipelines
[2025-08-30T00:10:48.461Z] [INFO] [dependency_imagegeneration] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:10:48.462Z] [INFO] [dependency_imagegeneration] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-08-30T00:10:48.472Z] [INFO] [dependency_imagegeneration] DependencyManager: Existing directory removed successfully
[2025-08-30T00:10:48.473Z] [INFO] [dependency_imagegeneration] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-08-30T00:10:48.474Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
