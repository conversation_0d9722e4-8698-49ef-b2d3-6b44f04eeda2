========================================
AIStudio Real-time Log: main
Started: 2025-08-31T17:10:02.503Z
File: app_main_2025-08-31_12-10-02_001.log
========================================

[2025-08-31T17:10:02.726Z] [INFO] AIStudio application started successfully
[2025-08-31T17:10:02.726Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T17:10:02.762Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T17:10:03.783Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T17:10:19.322Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_12-10-19_001.log
[2025-08-31T17:10:19.323Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T17:14:17.436Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_12-14-17_001.log
[2025-08-31T17:14:17.436Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T17:14:17.437Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T17:14:17.438Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T17:14:17.438Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T17:14:17.439Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T17:14:17.440Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_12-14-17_001.log
[2025-08-31T17:14:17.440Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
