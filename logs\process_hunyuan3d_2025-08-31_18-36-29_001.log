========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-31T23:36:29.035Z
File: process_hunyuan3d_2025-08-31_18-36-29_001.log
========================================

[2025-08-31T23:36:29.034Z] [STDOUT] N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-31T23:36:29.037Z] [STDOUT] "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
"N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-31T23:36:34.345Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-31T23:36:34.346Z] [STDOUT] "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-08-31T23:36:49.010Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 12334ms
  creator CPython3Windows(dest=N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.2, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-08-31T23:36:49.114Z] [STDOUT] 1 file(s) copied.
[2025-08-31T23:36:49.401Z] [STDOUT] Portable Python located at: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-08-31T23:36:49.401Z] [STDOUT] Virtual environment Python set to: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-31T23:36:49.404Z] [STDOUT] _
[2025-08-31T23:36:49.406Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-31T23:36:49.407Z] [STDOUT] Virtual Env: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-31T23:36:49.408Z] [STDOUT] _
[2025-08-31T23:36:49.426Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-31T23:36:49.427Z] [STDOUT] Virtual Env: N:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-31T23:36:49.428Z] [STDOUT] Starting the server, please wait...
[2025-08-31T23:40:29.062Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-08-31T23:40:29.064Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<?, ?it/s]
[2025-08-31T23:40:29.209Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-08-31T23:40:29.211Z] [STDERR] Fetching 17 files: 100%|##########| 17/17 [00:00<?, ?it/s]
[2025-08-31T23:40:30.631Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-31T23:40:32.103Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:01<00:07,  1.47s/it]
[2025-08-31T23:40:32.394Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:01<00:01,  2.26it/s]
[2025-08-31T23:40:43.513Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:12<00:00,  3.76s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:12<00:00,  2.15s/it]
[2025-08-31T23:40:49.011Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-31T23:40:49.982Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:00<00:00,  3.76it/s]
[2025-08-31T23:40:55.253Z] [STDERR] Loading pipeline components...:  67%|######6   | 4/6 [00:06<00:03,  2.00s/it]
[2025-08-31T23:40:55.379Z] [STDERR] Loading pipeline components...:  83%|########3 | 5/6 [00:06<00:01,  1.38s/it]
[2025-08-31T23:41:17.807Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:28<00:00,  8.13s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:28<00:00,  4.80s/it]
[2025-08-31T23:41:52.062Z] [STDERR] 2025-08-31 18:41:52,062 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0-turbo
[2025-08-31T23:41:52.063Z] [STDERR] 2025-08-31 18:41:52,062 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-31T23:41:52.165Z] [STDERR] Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
[2025-08-31T23:41:52.166Z] [STDERR] Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]
[2025-08-31T23:41:52.175Z] [STDERR] 2025-08-31 18:41:52,176 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2\snapshots\0798d3451a19a46491e9ba8ed9b935274ad70c9f\hunyuan3d-dit-v2-0-turbo\model.fp16.safetensors
[2025-08-31T23:42:56.796Z] [STDERR] 2025-08-31 18:42:56,796 - hy3dgen.shapgen - INFO - Try to load model from local path: C:\Users\<USER>\tencent/Hunyuan3D-2\hunyuan3d-vae-v2-0-turbo
2025-08-31 18:42:56,796 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-31T23:42:56.913Z] [STDERR] Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|##########| 3/3 [00:00<?, ?it/s]
[2025-08-31T23:42:56.919Z] [STDERR] 2025-08-31 18:42:56,930 - hy3dgen.shapgen - INFO - Loading model from C:\Users\<USER>\.cache\huggingface\hub\models--tencent--Hunyuan3D-2\snapshots\0798d3451a19a46491e9ba8ed9b935274ad70c9f\hunyuan3d-vae-v2-0-turbo\model.fp16.safetensors
[2025-08-31T23:43:03.427Z] [STDERR] INFO:     Started server process [41440]
INFO:     Waiting for application startup.
[2025-08-31T23:43:03.429Z] [STDERR] INFO:     Application startup complete.
[2025-08-31T23:43:03.437Z] [STDERR] INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
[2025-08-31T23:43:03.558Z] [STDOUT] After launched, open a browser and enter 0.0.0.0:8080 into url, as if it was a website:
INFO:     127.0.0.1:62603 - "GET / HTTP/1.1" 200 OK
[2025-08-31T23:43:03.943Z] [STDOUT] INFO:     127.0.0.1:62603 - "POST /upload HTTP/1.1" 200 OK
[2025-08-31T23:43:18.547Z] [STDOUT] Created new folder: gradio_cache\4e5c63bc-24aa-4d8d-9204-7e534acb0e25
[2025-08-31T23:43:18.548Z] [STDERR] Diffusion Sampling::   0%|          | 0/25 [00:00<?, ?it/s]
[2025-08-31T23:43:21.805Z] [STDERR] Diffusion Sampling::  20%|##        | 5/25 [00:03<00:11,  1.77it/s]
[2025-08-31T23:43:24.428Z] [STDERR] Diffusion Sampling::  40%|####      | 10/25 [00:05<00:07,  1.89it/s]
[2025-08-31T23:43:27.050Z] [STDERR] Diffusion Sampling::  60%|######    | 15/25 [00:08<00:05,  1.90it/s]
[2025-08-31T23:43:29.669Z] [STDERR] Diffusion Sampling::  80%|########  | 20/25 [00:11<00:02,  1.91it/s]
[2025-08-31T23:43:32.301Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:13<00:00,  1.90it/s]
Diffusion Sampling:: 100%|##########| 25/25 [00:13<00:00,  1.82it/s]
[2025-08-31T23:43:32.656Z] [STDERR] 2025-08-31 18:43:32,663 - hy3dgen.shapgen - INFO - FlashVDMVolumeDecoding Resolution: [63, 126, 252]
[2025-08-31T23:43:33.253Z] [STDERR] FlashVDM Volume Decoding:   0%|          | 0/64 [00:00<?, ?it/s]
[2025-08-31T23:43:35.364Z] [STDERR] FlashVDM Volume Decoding: 100%|##########| 64/64 [00:02<00:00, 30.49it/s]
[2025-08-31T23:43:48.172Z] [STDERR] 2025-08-31 18:43:48,180 - hy3dgen.shapgen - INFO - ---Shape generation takes 36.65005683898926 seconds ---
[2025-08-31T23:43:56.602Z] [STDERR] 2025-08-31 18:43:56,613 - hy3dgen.shapgen - INFO - ---Postprocessing takes 4.750009059906006 seconds ---
[2025-08-31T23:44:22.384Z] [STDERR] 2025-08-31 18:44:22,384 - hy3dgen.shapgen - INFO - ---Face Reduction takes 25.770581483840942 seconds ---
[2025-08-31T23:46:55.161Z] [STDERR] 2025-08-31 18:46:55,161 - hy3dgen.shapgen - INFO - ---Texture Generation takes 152.7766523361206 seconds ---
[2025-08-31T23:46:57.920Z] [STDOUT] Find html file gradio_cache\4e5c63bc-24aa-4d8d-9204-7e534acb0e25\textured_mesh.html, True, relative HTML path is /static/4e5c63bc-24aa-4d8d-9204-7e534acb0e25\textured_mesh.html
INFO:     127.0.0.1:62604 - "POST /api/predict HTTP/1.1" 200 OK
[2025-08-31T23:46:57.958Z] [STDOUT] INFO:     127.0.0.1:62721 - "GET /file%3DC%3A/Users/<USER>/AppData/Local/Temp/gradio/dcec55a1a04e8b6c7b70aa19dcceefd92a7c8d050c2fff53cda64c4a7bdbfb35/textured_mesh.glb HTTP/1.1" 200 OK
[2025-08-31T23:47:04.413Z] [STDOUT] Something went wrong, consider removing code/hunyuan_init_done.txt and the venv folder to re-initialize from scratch
[2025-08-31T23:47:04.416Z] [STDOUT] Press any key to continue . . .
