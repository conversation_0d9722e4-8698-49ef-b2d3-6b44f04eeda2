========================================
AIStudio Real-time Log: main
Started: 2025-08-30T22:04:41.167Z
File: app_main_2025-08-30_17-04-41_001.log
========================================

[2025-08-30T22:04:41.433Z] [INFO] AIStudio application started successfully
[2025-08-30T22:04:41.433Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T22:04:41.469Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T22:04:42.445Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T22:05:03.105Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_17-05-03_001.log
[2025-08-30T22:05:03.105Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
