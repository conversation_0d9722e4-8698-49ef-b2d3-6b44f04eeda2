========================================
AIStudio Real-time Log: main
Started: 2025-08-30T23:42:20.591Z
File: app_main_2025-08-30_18-42-20_001.log
========================================

[2025-08-30T23:42:20.862Z] [INFO] AIStudio application started successfully
[2025-08-30T23:42:20.862Z] [INFO] [main] AIStudio application started successfully
[2025-08-30T23:42:20.897Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-30T23:42:21.827Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-30T23:42:38.787Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_18-42-38_001.log
[2025-08-30T23:42:38.788Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
