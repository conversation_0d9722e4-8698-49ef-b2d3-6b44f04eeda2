========================================
AIStudio Real-time Log: main
Started: 2025-08-31T18:21:05.801Z
File: app_main_2025-08-31_13-21-05_001.log
========================================

[2025-08-31T18:21:06.077Z] [INFO] AIStudio application started successfully
[2025-08-31T18:21:06.077Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T18:21:06.114Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T18:21:07.127Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T18:21:27.783Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_13-21-27_001.log
[2025-08-31T18:21:27.784Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T18:39:25.360Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-31_13-39-25_001.log
[2025-08-31T18:39:25.360Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T18:39:25.361Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T18:39:25.361Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T18:39:25.362Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T18:39:25.362Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T18:39:25.364Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-31_13-39-25_001.log
[2025-08-31T18:39:25.364Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
[2025-08-31T18:39:52.194Z] [ERROR] Error occurred in handler for 'clean-video-generation-install-with-progress': TypeError: dependencyManager._installVideoGenerationModule is not a function
