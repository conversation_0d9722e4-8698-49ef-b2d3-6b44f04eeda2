========================================
AIStudio Real-time Log: main
Started: 2025-08-31T00:40:39.278Z
File: app_main_2025-08-30_19-40-39_001.log
========================================

[2025-08-31T00:40:39.534Z] [INFO] AIStudio application started successfully
[2025-08-31T00:40:39.534Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T00:40:39.569Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T00:40:40.538Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T00:40:58.899Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_19-40-58_001.log
[2025-08-31T00:40:58.899Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
