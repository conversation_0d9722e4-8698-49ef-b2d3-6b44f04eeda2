========================================
AIStudio Real-time Log: main
Started: 2025-09-01T01:34:14.085Z
File: app_main_2025-08-31_20-34-14_001.log
========================================

[2025-09-01T01:34:14.356Z] [INFO] AIStudio application started successfully
[2025-09-01T01:34:14.356Z] [INFO] [main] AIStudio application started successfully
[2025-09-01T01:34:14.392Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-01T01:34:15.277Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-01T01:34:40.468Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-31_20-34-40_001.log
[2025-09-01T01:34:40.468Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-01T01:35:10.424Z] [INFO] [34m[Video Server][39m Starting server...
[2025-09-01T01:35:10.425Z] [INFO] [34m[Video Server][39m RUN_BAT: N:\AIStudio\src\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-01T01:35:10.426Z] [INFO] [34m[Video Server][39m Batch file exists: false
[2025-09-01T01:35:10.430Z] [ERROR] Framepack video generation failed: Error: Video generation run.bat not found at: N:\AIStudio\src\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
