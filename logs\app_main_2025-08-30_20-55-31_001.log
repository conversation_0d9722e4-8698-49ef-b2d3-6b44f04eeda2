========================================
AIStudio Real-time Log: main
Started: 2025-08-31T01:55:31.605Z
File: app_main_2025-08-30_20-55-31_001.log
========================================

[2025-08-31T01:55:31.867Z] [INFO] AIStudio application started successfully
[2025-08-31T01:55:31.868Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T01:55:31.907Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T01:55:32.855Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T01:55:45.594Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_20-55-45_001.log
[2025-08-31T01:55:45.594Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T01:56:19.157Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_20-56-19_001.log
[2025-08-31T01:56:19.158Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T01:56:19.158Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T01:56:19.159Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T01:56:19.159Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T01:56:19.160Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T01:56:19.161Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_20-56-19_001.log
[2025-08-31T01:56:19.161Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
