========================================
AIStudio Real-time Log: main
Started: 2025-08-31T02:00:55.731Z
File: app_main_2025-08-30_21-00-55_001.log
========================================

[2025-08-31T02:00:55.986Z] [INFO] AIStudio application started successfully
[2025-08-31T02:00:55.986Z] [INFO] [main] AIStudio application started successfully
[2025-08-31T02:00:56.024Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-08-31T02:00:56.927Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-08-31T02:01:09.819Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-08-30_21-01-09_001.log
[2025-08-31T02:01:09.820Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-08-31T02:01:43.666Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-08-30_21-01-43_001.log
[2025-08-31T02:01:43.667Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-08-31T02:01:43.668Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-08-31T02:01:43.668Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-08-31T02:01:43.669Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-08-31T02:01:43.669Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-08-31T02:01:43.670Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_module installation_2025-08-30_21-01-43_001.log
[2025-08-31T02:01:43.670Z] [INFO] [dependency_video_generation] DependencyManager: Starting module installation for Video Generation
